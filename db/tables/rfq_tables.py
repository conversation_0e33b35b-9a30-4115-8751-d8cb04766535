from datetime import datetime
from uuid import uuid4

from sqlalchemy import (
    Boolean,
    Column,
    DateTime,
    Float,
    ForeignKey,
    String,
    Text,
)
from sqlalchemy.dialects.postgresql import JSONB, UUID

from db.tables.base import Base


class RFQInstructionSchema(Base):
    """Versioned instruction schemas and rules for all agents."""

    __tablename__ = "rfq_instruction_schemas"

    id = Column(UUID, primary_key=True, default=uuid4)
    client_id = Column(UUID, ForeignKey("rfq_clients.client_id"), nullable=True, comment="NULL for global/generic schemas")
    name = Column(String(100), nullable=False, comment="Human-readable name of the schema/rule")
    type = Column(String(50), nullable=False, comment="Type: extraction_rules, extraction_schema, validation_rules, etc.")
    agent_prefix = Column(String(10), nullable=False, comment="Agent prefix: e. for extractor, v. for validator, n. for normalizer, f. for formatter")
    version = Column(String(20), nullable=False, comment="Semantic version: e.1.0.0, v.1.1.0, n.2.0.0, etc.")
    content = Column(JSONB, nullable=False, comment="The actual schema or rules JSON content")
    description = Column(Text, nullable=True, comment="Description of changes in this version")
    is_active = Column(Boolean, default=True, nullable=False, comment="Whether this version is active")
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


# Client management tables
class RFQClient(Base):
    """Client table for RFQ processor multi-tenant setup."""

    __tablename__ = "rfq_clients"

    client_id = Column(UUID, primary_key=True, default=uuid4)
    name = Column(String(100), nullable=False)
    client_code = Column(String(100), nullable=False)
    status = Column(String(20), default="active", nullable=False)

    # Boolean flags for conditional loading
    has_kb = Column(Boolean, default=False, nullable=False, comment="Whether client has custom knowledge base")
    has_instruction = Column(Boolean, default=False, nullable=False, comment="Whether client has custom instructions")
    has_output_preference = Column(Boolean, default=False, nullable=False, comment="Whether client has custom output preferences")

    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class ClientConfiguration(Base):
    """Client configuration for RFQ processing with UUID references to versioned schemas."""

    __tablename__ = "rfq_client_configurations"

    config_id = Column(UUID, primary_key=True, default=uuid4)
    client_id = Column(UUID, ForeignKey("rfq_clients.client_id"), nullable=False)
    active_versions = Column(JSONB, nullable=False, comment="JSON tracking active versions for each component type")

    # UUID references to rfq_instruction_schemas table
    extraction_output_schema_id = Column(
        UUID, ForeignKey("rfq_instruction_schemas.id"), nullable=True, comment="Reference to extraction output schema"
    )
    validation_output_schema_id = Column(
        UUID, ForeignKey("rfq_instruction_schemas.id"), nullable=True, comment="Reference to validation output schema"
    )
    normalizer_output_schema_id = Column(
        UUID, ForeignKey("rfq_instruction_schemas.id"), nullable=True, comment="Reference to normalizer output schema"
    )
    extraction_rules_id = Column(UUID, ForeignKey("rfq_instruction_schemas.id"), nullable=True, comment="Reference to extraction rules")
    validation_rules_id = Column(UUID, ForeignKey("rfq_instruction_schemas.id"), nullable=True, comment="Reference to validation rules")
    normalization_rules_id = Column(UUID, ForeignKey("rfq_instruction_schemas.id"), nullable=True, comment="Reference to normalization rules")
    formatter_rules_id = Column(UUID, ForeignKey("rfq_instruction_schemas.id"), nullable=True, comment="Reference to formatter rules")
    formatter_output_config_id = Column(UUID, ForeignKey("rfq_instruction_schemas.id"), nullable=True, comment="Reference to formatter output config")
    formatter_output_schema_id = Column(UUID, ForeignKey("rfq_instruction_schemas.id"), nullable=True, comment="Reference to formatter output schema")

    # Keep model_preferences as JSONB for now (not versioned)
    model_preferences = Column(JSONB, nullable=True, comment="Agent-specific model preferences and settings")

    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


# Reference data tables
class CatalogType(Base):
    """Types of catalogs for reference data."""

    __tablename__ = "rfq_catalog_types"

    catalog_type_id = Column(UUID, primary_key=True, default=uuid4)
    name = Column(String(50), unique=True, nullable=False)
    description = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)


class ReferenceData(Base):
    """Reference data entries for RFQ validation."""

    __tablename__ = "rfq_reference_data"

    reference_id = Column(UUID, primary_key=True, default=uuid4)
    client_id = Column(UUID, ForeignKey("rfq_clients.client_id"), nullable=True)  # NULL for global entries
    catalog_type_id = Column(UUID, ForeignKey("rfq_catalog_types.catalog_type_id"), nullable=False)
    code = Column(String(100), nullable=False)
    name = Column(String(200), nullable=True)
    description = Column(Text, nullable=True)
    properties = Column(JSONB, nullable=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    __table_args__ = (
        # Unique constraint for (client_id, catalog_type_id, code)
        # None for client_id represents global entries
        {"sqlite_autoincrement": True},
    )


# Grade-coating compatibility table
class GradeCoatingCompatibility(Base):
    """Compatibility table for grades and coatings."""

    __tablename__ = "rfq_grade_coating_compatibility"

    compatibility_id = Column(UUID, primary_key=True, default=uuid4)
    client_id = Column(UUID, ForeignKey("rfq_clients.client_id"), nullable=True)  # NULL for global entries
    grade_code = Column(String(100), nullable=False)
    coating_code = Column(String(100), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)


# Processing history
class ProcessingHistory(Base):
    """History of RFQ processing requests and results."""

    __tablename__ = "rfq_processing_history"

    history_id = Column(UUID, primary_key=True, default=uuid4)
    client_id = Column(UUID, ForeignKey("rfq_clients.client_id"), nullable=False)
    request_id = Column(String(100), nullable=False)
    email_id = Column(String(100), nullable=True)
    input_text = Column(Text, nullable=False)
    output_json = Column(JSONB, nullable=False)
    processing_time = Column(Float, nullable=False)  # in seconds
    created_at = Column(DateTime, default=datetime.utcnow)
