import logging
from os import getenv
from typing import Any
from urllib.parse import quote_plus

from pydantic_settings import BaseSettings, SettingsConfigDict

logger = logging.getLogger(__name__)


class DbSettings(BaseSettings):
    """
    Database settings that can be set using environment variables.

    Reference: https://docs.pydantic.dev/latest/usage/pydantic_settings/
    """

    # Database configuration
    db_host: str | None = None
    db_port: int | None = None
    db_user: str | None = None
    db_pass: str | None = None
    db_database: str | None = None
    db_driver: str = "postgresql+psycopg"

    # Connection pooling settings
    db_pool_size: int = 5  # Default for dev/staging
    db_max_overflow: int = 10
    db_pool_timeout: int = 30
    db_pool_recycle: int = 1800  # 30 minutes

    # SSL settings
    db_use_ssl: bool = False
    db_ssl_ca: str | None = None
    db_ssl_cert: str | None = None
    db_ssl_key: str | None = None

    # Create/Upgrade database on startup using alembic
    migrate_db: bool = False

    # Environment-specific configurations
    model_config = SettingsConfigDict(env_file=".env", env_file_encoding="utf-8", extra="ignore")

    def get_environment_config(self) -> dict[str, Any]:
        """Get environment-specific database configuration"""
        runtime_env = getenv("RUNTIME_ENV", "dev")

        configs = {
            "dev": {
                "pool_size": 5,
                "max_overflow": 10,
                "use_ssl": False,
            },
            "stg": {
                "pool_size": 10,
                "max_overflow": 20,
                "use_ssl": False,
            },
            "prd": {
                "pool_size": 20,
                "max_overflow": 30,
                "use_ssl": True,
            },
        }
        return configs.get(runtime_env, {})

    def is_unix_socket(self) -> bool:
        """Check if the database host is a Unix socket path (Cloud SQL)"""
        if not self.db_host:
            return False

        # Check for Cloud SQL socket paths (both Cloud Run and local testing)
        socket_indicators = ["/cloudsql/", "/tmp/cloudsql/", "/app/cloudsql/", ".s.PGSQL."]

        return any(indicator in self.db_host for indicator in socket_indicators)

    def get_db_url(self) -> str:
        """
        Build and return the database connection URL.
        FIXED: Proper handling of Cloud SQL Unix sockets.
        """
        runtime_env = getenv("RUNTIME_ENV", "dev")

        # Check if all required environment variables are set
        missing_vars = []
        if not self.db_host:
            missing_vars.append("DB_HOST")
        if not self.db_port:
            missing_vars.append("DB_PORT")
        if not self.db_user:
            missing_vars.append("DB_USER")
        if not self.db_database:
            missing_vars.append("DB_DATABASE")

        if missing_vars:
            error_msg = f"Missing required database environment variables: {', '.join(missing_vars)}"
            logger.error(error_msg)

            # Only attempt fallback for local development outside containers
            if runtime_env == "dev" and getenv("RUNTIME_ENV") is None:
                logger.warning("Attempting fallback to local database configuration...")
                try:
                    from workspace.dev_resources import dev_db

                    local_db_url = dev_db.get_db_connection_local()
                    if local_db_url and "None" not in local_db_url:
                        logger.info("Using local database connection")
                        return local_db_url
                except Exception as e:
                    logger.error(f"Failed to get local database connection: {e}")

            raise ValueError(f"Could not build database connection. {error_msg}")

        # Type safety: ensure required fields are not None after validation
        assert self.db_host is not None
        assert self.db_user is not None
        assert self.db_database is not None

        # Handle Cloud SQL Unix socket connections
        if self.is_unix_socket():
            logger.info(f"Using Cloud SQL Unix socket connection: {self.db_host}")

            # For Unix sockets, construct URL with host parameter
            password_part = f":{quote_plus(self.db_pass)}" if self.db_pass else ""
            db_url = f"{self.db_driver}://{self.db_user}{password_part}@/{self.db_database}?host={quote_plus(self.db_host)}"

            logger.info(f"Cloud SQL socket URL: {self.db_driver}://<user>@/{self.db_database}?host={self.db_host}")
        else:
            # Traditional TCP connection
            logger.info(f"Using TCP connection: {self.db_host}:{self.db_port}")

            password_part = f":{quote_plus(self.db_pass)}" if self.db_pass else ""
            db_url = f"{self.db_driver}://{self.db_user}{password_part}@{self.db_host}:{self.db_port}/{self.db_database}"

            logger.info(f"TCP connection URL: {self.db_driver}://<user>@{self.db_host}:{self.db_port}/{self.db_database}")

        # Validate that the URL doesn't contain None values
        if "None" in db_url:
            raise ValueError(f"Database URL contains None values: {db_url}")

        return db_url

    def get_ssl_config(self) -> dict[str, Any]:
        """Get SSL configuration for database connection"""
        runtime_env = getenv("RUNTIME_ENV", "dev")

        # For Cloud SQL Unix sockets, SSL is handled by the proxy
        if self.is_unix_socket():
            logger.info("Using Cloud SQL Unix socket - SSL handled by Cloud SQL proxy")
            return {}

        if runtime_env in ("stg", "prd") or self.db_use_ssl:
            ssl_config = {"sslmode": "require"}

            # Add SSL certificate paths if provided
            if self.db_ssl_ca:
                ssl_config["sslrootcert"] = self.db_ssl_ca
            if self.db_ssl_cert:
                ssl_config["sslcert"] = self.db_ssl_cert
            if self.db_ssl_key:
                ssl_config["sslkey"] = self.db_ssl_key

            return ssl_config

        return {}

    def validate_environment(self) -> list[str]:
        """
        Validate that all required environment variables are properly set.

        Returns:
            List[str]: List of validation errors, empty if all valid
        """
        errors: list[str] = []
        runtime_env = getenv("RUNTIME_ENV", "dev")

        # Skip validation in test environment
        if getenv("TESTING") == "True":
            return errors

        # Required variables for all environments
        required_vars = {
            "DB_HOST": self.db_host,
            "DB_PORT": self.db_port,
            "DB_USER": self.db_user,
            "DB_DATABASE": self.db_database,
        }

        # Check required variables
        for var_name, var_value in required_vars.items():
            if not var_value:
                errors.append(f"Missing required environment variable: {var_name}")

        # Production-specific validations
        if runtime_env == "prd":
            if not self.is_unix_socket() and not self.db_use_ssl:
                errors.append("SSL must be enabled in production environment (unless using Cloud SQL)")
            if self.db_pool_size < 10:
                errors.append("Database pool size should be at least 10 in production")

        return errors

    def log_configuration_status(self) -> None:
        """
        Log the current database configuration status.
        """
        runtime_env = getenv("RUNTIME_ENV", "dev")
        connection_type = "Cloud SQL Unix Socket" if self.is_unix_socket() else "TCP"

        logger.info(f"Database configuration initialized for {runtime_env} environment")
        logger.info(f"Connection type: {connection_type}")
        logger.info(f"Connection pool size: {self.db_pool_size}")
        logger.info(f"SSL enabled: {self.db_use_ssl}")
        logger.info(f"Connection timeout: {self.db_pool_timeout}s")
        logger.info(f"Connection recycle: {self.db_pool_recycle}s")

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

        # Apply environment-specific configurations
        env_config = self.get_environment_config()

        # Override defaults with environment-specific values if not explicitly set
        if "pool_size" in env_config and not kwargs.get("db_pool_size"):
            self.db_pool_size = env_config["pool_size"]
        if "max_overflow" in env_config and not kwargs.get("db_max_overflow"):
            self.db_max_overflow = env_config["max_overflow"]
        if "use_ssl" in env_config and not kwargs.get("db_use_ssl"):
            self.db_use_ssl = env_config["use_ssl"]

        # Validate environment configuration
        validation_errors = self.validate_environment()
        if validation_errors:
            for error in validation_errors:
                logger.error(error)
            if getenv("RUNTIME_ENV") in ["stg", "prd"]:
                # Fail fast in production/staging
                raise ValueError(f"Environment validation failed: {', '.join(validation_errors)}")
            else:
                # Just warn in development
                logger.warning("Environment validation warnings detected")

        # Log configuration status
        self.log_configuration_status()


# Create DbSettings object
db_settings = DbSettings()
