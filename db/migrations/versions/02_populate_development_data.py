"""Populate development data with clients, configurations, and reference materials

Revision ID: 02_populate_dev_data
Revises: 01_create_schema
Create Date: 2025-07-01 22:00:00.000000

"""

import os
import uuid
from datetime import datetime
from typing import Any, cast

import sqlalchemy as sa
from alembic import op
from sqlalchemy import DateTime, String, Text
from sqlalchemy.dialects.postgresql import JSON<PERSON>, UUID
from sqlalchemy.sql import column, table

from db.reference_data import ALL_REFERENCE_DATA
from utils.rfq.config import get_default_client_id
from utils.rfq.rules import extraction_rules, formatter_rules, normalizer_rules, validation_rules
from utils.rfq.schemas import categorical_schema, extraction_schema, formatter_schema, numerical_schema

# Import form criteria to avoid duplication with Migration 04
try:
    from utils.rfq.rules.form_criteria import FORM_DIMENSIONAL_CRITERIA
except ImportError:
    print("⚠️  Warning: Could not import form criteria, using empty dict")
    FORM_DIMENSIONAL_CRITERIA = {}

# revision identifiers, used by Alembic.
# --- FIX: Shortened revision ID and updated down_revision link ---
revision = "02_populate_dev_data"
down_revision = "01_create_schema"
branch_labels = None
depends_on = None

# Load schemas and reference data
DEFAULT_EXTRACTION_OUTPUT_SCHEMA = extraction_schema.EXTRACTION_AGENT_OUTPUT_SCHEMA
DEFAULT_EXTRACTION_RULES = extraction_rules.DEFAULT_EXTRACTION_RULES
DEFAULT_VALIDATION_OUTPUT_SCHEMA = categorical_schema.VALIDATION_AGENT_OUTPUT_SCHEMA
DEFAULT_VALIDATION_RULES = validation_rules.DEFAULT_VALIDATION_RULES
DEFAULT_NORMALIZER_OUTPUT_SCHEMA = numerical_schema.NORMALIZER_AGENT_OUTPUT_SCHEMA
DEFAULT_NORMALIZER_RULES = normalizer_rules.DEFAULT_NORMALIZER_RULES
DEFAULT_FORMATTER_RULES = formatter_rules.DEFAULT_FORMATTER_RULES
DEFAULT_FORMATTER_OUTPUT_SCHEMA = formatter_schema.DEFAULT_FORMATTER_OUTPUT_SCHEMA
DEFAULT_FORMATTER_CONFIGURATION = formatter_schema.CLIENT_CONFIGURATION_SCHEMA

REFERENCE_DATA = ALL_REFERENCE_DATA

# Sample data for development
SAMPLE_CLIENTS = [
    {
        "client_id": get_default_client_id(),
        "name": "Global Extractor Template",
        "has_kb": True,
        "has_instruction": True,
        "has_output_preference": True,
        "client_code": "global",
        "status": "active",
    },
    {
        "client_id": str(uuid.uuid4()),
        "name": "Developer",
        "has_kb": False,
        "has_instruction": False,
        "has_output_preference": False,
        "client_code": "dev",
        "status": "active",
    },
]

CATALOG_TYPES = [
    {"catalog_type_id": str(uuid.uuid4()), "name": "grade", "description": "Standard steel grade designations"},
    {"catalog_type_id": str(uuid.uuid4()), "name": "coating", "description": "Metal coating types and specifications"},
    {"catalog_type_id": str(uuid.uuid4()), "name": "finish", "description": "Surface finish specifications"},
    {"catalog_type_id": str(uuid.uuid4()), "name": "surface_type", "description": "Material surface type specifications"},
    {"catalog_type_id": str(uuid.uuid4()), "name": "surface_protection", "description": "Material surface protection specifications"},
    {"catalog_type_id": str(uuid.uuid4()), "name": "certificate", "description": "Quality and compliance certificates"},
    {"catalog_type_id": str(uuid.uuid4()), "name": "form", "description": "Dimensional criteria for classifying steel forms"},
    {"catalog_type_id": str(uuid.uuid4()), "name": "form_priority_rules", "description": "Form classification priority rules for validation"},
    {"catalog_type_id": str(uuid.uuid4()), "name": "language_translations", "description": "Language translations for steel industry terms"},
]

COMPATIBILITY_DATA = [
    {"grade_code": "DC01", "coating_code": "Z100"},
    {"grade_code": "DC01", "coating_code": "Z140"},
    {"grade_code": "DC01", "coating_code": "Z200"},
    {"grade_code": "DC03", "coating_code": "Z100"},
    {"grade_code": "DC03", "coating_code": "Z140"},
    {"grade_code": "DX51D", "coating_code": "Z140"},
    {"grade_code": "DX51D", "coating_code": "Z200"},
    {"grade_code": "DX51D", "coating_code": "Z275"},
    {"grade_code": "DX51D", "coating_code": "AZ150"},
    {"grade_code": "S235JR", "coating_code": "Z275"},
    {"grade_code": "S355J2", "coating_code": "Z275"},
]

# Language translations (German terms) - moved from Migration 03
LANGUAGE_TRANSLATIONS = [
    ("verzinkt", "galvanized", "German term for galvanized coating"),
    ("Spule", "coil", "German term for coil form"),
    ("Blech", "sheet", "German term for sheet form"),
    ("chem. Werte", "chemical values", "German term for chemical analysis"),
    ("mech. Werten", "mechanical values", "German term for mechanical properties"),
    ("geb. gef.", "pickled and oiled", "German abbreviation for pickled and oiled surface treatment"),
    ("gebeizt geölt", "pickled and oiled", "German term for pickled and oiled surface treatment"),
    ("geölt", "oiled", "German term for oiled surface protection"),
    ("Warmbreitband", "warm-broad strip steel", "German term for hot-rolled wide strip"),
    ("NK", "not specified", "German abbreviation meaning not specified"),
    ("ID", "inner diameter", "German abbreviation for inner diameter"),
    ("Coilbreite", "coil width", "German term for coil width"),
    ("Dehnung", "elongation", "German term for elongation percentage"),
]

# Form priority rules - moved from Migration 04
FORM_PRIORITY_RULES = [
    "Check catalog form validation first",
    "If catalog validation fails or has low confidence, apply dimensional classification",
    "Use dimensional criteria to suggest correct form classification",
    "Prefer more specific forms over generic ones (e.g., Heavy Plates over Plates)",
    "Consider multiple possible matches and rank by specificity",
]


def is_development_environment() -> bool:
    """Check if we're running in development environment."""
    runtime_env = os.getenv("RUNTIME_ENV", "dev")
    return runtime_env.lower() in ["dev", "development", "stg", "staging"]


def upgrade() -> None:
    """
    Populate development data.

    Only runs in development environment to avoid populating production
    with test data.
    """
    if not is_development_environment():
        print("🏭 Production environment detected - skipping development data population")
        print("✅ Migration completed (no changes in production)")
        return

    print("🔧 Development environment detected - populating development data...")
    print("⚠️  This will clear ALL existing data and repopulate with fresh development data!")

    # Clear existing data first
    clear_existing_data()

    # Add unique constraints to prevent duplicates
    add_unique_constraints()

    # Populate fresh development data
    populate_fresh_data()

    print("\n🎉 Development data population completed successfully!")
    print("✅ All tables populated with fresh development data")


def clear_existing_data() -> None:
    """Clear existing data from all tables."""
    print("\n📋 Clearing existing data...")

    # Order matters due to foreign key constraints
    tables_to_clear = [
        "rfq_processing_history",
        "rfq_grade_coating_compatibility",
        "rfq_reference_data",
        "rfq_client_configurations",
        "rfq_instruction_schemas",
        "rfq_catalog_types",
        "rfq_clients",
    ]

    for table_name in tables_to_clear:
        try:
            op.execute(f"TRUNCATE TABLE {table_name} RESTART IDENTITY CASCADE;")
            print(f"✅ Cleared table: {table_name}")
        except Exception as e:
            print(f"⚠️  Warning: Could not clear table {table_name}: {e}")


def add_unique_constraints() -> None:
    """Add unique constraints to prevent duplicate data."""
    print("🔒 Adding unique constraints to prevent duplicates...")

    try:
        # Drop the old constraint if it exists
        op.execute("DROP INDEX IF EXISTS idx_rfq_reference_unique;")

        # Add unique constraint on reference data (client_id, catalog_type_id, code, name)
        # This allows multiple entries with same code but different names (e.g., AS50, AS100)
        op.execute("""
            CREATE UNIQUE INDEX IF NOT EXISTS idx_rfq_reference_unique
            ON rfq_reference_data (client_id, catalog_type_id, code, name)
            WHERE client_id IS NOT NULL;
        """)
        print("✅ Added unique constraint on reference data (client_id, catalog_type_id, code, name)")
    except Exception as e:
        print(f"⚠️  Warning: Could not add unique constraint: {e}")

    try:
        # Add unique constraint on instruction schemas (client_id, type, version)
        op.execute("""
            CREATE UNIQUE INDEX IF NOT EXISTS idx_rfq_instruction_unique
            ON rfq_instruction_schemas (client_id, type, version)
            WHERE client_id IS NOT NULL;
        """)
        print("✅ Added unique constraint on instruction schemas")
    except Exception as e:
        print(f"⚠️  Warning: Could not add unique constraint: {e}")


def populate_fresh_data() -> None:
    """Populate tables with fresh development data."""
    print("\n🔄 Populating fresh development data...")

    # 1. Insert clients
    populate_clients()

    # 2. Insert catalog types
    populate_catalog_types()

    # 3. Insert versioned instruction schemas (NEW)
    populate_instruction_schemas()

    # 4. Insert client configurations with UUID references
    populate_client_configurations()

    # 5. Populate reference data
    populate_reference_data()

    # 6. Add form criteria (consolidated from Migration 04)
    populate_all_form_data()

    # 7. Add language translations (moved from Migration 03)
    populate_language_translations()

    # 8. Populate grade-coating compatibility
    populate_compatibility_data()

    print("✅ Fresh development data populated successfully!")


def populate_clients() -> None:
    """Populate rfq_clients table."""
    print("👥 Populating clients...")

    rfq_clients = table(
        "rfq_clients",
        column("client_id", UUID(as_uuid=True)),
        column("name", String),
        column("client_code", String),
        column("has_kb", sa.Boolean),
        column("has_instruction", sa.Boolean),
        column("has_output_preference", sa.Boolean),
        column("status", String),
        column("created_at", DateTime),
        column("updated_at", DateTime),
    )

    op.bulk_insert(
        rfq_clients,
        [
            {
                "client_id": uuid.UUID(str(client["client_id"])),
                "name": client["name"],
                "client_code": client["client_code"],
                "has_kb": client["has_kb"],
                "has_instruction": client["has_instruction"],
                "has_output_preference": client["has_output_preference"],
                "status": client["status"],
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow(),
            }
            for client in SAMPLE_CLIENTS
        ],
    )


def populate_catalog_types() -> None:
    """Populate rfq_catalog_types table."""
    print("📂 Populating catalog types...")

    rfq_catalog_types = table(
        "rfq_catalog_types",
        column("catalog_type_id", UUID(as_uuid=True)),
        column("name", String),
        column("description", Text),
        column("created_at", DateTime),
        column("updated_at", DateTime),
    )

    op.bulk_insert(
        rfq_catalog_types,
        [
            {
                "catalog_type_id": uuid.UUID(str(catalog_type["catalog_type_id"])),
                "name": catalog_type["name"],
                "description": catalog_type["description"],
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow(),
            }
            for catalog_type in CATALOG_TYPES
        ],
    )


def populate_instruction_schemas() -> None:
    """Populate rfq_instruction_schemas table with versioned schemas and rules."""
    print("📄 Populating versioned instruction schemas...")

    rfq_instruction_schemas = table(
        "rfq_instruction_schemas",
        column("id", UUID(as_uuid=True)),
        column("client_id", UUID(as_uuid=True)),
        column("name", String),
        column("type", String),
        column("agent_prefix", String),
        column("version", String),
        column("content", JSONB),
        column("description", Text),
        column("is_active", sa.Boolean),
        column("created_at", DateTime),
        column("updated_at", DateTime),
    )

    # Define all schemas and rules to migrate
    schemas_to_create = []

    # For each client, create versioned schemas
    for client in SAMPLE_CLIENTS:
        client_uuid = uuid.UUID(str(client["client_id"]))

        # Extraction Agent Schemas (e.1.0.0)
        schemas_to_create.extend(
            [
                {
                    "id": uuid.uuid4(),
                    "client_id": client_uuid,
                    "name": "Extraction Output Schema",
                    "type": "extraction_schema",
                    "agent_prefix": "extraction",
                    "version": "1.0.0",
                    "content": DEFAULT_EXTRACTION_OUTPUT_SCHEMA,
                    "description": "Initial extraction output schema with material specifications",
                    "is_active": True,
                    "created_at": datetime.utcnow(),
                    "updated_at": datetime.utcnow(),
                },
                {
                    "id": uuid.uuid4(),
                    "client_id": client_uuid,
                    "name": "Extraction Rules",
                    "type": "extraction_rules",
                    "agent_prefix": "extraction",
                    "version": "1.0.0",
                    "content": DEFAULT_EXTRACTION_RULES,
                    "description": "Initial extraction rules and patterns",
                    "is_active": True,
                    "created_at": datetime.utcnow(),
                    "updated_at": datetime.utcnow(),
                },
            ]
        )

        # Validation Agent Schemas (1.0.0)
        schemas_to_create.extend(
            [
                {
                    "id": uuid.uuid4(),
                    "client_id": client_uuid,
                    "name": "Validation Output Schema",
                    "type": "validation_schema",
                    "agent_prefix": "validation",
                    "version": "1.0.0",
                    "content": DEFAULT_VALIDATION_OUTPUT_SCHEMA,
                    "description": "Initial validation output schema for categorical data",
                    "is_active": True,
                    "created_at": datetime.utcnow(),
                    "updated_at": datetime.utcnow(),
                },
                {
                    "id": uuid.uuid4(),
                    "client_id": client_uuid,
                    "name": "Validation Rules",
                    "type": "validation_rules",
                    "agent_prefix": "validation",
                    "version": "1.0.0",
                    "content": DEFAULT_VALIDATION_RULES,
                    "description": "Initial validation rules and constraints",
                    "is_active": True,
                    "created_at": datetime.utcnow(),
                    "updated_at": datetime.utcnow(),
                },
            ]
        )

        # Normalizer Agent Schemas (n.1.0.0)
        schemas_to_create.extend(
            [
                {
                    "id": uuid.uuid4(),
                    "client_id": client_uuid,
                    "name": "Normalizer Output Schema",
                    "type": "normalizer_schema",
                    "agent_prefix": "normalization",
                    "version": "1.0.0",
                    "content": DEFAULT_NORMALIZER_OUTPUT_SCHEMA,
                    "description": "Initial normalizer output schema for numerical data",
                    "is_active": True,
                    "created_at": datetime.utcnow(),
                    "updated_at": datetime.utcnow(),
                },
                {
                    "id": uuid.uuid4(),
                    "client_id": client_uuid,
                    "name": "Normalizer Rules",
                    "type": "normalizer_rules",
                    "agent_prefix": "normalization",
                    "version": "1.0.0",
                    "content": DEFAULT_NORMALIZER_RULES,
                    "description": "Initial normalization rules and unit conversions",
                    "is_active": True,
                    "created_at": datetime.utcnow(),
                    "updated_at": datetime.utcnow(),
                },
            ]
        )

        # Formatter Agent Schemas (f.1.0.0) - NEW!
        schemas_to_create.extend(
            [
                {
                    "id": uuid.uuid4(),
                    "client_id": client_uuid,
                    "name": "Formatter Output Schema",
                    "type": "formatter_schema",
                    "agent_prefix": "formatter",
                    "version": "1.0.0",
                    "content": DEFAULT_FORMATTER_OUTPUT_SCHEMA,
                    "description": "Initial formatter output schema with min/max dimensional values",
                    "is_active": True,
                    "created_at": datetime.utcnow(),
                    "updated_at": datetime.utcnow(),
                },
                {
                    "id": uuid.uuid4(),
                    "client_id": client_uuid,
                    "name": "Formatter Rules",
                    "type": "formatter_rules",
                    "agent_prefix": "formatter",
                    "version": "1.0.0",
                    "content": DEFAULT_FORMATTER_RULES,
                    "description": "Initial formatter rules for output formatting",
                    "is_active": True,
                    "created_at": datetime.utcnow(),
                    "updated_at": datetime.utcnow(),
                },
                {
                    "id": uuid.uuid4(),
                    "client_id": client_uuid,
                    "name": "Formatter Configuration",
                    "type": "formatter_configuration",
                    "agent_prefix": "formatter",
                    "version": "1.0.0",
                    "content": DEFAULT_FORMATTER_CONFIGURATION,
                    "description": "Initial formatter configuration schema",
                    "is_active": True,
                    "created_at": datetime.utcnow(),
                    "updated_at": datetime.utcnow(),
                },
            ]
        )

    op.bulk_insert(rfq_instruction_schemas, schemas_to_create)
    print(f"  ✅ Created {len(schemas_to_create)} versioned instruction schemas")


def populate_client_configurations() -> None:
    """Populate rfq_client_configurations with UUID references to versioned schemas."""
    print("⚙️  Populating client configurations with UUID references...")

    # First, get the schema IDs we just created
    connection = op.get_bind()

    # Query to get all schema IDs for each client and type
    schema_query = """
    SELECT client_id, type, agent_prefix, version, id
    FROM rfq_instruction_schemas
    WHERE is_active = true
    ORDER BY client_id, type
    """

    schema_results = connection.execute(sa.text(schema_query)).fetchall()

    # Build a mapping of client_id -> schema_type -> id
    schema_mapping: dict[str, dict[str, uuid.UUID]] = {}
    for row in schema_results:
        client_id, schema_type, agent_prefix, version, schema_id = row
        # Convert client_id to string for consistent lookup
        client_key = str(client_id) if client_id else None
        if client_key and client_key not in schema_mapping:
            schema_mapping[client_key] = {}
        if client_key:
            schema_mapping[client_key][schema_type] = schema_id

    # Debug: Print schema mapping
    print(f"  📋 Schema mapping created for {len(schema_mapping)} clients:")
    for client_key, schemas in schema_mapping.items():
        print(f"    Client {client_key}: {len(schemas)} schemas ({list(schemas.keys())})")

    # Create client configurations table definition
    rfq_client_configurations = table(
        "rfq_client_configurations",
        column("config_id", UUID(as_uuid=True)),
        column("client_id", UUID(as_uuid=True)),
        column("version", String),
        column("extraction_output_schema_id", UUID(as_uuid=True)),
        column("validation_output_schema_id", UUID(as_uuid=True)),
        column("normalizer_output_schema_id", UUID(as_uuid=True)),
        column("extraction_rules_id", UUID(as_uuid=True)),
        column("validation_rules_id", UUID(as_uuid=True)),
        column("normalization_rules_id", UUID(as_uuid=True)),
        column("formatter_rules_id", UUID(as_uuid=True)),
        column("formatter_output_config_id", UUID(as_uuid=True)),
        column("formatter_output_schema_id", UUID(as_uuid=True)),
        column("model_preferences", JSONB),
        column("created_at", DateTime),
        column("updated_at", DateTime),
    )

    active_clients = [client for client in SAMPLE_CLIENTS if client["status"] == "active"]

    # Create client configurations with UUID references
    configurations = []
    for client in active_clients:
        client_uuid = uuid.UUID(str(client["client_id"]))
        client_key = str(client_uuid)
        client_schemas = schema_mapping.get(client_key, {})

        # Debug: Print what schemas were found for this client
        print(f"  🔍 Client {client['name']} ({client_key}):")
        print(f"      Found {len(client_schemas)} schemas: {list(client_schemas.keys())}")
        if not client_schemas:
            print(f"      ⚠️  No schemas found! Available clients in mapping: {list(schema_mapping.keys())}")

        # Create model preferences based on client type
        if client["client_id"] == get_default_client_id():
            # Generic client gets full model preferences
            model_prefs = {
                "default_model": "anthropic/claude-3.5-sonnet",
                "agent_models": {
                    "extraction": "anthropic/claude-3.5-sonnet",
                    "validation": "google/gemini-pro-1.5",
                    "normalizer": "anthropic/claude-3.5-sonnet",
                    "formatter": "google/gemini-flash-1.5",
                },
                "performance_settings": {
                    "extraction": {"temperature": 0.0, "max_tokens": 6144},
                    "validation": {"temperature": 0.1, "max_tokens": 8192},
                    "normalizer": {"temperature": 0.0, "max_tokens": 4096},
                    "formatter": {"temperature": 0.0, "max_tokens": 4096},
                },
                "fallback_model": "google/gemini-pro-1.5",
            }
        else:
            # Other clients get basic model preferences
            model_prefs = {
                "default_model": "anthropic/claude-3.5-sonnet",
                "agent_models": {
                    "extraction": "anthropic/claude-3.5-sonnet",
                    "validation": "google/gemini-pro-1.5",
                    "normalizer": "anthropic/claude-3.5-sonnet",
                    "formatter": "google/gemini-pro-1.5",
                },
                "performance_settings": {
                    "extraction": {"temperature": 0.0, "max_tokens": 6144},
                    "validation": {"temperature": 0.1, "max_tokens": 8192},
                },
                "fallback_model": "google/gemini-pro-1.5",
            }

        # Debug: Show what IDs are being assigned
        config_data = {
            "config_id": uuid.uuid4(),
            "client_id": client_uuid,
            "version": "1.0.0",  # Initial configuration version
            "extraction_output_schema_id": client_schemas.get("extraction_schema"),
            "validation_output_schema_id": client_schemas.get("validation_schema"),
            "normalizer_output_schema_id": client_schemas.get("normalizer_schema"),
            "extraction_rules_id": client_schemas.get("extraction_rules"),
            "validation_rules_id": client_schemas.get("validation_rules"),
            "normalization_rules_id": client_schemas.get("normalizer_rules"),
            "formatter_rules_id": client_schemas.get("formatter_rules"),
            "formatter_output_config_id": client_schemas.get("formatter_configuration"),
            "formatter_output_schema_id": client_schemas.get("formatter_schema"),
            "model_preferences": model_prefs,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow(),
        }

        # Debug: Print schema ID assignments
        schema_assignments = {k: v for k, v in config_data.items() if k.endswith("_id") and k != "config_id" and k != "client_id"}
        non_null_assignments = {k: v for k, v in schema_assignments.items() if v is not None}
        null_assignments = {k: v for k, v in schema_assignments.items() if v is None}

        print(f"      ✅ Assigned {len(non_null_assignments)} schema IDs: {list(non_null_assignments.keys())}")
        if null_assignments:
            print(f"      ❌ Missing {len(null_assignments)} schema IDs: {list(null_assignments.keys())}")

        configurations.append(config_data)

    op.bulk_insert(rfq_client_configurations, configurations)
    print(f"  ✅ Created {len(configurations)} client configurations with UUID references")


def populate_reference_data() -> None:
    """Populate rfq_reference_data with steel industry data."""
    print("📊 Populating reference data...")

    rfq_reference_data = table(
        "rfq_reference_data",
        column("reference_id", UUID(as_uuid=True)),
        column("client_id", UUID(as_uuid=True)),
        column("catalog_type_id", UUID(as_uuid=True)),
        column("code", String),
        column("name", String),
        column("description", Text),
        column("properties", JSONB),
        column("is_active", sa.Boolean),
        column("created_at", DateTime),
        column("updated_at", DateTime),
    )

    # Create mapping of catalog type names to IDs
    catalog_type_map = {cat["name"]: cat["catalog_type_id"] for cat in CATALOG_TYPES}

    # Create reference data for ALL active clients
    active_clients = [client for client in SAMPLE_CLIENTS if client["status"] == "active"]
    reference_entries = []
    for client in active_clients:
        for data in REFERENCE_DATA:
            reference_entries.append(
                {
                    "reference_id": uuid.uuid4(),
                    "client_id": uuid.UUID(str(client["client_id"])),
                    "catalog_type_id": uuid.UUID(catalog_type_map[data["catalog_type"]]),
                    "code": data["code"],
                    "name": data["name"],
                    "description": data.get("description"),
                    "properties": data.get("properties", {}),
                    "is_active": True,
                    "created_at": datetime.utcnow(),
                    "updated_at": datetime.utcnow(),
                }
            )

    op.bulk_insert(rfq_reference_data, reference_entries)
    print(f"  ✅ Added {len(reference_entries)} reference data entries")


def populate_all_form_data() -> None:
    """Populate all form-related data (criteria and priority rules)."""
    print("📌 Populating form criteria and priority rules...")

    rfq_reference_data = table(
        "rfq_reference_data",
        column("reference_id", UUID(as_uuid=True)),
        column("client_id", UUID(as_uuid=True)),
        column("catalog_type_id", UUID(as_uuid=True)),
        column("code", String),
        column("name", String),
        column("description", Text),
        column("properties", JSONB),
        column("is_active", sa.Boolean),
        column("created_at", DateTime),
        column("updated_at", DateTime),
    )

    # Create mapping of catalog type names to IDs
    catalog_type_map = {cat["name"]: cat["catalog_type_id"] for cat in CATALOG_TYPES}
    active_clients = [client for client in SAMPLE_CLIENTS if client["status"] == "active"]

    # 1. Populate form criteria from FORM_DIMENSIONAL_CRITERIA
    if FORM_DIMENSIONAL_CRITERIA:
        form_criteria_data = []
        for client in active_clients:
            for form_name, criteria_dict in FORM_DIMENSIONAL_CRITERIA.items():
                criteria_dict = cast(dict[str, Any], criteria_dict)  # Type hint for mypy
                form_criteria_data.append(
                    {
                        "reference_id": uuid.uuid4(),
                        "client_id": uuid.UUID(str(client["client_id"])),
                        "catalog_type_id": uuid.UUID(str(catalog_type_map["form"])),
                        "code": form_name.replace(" ", "_").upper(),
                        "name": form_name,
                        "description": f"Dimensional criteria for {form_name}",
                        "properties": {
                            "qualifying_criteria": criteria_dict["qualifying_criteria"],
                            "dimensional_criteria": criteria_dict["dimensional_criteria"],
                            "category": "steel_form",
                            "evaluation_functions": {
                                "IS_PRESENT": "Checks if the dimension field exists and has a non-null value > 0",
                                "APPROX_EQUAL": "Checks if two dimensions are approximately equal (within 5% tolerance)",
                                "NOT_APPROX_EQUAL": "Checks if two dimensions are NOT approximately equal (beyond 5% tolerance)",
                                "MAX": "Returns the maximum of two dimension values",
                            },
                        },
                        "is_active": True,
                        "created_at": datetime.utcnow(),
                        "updated_at": datetime.utcnow(),
                    }
                )

        if form_criteria_data:
            op.bulk_insert(rfq_reference_data, form_criteria_data)
            print(f"  ✅ Added {len(form_criteria_data)} form criteria entries")

    # 2. Populate form priority rules
    priority_data = []
    for client in active_clients:
        for i, rule in enumerate(FORM_PRIORITY_RULES):
            priority_data.append(
                {
                    "reference_id": uuid.uuid4(),
                    "client_id": uuid.UUID(str(client["client_id"])),
                    "catalog_type_id": uuid.UUID(str(catalog_type_map["form_priority_rules"])),
                    "code": f"PRIORITY_{i + 1:02d}",
                    "name": rule,
                    "description": f"Form classification priority rule {i + 1}",
                    "properties": {"priority_order": i + 1, "category": "form_validation"},
                    "is_active": True,
                    "created_at": datetime.utcnow(),
                    "updated_at": datetime.utcnow(),
                }
            )

    if priority_data:
        op.bulk_insert(rfq_reference_data, priority_data)
        print(f"  ✅ Added {len(priority_data)} form priority rules")


def populate_language_translations() -> None:
    """Populate language translations (consolidated from Migration 03)."""
    print("🌍 Populating language translations...")

    rfq_reference_data = table(
        "rfq_reference_data",
        column("reference_id", UUID(as_uuid=True)),
        column("client_id", UUID(as_uuid=True)),
        column("catalog_type_id", UUID(as_uuid=True)),
        column("code", String),
        column("name", String),
        column("description", Text),
        column("properties", JSONB),
        column("is_active", sa.Boolean),
        column("created_at", DateTime),
        column("updated_at", DateTime),
    )

    # Create mapping of catalog type names to IDs
    catalog_type_map = {cat["name"]: cat["catalog_type_id"] for cat in CATALOG_TYPES}
    active_clients = [client for client in SAMPLE_CLIENTS if client["status"] == "active"]

    translation_data = []
    for client in active_clients:
        for german_term, english_translation, description in LANGUAGE_TRANSLATIONS:
            translation_data.append(
                {
                    "reference_id": uuid.uuid4(),
                    "client_id": uuid.UUID(str(client["client_id"])),
                    "catalog_type_id": uuid.UUID(str(catalog_type_map["language_translations"])),
                    "code": german_term,
                    "name": english_translation,
                    "description": description,
                    "properties": {"language_pair": "de_to_en", "category": "steel_industry"},
                    "is_active": True,
                    "created_at": datetime.utcnow(),
                    "updated_at": datetime.utcnow(),
                }
            )

    if translation_data:
        op.bulk_insert(rfq_reference_data, translation_data)
        print(f"  ✅ Added {len(translation_data)} language translations")


def populate_form_criteria() -> None:
    """Populate form criteria (merged from add_form_criteria migration)."""
    print("📐 Populating form criteria...")

    rfq_reference_data = table(
        "rfq_reference_data",
        column("reference_id", UUID(as_uuid=True)),
        column("client_id", UUID(as_uuid=True)),
        column("catalog_type_id", UUID(as_uuid=True)),
        column("code", String),
        column("name", String),
        column("description", Text),
        column("properties", JSONB),
        column("is_active", sa.Boolean),
        column("created_at", DateTime),
        column("updated_at", DateTime),
    )

    # Create mapping of catalog type names to IDs
    catalog_type_map = {cat["name"]: cat["catalog_type_id"] for cat in CATALOG_TYPES}
    active_clients = [client for client in SAMPLE_CLIENTS if client["status"] == "active"]

    try:
        from utils.rfq.rules.form_criteria import FORM_DIMENSIONAL_CRITERIA

        # Create form criteria data for ALL active clients
        form_criteria_data = []
        for client in active_clients:
            for form_name, criteria_dict in FORM_DIMENSIONAL_CRITERIA.items():
                criteria_dict = cast(dict[str, Any], criteria_dict)  # Type hint for mypy
                form_criteria_data.append(
                    {
                        "reference_id": uuid.uuid4(),
                        "client_id": uuid.UUID(str(client["client_id"])),
                        "catalog_type_id": uuid.UUID(str(catalog_type_map["form"])),
                        "code": form_name,
                        "name": form_name,
                        "description": f"Steel form: {form_name}",
                        "properties": {
                            "qualifying_criteria": criteria_dict["qualifying_criteria"],
                            "dimensional_criteria": criteria_dict["dimensional_criteria"],
                            "category": "steel_form",
                            "evaluation_functions": {
                                "IS_PRESENT": "Checks if the dimension field exists and has a non-null value > 0",
                                "APPROX_EQUAL": "Checks if two dimensions are approximately equal (within 5% tolerance)",
                                "NOT_APPROX_EQUAL": "Checks if two dimensions are NOT approximately equal (beyond 5% tolerance)",
                                "MAX": "Returns the maximum of two dimension values",
                            },
                        },
                        "is_active": True,
                        "created_at": datetime.utcnow(),
                        "updated_at": datetime.utcnow(),
                    }
                )

        op.bulk_insert(rfq_reference_data, form_criteria_data)
    except ImportError:
        print("⚠️  Warning: Could not import form criteria, skipping...")


def populate_compatibility_data() -> None:
    """Populate rfq_grade_coating_compatibility table."""
    print("🔗 Populating grade-coating compatibility...")

    rfq_grade_coating_compatibility = table(
        "rfq_grade_coating_compatibility",
        column("compatibility_id", UUID(as_uuid=True)),
        column("client_id", UUID(as_uuid=True)),
        column("grade_code", String),
        column("coating_code", String),
        column("created_at", DateTime),
        column("updated_at", DateTime),
    )

    active_clients = [client for client in SAMPLE_CLIENTS if client["status"] == "active"]
    compatibility_entries = []
    for client in active_clients:
        for comp in COMPATIBILITY_DATA:
            compatibility_entries.append(
                {
                    "compatibility_id": uuid.uuid4(),
                    "client_id": uuid.UUID(str(client["client_id"])),
                    "grade_code": comp["grade_code"],
                    "coating_code": comp["coating_code"],
                    "created_at": datetime.utcnow(),
                    "updated_at": datetime.utcnow(),
                }
            )

    op.bulk_insert(rfq_grade_coating_compatibility, compatibility_entries)
    print(f"  ✅ Added {len(compatibility_entries)} grade-coating compatibility entries")


def downgrade() -> None:
    """
    Downgrade is not supported for this migration.
    This migration is designed to populate development data only.
    """
    if not is_development_environment():
        print("🏭 Production environment - no downgrade action needed")
        return

    print("⚠️  Downgrade not supported for development data population migration")
    print("💡 To reset data again, re-run this migration or use a database backup")
