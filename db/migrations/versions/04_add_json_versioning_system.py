"""Add JSON-based versioning system for client configurations

Revision ID: 04_json_versioning
Revises: 03_add_client_output_preferences
Create Date: 2025-07-16 16:00:00.000000

"""

import json

import sqlalchemy as sa
from alembic import op
from sqlalchemy import String, text
from sqlalchemy.dialects.postgresql import JSONB

# revision identifiers, used by Alembic.
revision = "04_json_versioning"
down_revision = "03_add_client_output_preferences"
branch_labels = None
depends_on = None


def upgrade() -> None:
    """
    Replace version column with active_versions JSON column.

    This allows tracking active versions for each component type
    (extraction, validation, normalization, formatter) separately.
    """
    print("🔄 Upgrading to JSON-based versioning system...")

    # Step 1: Add the new active_versions column
    print("📝 Adding active_versions JSON column...")
    op.add_column(
        "rfq_client_configurations",
        sa.Column("active_versions", JSONB, nullable=True, comment="JSON tracking active versions for each component type"),
    )

    # Step 2: Populate active_versions with current version data
    print("🔄 Populating active_versions from existing data...")
    populate_active_versions()

    # Step 3: Drop the old version column
    print("🗑️  Dropping old version column...")
    op.drop_column("rfq_client_configurations", "version")

    # Step 4: Make active_versions NOT NULL now that it's populated
    print("🔒 Making active_versions column NOT NULL...")
    op.alter_column("rfq_client_configurations", "active_versions", nullable=False)

    print("✅ JSON-based versioning system upgrade completed!")


def populate_active_versions() -> None:
    """Populate the active_versions JSON column with current version information."""

    # Get database connection
    connection = op.get_bind()

    # Query to get all client configurations and their referenced schemas
    query = """
    SELECT 
        cc.config_id,
        cc.client_id,
        
        -- Get versions for each schema type
        es.version as extraction_schema_version,
        vs.version as validation_schema_version, 
        ns.version as normalizer_schema_version,
        fs.version as formatter_schema_version,
        
        er.version as extraction_rules_version,
        vr.version as validation_rules_version,
        nr.version as normalization_rules_version,
        fr.version as formatter_rules_version,
        fc.version as formatter_config_version
        
    FROM rfq_client_configurations cc
    LEFT JOIN rfq_instruction_schemas es ON cc.extraction_output_schema_id = es.id
    LEFT JOIN rfq_instruction_schemas vs ON cc.validation_output_schema_id = vs.id  
    LEFT JOIN rfq_instruction_schemas ns ON cc.normalizer_output_schema_id = ns.id
    LEFT JOIN rfq_instruction_schemas fs ON cc.formatter_output_schema_id = fs.id
    LEFT JOIN rfq_instruction_schemas er ON cc.extraction_rules_id = er.id
    LEFT JOIN rfq_instruction_schemas vr ON cc.validation_rules_id = vr.id
    LEFT JOIN rfq_instruction_schemas nr ON cc.normalization_rules_id = nr.id
    LEFT JOIN rfq_instruction_schemas fr ON cc.formatter_rules_id = fr.id
    LEFT JOIN rfq_instruction_schemas fc ON cc.formatter_output_config_id = fc.id
    """

    results = connection.execute(text(query)).fetchall()

    # Update each configuration with active versions JSON
    for row in results:
        config_id = row[0]

        # Build the active versions JSON structure
        active_versions = {
            "extraction": {
                "output_schema": row[2] or "1.0.0",  # extraction_schema_version
                "rules": row[6] or "1.0.0",  # extraction_rules_version
            },
            "validation": {
                "output_schema": row[3] or "1.0.0",  # validation_schema_version
                "rules": row[7] or "1.0.0",  # validation_rules_version
            },
            "normalizer": {
                "output_schema": row[4] or "1.0.0",  # normalizer_schema_version
                "rules": row[8] or "1.0.0",  # normalization_rules_version
            },
            "formatter": {
                "output_schema": row[5] or "1.0.0",  # formatter_schema_version
                "rules": row[9] or "1.0.0",  # formatter_rules_version
                "configuration": row[10] or "1.0.0",  # formatter_config_version
            },
        }

        # Update the configuration with the active versions JSON using op.execute
        op.execute(f"""
            UPDATE rfq_client_configurations
            SET active_versions = '{json.dumps(active_versions)}'::jsonb
            WHERE config_id = '{config_id}'
        """)

    print(f"  ✅ Updated {len(results)} client configurations with active versions")


def downgrade() -> None:
    """
    Downgrade from JSON-based versioning back to simple version column.
    """
    print("🔄 Downgrading from JSON-based versioning system...")

    # Step 1: Add back the version column
    print("📝 Adding back version column...")
    op.add_column("rfq_client_configurations", sa.Column("version", String(20), nullable=True, comment="Version tracking for client configuration"))

    # Step 2: Populate version column with default value
    print("🔄 Populating version column with default values...")
    connection = op.get_bind()
    connection.execute(text("UPDATE rfq_client_configurations SET version = '1.0.0'"))

    # Step 3: Make version column NOT NULL
    print("🔒 Making version column NOT NULL...")
    op.alter_column("rfq_client_configurations", "version", nullable=False)

    # Step 4: Drop the active_versions column
    print("🗑️  Dropping active_versions column...")
    op.drop_column("rfq_client_configurations", "active_versions")

    print("✅ Downgrade completed!")
