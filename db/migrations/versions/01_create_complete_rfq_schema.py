"""Create complete RFQ schema with all tables and features

Revision ID: 01_create_schema
Revises:
Create Date: 2025-07-01 20:00:00.000000

"""

import uuid
from datetime import datetime

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects.postgresql import J<PERSON>N<PERSON>, UUID

# revision identifiers, used by Alembic.
revision = "01_create_schema"
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    """
    Create complete RFQ database schema.

    Drops existing tables if they exist to avoid conflicts,
    then creates the complete, final schema.
    """

    # Drop existing tables in correct order (foreign keys first)
    print("🗑️  Dropping existing tables if they exist...")
    drop_tables_if_exist()

    print("🏗️  Creating complete RFQ schema...")

    # Create all tables with final, complete structure
    create_rfq_clients_table()
    create_rfq_catalog_types_table()
    create_rfq_instruction_schemas_table()
    create_rfq_client_configurations_table()
    create_rfq_reference_data_table()
    create_rfq_grade_coating_compatibility_table()
    create_rfq_processing_history_table()

    # Create indexes for performance
    create_performance_indexes()

    print("✅ Complete RFQ schema created successfully!")


def drop_tables_if_exist() -> None:
    """Drop existing tables in correct order to avoid foreign key conflicts."""
    tables_to_drop = [
        "rfq_processing_history",
        "rfq_grade_coating_compatibility",
        "rfq_reference_data",
        "rfq_client_configurations",
        "rfq_instruction_schemas",
        "rfq_catalog_types",
        "rfq_clients",
        # Drop any agent session tables that might exist
        "rfq_extractor_sessions",
        "rfq_validator_sessions",
    ]

    for table_name in tables_to_drop:
        try:
            op.execute(f"DROP TABLE IF EXISTS {table_name} CASCADE;")
            print(f"  Dropped table: {table_name}")
        except Exception as e:
            print(f"  Warning: Could not drop table {table_name}: {e}")


def create_rfq_clients_table() -> None:
    """Create rfq_clients table with client flags."""
    op.create_table(
        "rfq_clients",
        sa.Column("client_id", UUID(as_uuid=True), primary_key=True, default=uuid.uuid4),
        sa.Column("name", sa.String(100), nullable=False),
        sa.Column("client_code", sa.String(100), nullable=False, unique=True),
        sa.Column("has_kb", sa.Boolean(), nullable=False, server_default=sa.text("false"), comment="Whether client has custom knowledge base"),
        sa.Column("has_instruction", sa.Boolean(), nullable=False, server_default=sa.text("false"), comment="Whether client has custom instructions"),
        sa.Column(
            "has_output_preference",
            sa.Boolean(),
            nullable=False,
            server_default=sa.text("false"),
            comment="Whether client has custom output preferences",
        ),
        sa.Column("status", sa.String(20), default="active", nullable=False),
        sa.Column("created_at", sa.DateTime, default=datetime.utcnow, nullable=False),
        sa.Column("updated_at", sa.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False),
    )


def create_rfq_catalog_types_table() -> None:
    """Create rfq_catalog_types table."""
    op.create_table(
        "rfq_catalog_types",
        sa.Column("catalog_type_id", UUID(as_uuid=True), primary_key=True, default=uuid.uuid4),
        sa.Column("name", sa.String(50), unique=True, nullable=False),
        sa.Column("description", sa.Text, nullable=True),
        sa.Column("created_at", sa.DateTime, default=datetime.utcnow, nullable=False),
        sa.Column("updated_at", sa.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False),
    )


def create_rfq_instruction_schemas_table() -> None:
    """Create rfq_instruction_schemas table for versioned schemas and instructions."""
    op.create_table(
        "rfq_instruction_schemas",
        sa.Column("id", UUID(as_uuid=True), primary_key=True, default=uuid.uuid4),
        sa.Column("client_id", UUID(as_uuid=True), sa.ForeignKey("rfq_clients.client_id"), nullable=True, comment="NULL for global/generic schemas"),
        sa.Column("name", sa.String(100), nullable=False, comment="Human-readable name of the schema/rule"),
        sa.Column("type", sa.String(50), nullable=False, comment="Type: extraction_rules, extraction_schema, validation_rules, etc."),
        sa.Column(
            "agent_prefix",
            sa.String(20),
            nullable=False,
            comment="Agent prefix: extraction, validation, normalizer, formatter",
        ),
        sa.Column("version", sa.String(20), nullable=False, comment="Semantic version: 1.0.0, 1.1.0, 2.0.0, etc."),
        sa.Column("content", JSONB, nullable=False, comment="The actual schema or rules JSON content"),
        sa.Column("description", sa.Text, nullable=True, comment="Description of changes in this version"),
        sa.Column("is_active", sa.Boolean, default=True, nullable=False, comment="Whether this version is active"),
        sa.Column("created_at", sa.DateTime, default=datetime.utcnow, nullable=False),
        sa.Column("updated_at", sa.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False),
    )


def create_rfq_client_configurations_table() -> None:
    """Create rfq_client_configurations table with UUID references to versioned schemas."""
    op.create_table(
        "rfq_client_configurations",
        sa.Column("config_id", UUID(as_uuid=True), primary_key=True, default=uuid.uuid4),
        sa.Column("client_id", UUID(as_uuid=True), sa.ForeignKey("rfq_clients.client_id"), nullable=False),
        sa.Column("version", sa.String(20), nullable=False, comment="Version tracking for client configuration"),
        # UUID references to rfq_instruction_schemas table
        sa.Column(
            "extraction_output_schema_id",
            UUID(as_uuid=True),
            sa.ForeignKey("rfq_instruction_schemas.id"),
            nullable=True,
            comment="Reference to extraction output schema",
        ),
        sa.Column(
            "validation_output_schema_id",
            UUID(as_uuid=True),
            sa.ForeignKey("rfq_instruction_schemas.id"),
            nullable=True,
            comment="Reference to validation output schema",
        ),
        sa.Column(
            "normalizer_output_schema_id",
            UUID(as_uuid=True),
            sa.ForeignKey("rfq_instruction_schemas.id"),
            nullable=True,
            comment="Reference to normalizer output schema",
        ),
        sa.Column(
            "formatter_output_schema_id",
            UUID(as_uuid=True),
            sa.ForeignKey("rfq_instruction_schemas.id"),
            nullable=True,
            comment="Reference to formatter output schema",
        ),
        sa.Column(
            "extraction_rules_id",
            UUID(as_uuid=True),
            sa.ForeignKey("rfq_instruction_schemas.id"),
            nullable=True,
            comment="Reference to extraction rules",
        ),
        sa.Column(
            "validation_rules_id",
            UUID(as_uuid=True),
            sa.ForeignKey("rfq_instruction_schemas.id"),
            nullable=True,
            comment="Reference to validation rules",
        ),
        sa.Column(
            "normalization_rules_id",
            UUID(as_uuid=True),
            sa.ForeignKey("rfq_instruction_schemas.id"),
            nullable=True,
            comment="Reference to normalization rules",
        ),
        sa.Column(
            "formatter_rules_id",
            UUID(as_uuid=True),
            sa.ForeignKey("rfq_instruction_schemas.id"),
            nullable=True,
            comment="Reference to formatter rules",
        ),
        sa.Column(
            "formatter_output_config_id",
            UUID(as_uuid=True),
            sa.ForeignKey("rfq_instruction_schemas.id"),
            nullable=True,
            comment="Reference to formatter output config",
        ),
        # Keep model_preferences as JSONB for now (not versioned)
        sa.Column("model_preferences", JSONB, nullable=True, comment="Agent-specific model preferences and settings"),
        sa.Column("created_at", sa.DateTime, default=datetime.utcnow, nullable=False),
        sa.Column("updated_at", sa.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False),
    )


def create_rfq_reference_data_table() -> None:
    """Create rfq_reference_data table for all reference materials."""
    op.create_table(
        "rfq_reference_data",
        sa.Column("reference_id", UUID(as_uuid=True), primary_key=True, default=uuid.uuid4),
        sa.Column(
            "client_id",
            UUID(as_uuid=True),
            sa.ForeignKey("rfq_clients.client_id"),
            nullable=True,
            comment="NULL for global entries, specific client_id for client-specific data",
        ),
        sa.Column("catalog_type_id", UUID(as_uuid=True), sa.ForeignKey("rfq_catalog_types.catalog_type_id"), nullable=False),
        sa.Column("code", sa.String(50), nullable=False, comment="Unique identifier/code for the reference item"),
        sa.Column("name", sa.String(100), nullable=False, comment="Human-readable name or translation"),
        sa.Column("description", sa.Text, nullable=True, comment="Detailed description of the reference item"),
        sa.Column("properties", JSONB, nullable=True, comment="Additional structured properties and metadata"),
        sa.Column("is_active", sa.Boolean, default=True, nullable=False),
        sa.Column("created_at", sa.DateTime, default=datetime.utcnow, nullable=False),
        sa.Column("updated_at", sa.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False),
    )


def create_rfq_grade_coating_compatibility_table() -> None:
    """Create rfq_grade_coating_compatibility table."""
    op.create_table(
        "rfq_grade_coating_compatibility",
        sa.Column("compatibility_id", UUID(as_uuid=True), primary_key=True, default=uuid.uuid4),
        sa.Column(
            "client_id",
            UUID(as_uuid=True),
            sa.ForeignKey("rfq_clients.client_id"),
            nullable=True,
            comment="NULL for global compatibility, specific client_id for client-specific rules",
        ),
        sa.Column("grade_code", sa.String(50), nullable=False, comment="Steel grade code (e.g., DX51D, S250GD)"),
        sa.Column("coating_code", sa.String(50), nullable=False, comment="Coating specification code (e.g., Z275, AZ150)"),
        sa.Column("created_at", sa.DateTime, default=datetime.utcnow, nullable=False),
        sa.Column("updated_at", sa.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False),
    )


def create_rfq_processing_history_table() -> None:
    """Create rfq_processing_history table to match SQLAlchemy model."""
    op.create_table(
        "rfq_processing_history",
        sa.Column("history_id", UUID(as_uuid=True), primary_key=True, default=uuid.uuid4),
        sa.Column("client_id", UUID(as_uuid=True), sa.ForeignKey("rfq_clients.client_id"), nullable=False),
        sa.Column("request_id", sa.String(100), nullable=False),
        sa.Column("email_id", sa.String(100), nullable=True),
        sa.Column("input_text", sa.Text, nullable=False),
        sa.Column("output_json", JSONB, nullable=False),
        sa.Column("processing_time", sa.Float, nullable=False),
        sa.Column("created_at", sa.DateTime, default=datetime.utcnow, nullable=False),
    )


def create_performance_indexes() -> None:
    """Create indexes for better query performance."""
    print("📊 Creating performance indexes...")

    # Client lookups
    op.create_index("idx_rfq_clients_code", "rfq_clients", ["client_code"])
    op.create_index("idx_rfq_clients_status", "rfq_clients", ["status"])

    # Instruction schemas lookups
    op.create_index("idx_rfq_instruction_client_type", "rfq_instruction_schemas", ["client_id", "type"])
    op.create_index("idx_rfq_instruction_agent_version", "rfq_instruction_schemas", ["agent_prefix", "version"])
    op.create_index("idx_rfq_instruction_type_active", "rfq_instruction_schemas", ["type", "is_active"])
    op.create_index("idx_rfq_instruction_version", "rfq_instruction_schemas", ["version"])

    # Configuration lookups
    op.create_index("idx_rfq_config_client", "rfq_client_configurations", ["client_id"])
    op.create_index("idx_rfq_config_version", "rfq_client_configurations", ["version"])

    # Reference data lookups
    op.create_index("idx_rfq_reference_client_type", "rfq_reference_data", ["client_id", "catalog_type_id"])
    op.create_index("idx_rfq_reference_code", "rfq_reference_data", ["code"])
    op.create_index("idx_rfq_reference_active", "rfq_reference_data", ["is_active"])

    # Compatibility lookups
    op.create_index("idx_rfq_compatibility_codes", "rfq_grade_coating_compatibility", ["grade_code", "coating_code"])
    op.create_index("idx_rfq_compatibility_client", "rfq_grade_coating_compatibility", ["client_id"])

    # Processing history lookups
    op.create_index("idx_rfq_history_client", "rfq_processing_history", ["client_id"])
    op.create_index("idx_rfq_history_request", "rfq_processing_history", ["request_id"])
    op.create_index("idx_rfq_history_created", "rfq_processing_history", ["created_at"])


def downgrade() -> None:
    """Drop all RFQ tables."""
    print("🗑️  Dropping all RFQ tables...")
    drop_tables_if_exist()
    print("✅ All RFQ tables dropped!")
