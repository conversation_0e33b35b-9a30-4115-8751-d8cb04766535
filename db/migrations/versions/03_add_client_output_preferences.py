"""Add client output preferences to support CLIENT_CONFIGURATION_SCHEMA

Revision ID: 03_add_client_output_preferences
Revises: 02_populate_dev_data
Create Date: 2025-07-14 15:00:00.000000

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects.postgresql import JSONB

from utils.rfq.schemas import formatter_schema

DEFAULT_FORMATTER_CONFIGURATION = formatter_schema.CLIENT_CONFIGURATION_SCHEMA


# revision identifiers, used by Alembic.
revision = "03_add_client_output_preferences"
down_revision = "02_populate_dev_data"
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Add client_output_preferences column and populate with default values."""
    print("🔧 Adding client_output_preferences column...")

    # Add the new column
    op.add_column("rfq_client_configurations", sa.Column("client_output_preferences", JSONB, nullable=True))

    # Update existing records with default configuration
    import json

    config_json = json.dumps(DEFAULT_FORMATTER_CONFIGURATION)

    op.execute(
        sa.text(f"""
        UPDATE rfq_client_configurations
        SET client_output_preferences = '{config_json}'::jsonb
        WHERE client_output_preferences IS NULL
        """)
    )

    print("✅ Successfully added and populated client_output_preferences")
    print("   - Column added to rfq_client_configurations table")
    if DEFAULT_FORMATTER_CONFIGURATION:
        print(f"   - {len(DEFAULT_FORMATTER_CONFIGURATION)} configuration keys populated")
        output_prefs = DEFAULT_FORMATTER_CONFIGURATION.get("output_preferences", {})
        format_type = output_prefs.get("format_type", "unknown") if output_prefs else "unknown"
        print(f"   - Default format_type: {format_type}")
    else:
        print("   - No configuration keys found")


def downgrade() -> None:
    """Remove client_output_preferences column."""
    print("🔄 Removing client_output_preferences column...")

    op.drop_column("rfq_client_configurations", "client_output_preferences")

    print("✅ Successfully removed client_output_preferences column")
