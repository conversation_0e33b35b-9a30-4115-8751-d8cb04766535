"""
Reference data catalogs for steel RFQ processing.
"""

from .certificate_catalog import CERTIFICATE_DATA
from .coating_catalog import COATING_DATA
from .finish_catalog import FINISH_DATA
from .surface_protection_catalog import SURFACE_PROTECTION_DATA
from .surface_type_catalog import SURFACE_TYPE_DATA

# Combined reference data
ALL_REFERENCE_DATA = FINISH_DATA + COATING_DATA + CERTIFICATE_DATA + SURFACE_TYPE_DATA + SURFACE_PROTECTION_DATA

__all__ = ["FINISH_DATA", "COATING_DATA", "CERTIFICATE_DATA", "SURFACE_TYPE_DATA", "SURFACE_PROTECTION_DATA", "ALL_REFERENCE_DATA"]
