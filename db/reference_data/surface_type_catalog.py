"""
Steel surface type reference data for RFQ processing.
"""

from typing import Any

SURFACE_TYPE_DATA: list[dict[str, Any]] = [
    {
        "catalog_type": "surface_type",
        "code": "T1",
        "name": "TS1",
        "description": "Standard surface type designation T1",
        "properties": {"surface_category": "standard", "quality_level": "basic", "applications": ["general_purpose"]},
    },
    {
        "catalog_type": "surface_type",
        "code": "U",
        "name": "Unexposed (intenal parts) (U)",
        "description": "Surface for unexposed internal parts with basic requirements",
        "properties": {
            "surface_category": "unexposed",
            "quality_level": "internal",
            "visibility": "hidden",
            "applications": ["internal_components", "structural_parts"],
        },
    },
    {
        "catalog_type": "surface_type",
        "code": "E",
        "name": "Exposed (external parts) (E)",
        "description": "Surface for exposed external parts requiring enhanced appearance",
        "properties": {
            "surface_category": "exposed",
            "quality_level": "external",
            "visibility": "visible",
            "applications": ["exterior_panels", "visible_components"],
        },
    },
    {
        "catalog_type": "surface_type",
        "code": "A",
        "name": "Conventional surface (A)",
        "description": "Conventional surface finish with standard quality requirements",
        "properties": {
            "surface_category": "conventional",
            "quality_level": "standard",
            "finish_grade": "A",
            "applications": ["general_applications", "structural_use"],
        },
    },
    {
        "catalog_type": "surface_type",
        "code": "B",
        "name": "Improved surface (B)",
        "description": "Improved surface finish with enhanced quality and appearance",
        "properties": {
            "surface_category": "improved",
            "quality_level": "enhanced",
            "finish_grade": "B",
            "applications": ["automotive", "appliances", "visible_parts"],
        },
    },
    {
        "catalog_type": "surface_type",
        "code": "C",
        "name": "Best surface (C)",
        "description": "Best surface finish with highest quality and superior appearance",
        "properties": {
            "surface_category": "premium",
            "quality_level": "highest",
            "finish_grade": "C",
            "applications": ["premium_automotive", "high_end_appliances", "architectural"],
        },
    },
    {
        "catalog_type": "surface_type",
        "code": "NA",
        "name": "Normal spangle, conventional surface (NA)",
        "description": "Normal spangle pattern with conventional surface finish",
        "properties": {
            "surface_category": "conventional",
            "spangle_type": "normal",
            "finish_grade": "A",
            "pattern": "regular_spangle",
            "applications": ["construction", "roofing"],
        },
    },
    {
        "catalog_type": "surface_type",
        "code": "MA",
        "name": "Minimized spangle, conventional surface (MA)",
        "description": "Minimized spangle pattern with conventional surface finish for better paintability",
        "properties": {
            "surface_category": "conventional",
            "spangle_type": "minimized",
            "finish_grade": "A",
            "pattern": "minimal_spangle",
            "applications": ["painted_applications", "automotive"],
        },
    },
    {
        "catalog_type": "surface_type",
        "code": "MB",
        "name": "Minimized spangle, improved surface (MB)",
        "description": "Minimized spangle pattern with improved surface finish for superior paint adhesion",
        "properties": {
            "surface_category": "improved",
            "spangle_type": "minimized",
            "finish_grade": "B",
            "pattern": "minimal_spangle",
            "applications": ["automotive_body", "appliances"],
        },
    },
    {
        "catalog_type": "surface_type",
        "code": "MC",
        "name": "Minimized spangle, best surface (MC)",
        "description": "Minimized spangle pattern with best surface finish for premium applications",
        "properties": {
            "surface_category": "premium",
            "spangle_type": "minimized",
            "finish_grade": "C",
            "pattern": "minimal_spangle",
            "applications": ["premium_automotive", "high_end_products"],
        },
    },
    {
        "catalog_type": "surface_type",
        "code": "RA",
        "name": "Regular zinc-iron alloy coating, conventional surface (RA)",
        "description": "Regular zinc-iron alloy coating with conventional surface finish",
        "properties": {
            "surface_category": "conventional",
            "coating_type": "zinc_iron_alloy",
            "finish_grade": "A",
            "applications": ["automotive", "painted_parts"],
        },
    },
    {
        "catalog_type": "surface_type",
        "code": "RB",
        "name": "Regular zinc-iron alloy coating, improved surface (RB)",
        "description": "Regular zinc-iron alloy coating with improved surface finish for enhanced paintability",
        "properties": {
            "surface_category": "improved",
            "coating_type": "zinc_iron_alloy",
            "finish_grade": "B",
            "applications": ["automotive_body", "appliances"],
        },
    },
    {
        "catalog_type": "surface_type",
        "code": "RC",
        "name": "Regular zinc-iron alloy coating, best surface (RC)",
        "description": "Regular zinc-iron alloy coating with best surface finish for premium applications",
        "properties": {
            "surface_category": "premium",
            "coating_type": "zinc_iron_alloy",
            "finish_grade": "C",
            "applications": ["premium_automotive", "high_quality_painted_parts"],
        },
    },
    {
        "catalog_type": "surface_type",
        "code": "b",
        "name": "Bright (b)",
        "description": "Bright surface finish with high reflectivity and smooth appearance",
        "properties": {
            "surface_category": "bright",
            "appearance": "bright",
            "reflectivity": "high",
            "applications": ["decorative", "architectural", "appliances"],
        },
    },
    {
        "catalog_type": "surface_type",
        "code": "g",
        "name": "Semi-bright (g)",
        "description": "Semi-bright surface finish with moderate reflectivity",
        "properties": {
            "surface_category": "semi_bright",
            "appearance": "semi_bright",
            "reflectivity": "moderate",
            "applications": ["general_decorative", "commercial"],
        },
    },
    {
        "catalog_type": "surface_type",
        "code": "m",
        "name": "Normal (m)",
        "description": "Normal surface finish with standard appearance",
        "properties": {
            "surface_category": "normal",
            "appearance": "standard",
            "reflectivity": "normal",
            "applications": ["general_purpose", "structural"],
        },
    },
    {
        "catalog_type": "surface_type",
        "code": "r",
        "name": "Rough (r)",
        "description": "Rough surface finish with increased surface area",
        "properties": {
            "surface_category": "rough",
            "appearance": "textured",
            "texture": "rough",
            "applications": ["non_slip", "adhesion_enhancement"],
        },
    },
    {
        "catalog_type": "surface_type",
        "code": "Ab",
        "name": "Conventional surface, bright (Ab)",
        "description": "Conventional surface quality with bright finish",
        "properties": {
            "surface_category": "conventional",
            "appearance": "bright",
            "finish_grade": "A",
            "reflectivity": "high",
            "applications": ["decorative", "visible_parts"],
        },
    },
    {
        "catalog_type": "surface_type",
        "code": "Ag",
        "name": "Conventional surface, semi-bright (Ag)",
        "description": "Conventional surface quality with semi-bright finish",
        "properties": {
            "surface_category": "conventional",
            "appearance": "semi_bright",
            "finish_grade": "A",
            "reflectivity": "moderate",
            "applications": ["general_decorative"],
        },
    },
    {
        "catalog_type": "surface_type",
        "code": "Am",
        "name": "Conventional surface, normal (Am)",
        "description": "Conventional surface quality with normal finish",
        "properties": {
            "surface_category": "conventional",
            "appearance": "standard",
            "finish_grade": "A",
            "reflectivity": "normal",
            "applications": ["general_purpose"],
        },
    },
    {
        "catalog_type": "surface_type",
        "code": "Ar",
        "name": "Conventional surface, rough (Ar)",
        "description": "Conventional surface quality with rough finish",
        "properties": {
            "surface_category": "conventional",
            "appearance": "textured",
            "finish_grade": "A",
            "texture": "rough",
            "applications": ["non_slip_applications"],
        },
    },
    {
        "catalog_type": "surface_type",
        "code": "Bb",
        "name": "Improved surface, bright (Bb)",
        "description": "Improved surface quality with bright finish for enhanced appearance",
        "properties": {
            "surface_category": "improved",
            "appearance": "bright",
            "finish_grade": "B",
            "reflectivity": "high",
            "applications": ["automotive", "premium_decorative"],
        },
    },
    {
        "catalog_type": "surface_type",
        "code": "Bg",
        "name": "Improved surface, semi-bright (Bg)",
        "description": "Improved surface quality with semi-bright finish",
        "properties": {
            "surface_category": "improved",
            "appearance": "semi_bright",
            "finish_grade": "B",
            "reflectivity": "moderate",
            "applications": ["automotive", "appliances"],
        },
    },
    {
        "catalog_type": "surface_type",
        "code": "Bm",
        "name": "Improved surface, normal (Bm)",
        "description": "Improved surface quality with normal finish",
        "properties": {
            "surface_category": "improved",
            "appearance": "standard",
            "finish_grade": "B",
            "reflectivity": "normal",
            "applications": ["automotive_structural"],
        },
    },
    {
        "catalog_type": "surface_type",
        "code": "Br",
        "name": "Improved surface, rough (Br)",
        "description": "Improved surface quality with rough finish for specialized applications",
        "properties": {
            "surface_category": "improved",
            "appearance": "textured",
            "finish_grade": "B",
            "texture": "rough",
            "applications": ["adhesion_critical"],
        },
    },
    {
        "catalog_type": "surface_type",
        "code": "Cb",
        "name": "Best surface, bright (Cb)",
        "description": "Best surface quality with bright finish for premium applications",
        "properties": {
            "surface_category": "premium",
            "appearance": "bright",
            "finish_grade": "C",
            "reflectivity": "high",
            "applications": ["premium_automotive", "luxury_appliances"],
        },
    },
    {
        "catalog_type": "surface_type",
        "code": "Cg",
        "name": "Best surface, semi-bright (Cg)",
        "description": "Best surface quality with semi-bright finish",
        "properties": {
            "surface_category": "premium",
            "appearance": "semi_bright",
            "finish_grade": "C",
            "reflectivity": "moderate",
            "applications": ["high_end_automotive", "premium_products"],
        },
    },
    {
        "catalog_type": "surface_type",
        "code": "Cm",
        "name": "Best surface, normal (Cm)",
        "description": "Best surface quality with normal finish",
        "properties": {
            "surface_category": "premium",
            "appearance": "standard",
            "finish_grade": "C",
            "reflectivity": "normal",
            "applications": ["premium_structural"],
        },
    },
    {
        "catalog_type": "surface_type",
        "code": "Cr",
        "name": "Best surface, rough (Cr)",
        "description": "Best surface quality with rough finish for specialized premium applications",
        "properties": {
            "surface_category": "premium",
            "appearance": "textured",
            "finish_grade": "C",
            "texture": "rough",
            "applications": ["premium_adhesion_critical"],
        },
    },
    {
        "catalog_type": "surface_type",
        "code": "MA-RL",
        "name": "Minimized spangle, conventional surface, smooth finish (MA-RL)",
        "description": "Minimized spangle with conventional surface and smooth finish for paint applications",
        "properties": {
            "surface_category": "conventional",
            "spangle_type": "minimized",
            "finish_grade": "A",
            "surface_finish": "smooth",
            "applications": ["automotive_painting", "smooth_coatings"],
        },
    },
    {
        "catalog_type": "surface_type",
        "code": "MA-RM",
        "name": "Minimized spangle, conventional surface, matte finish (MA-RM)",
        "description": "Minimized spangle with conventional surface and matte finish",
        "properties": {
            "surface_category": "conventional",
            "spangle_type": "minimized",
            "finish_grade": "A",
            "surface_finish": "matte",
            "applications": ["matte_coatings", "non_reflective"],
        },
    },
    {
        "catalog_type": "surface_type",
        "code": "MA-RR",
        "name": "Minimized spangle, conventional surface, rough finish (MA-RR)",
        "description": "Minimized spangle with conventional surface and rough finish for enhanced adhesion",
        "properties": {
            "surface_category": "conventional",
            "spangle_type": "minimized",
            "finish_grade": "A",
            "surface_finish": "rough",
            "applications": ["adhesion_enhancement", "textured_coatings"],
        },
    },
    {
        "catalog_type": "surface_type",
        "code": "MB-RL",
        "name": "Minimized spangle, improved surface, smooth finish (MB-RL)",
        "description": "Minimized spangle with improved surface and smooth finish for high-quality paint applications",
        "properties": {
            "surface_category": "improved",
            "spangle_type": "minimized",
            "finish_grade": "B",
            "surface_finish": "smooth",
            "applications": ["high_quality_painting", "automotive_body"],
        },
    },
    {
        "catalog_type": "surface_type",
        "code": "MACE",
        "name": "Minimized spangle, Conventional surface, Enhanced surface protection (MACE)",
        "description": "Minimized spangle with conventional surface and enhanced protection for demanding environments",
        "properties": {
            "surface_category": "conventional",
            "spangle_type": "minimized",
            "finish_grade": "A",
            "protection": "enhanced",
            "applications": ["corrosive_environments", "marine_applications"],
        },
    },
    {
        "catalog_type": "surface_type",
        "code": "MBCE",
        "name": "Minimized spangle, Improved surface, Enhanced surface protection (MBCE)",
        "description": "Minimized spangle with improved surface and enhanced protection for premium demanding environments",
        "properties": {
            "surface_category": "improved",
            "spangle_type": "minimized",
            "finish_grade": "B",
            "protection": "enhanced",
            "applications": ["premium_corrosive_environments", "high_end_marine"],
        },
    },
    {
        "catalog_type": "surface_type",
        "code": "MB-RM",
        "name": "Minimized spangle, improved surface, matte finish (MB-RM)",
        "description": "Minimized spangle with improved surface and matte finish for premium non-reflective applications",
        "properties": {
            "surface_category": "improved",
            "spangle_type": "minimized",
            "finish_grade": "B",
            "surface_finish": "matte",
            "applications": ["premium_matte_coatings", "architectural"],
        },
    },
    {
        "catalog_type": "surface_type",
        "code": "MC-RN",
        "name": "Minimized spangle, best surface, bright finish (MC-RN)",
        "description": "Minimized spangle with best surface and bright finish for premium bright applications",
        "properties": {
            "surface_category": "premium",
            "spangle_type": "minimized",
            "finish_grade": "C",
            "surface_finish": "bright",
            "applications": ["luxury_automotive", "premium_bright_coatings"],
        },
    },
]


def get_surface_type_data() -> list[dict[str, Any]]:
    return SURFACE_TYPE_DATA


def get_surface_type_by_name(name: str) -> dict[str, Any] | None:
    for surface_type in SURFACE_TYPE_DATA:
        if surface_type["name"] == name:
            return surface_type
    return None


def get_surface_type_by_code(code: str) -> dict[str, Any] | None:
    for surface_type in SURFACE_TYPE_DATA:
        if surface_type["code"] == code:
            return surface_type
    return None


def get_surface_types_by_category(category: str) -> list[dict[str, Any]]:
    return [st for st in SURFACE_TYPE_DATA if st.get("properties", {}).get("surface_category") == category]


def get_surface_types_by_finish_grade(grade: str) -> list[dict[str, Any]]:
    return [st for st in SURFACE_TYPE_DATA if st.get("properties", {}).get("finish_grade") == grade]


def get_surface_types_by_spangle(spangle_type: str) -> list[dict[str, Any]]:
    return [st for st in SURFACE_TYPE_DATA if st.get("properties", {}).get("spangle_type") == spangle_type]


def get_surface_categories() -> list[str]:
    categories = set()
    for surface_type in SURFACE_TYPE_DATA:
        category = surface_type.get("properties", {}).get("surface_category")
        if category:
            categories.add(category)
    return sorted(list(categories))


def get_finish_grades() -> list[str]:
    grades = set()
    for surface_type in SURFACE_TYPE_DATA:
        grade = surface_type.get("properties", {}).get("finish_grade")
        if grade:
            grades.add(grade)
    return sorted(list(grades))


def get_spangle_types() -> list[str]:
    spangle_types = set()
    for surface_type in SURFACE_TYPE_DATA:
        spangle_type = surface_type.get("properties", {}).get("spangle_type")
        if spangle_type:
            spangle_types.add(spangle_type)
    return sorted(list(spangle_types))


__all__ = [
    "SURFACE_TYPE_DATA",
    "get_surface_type_data",
    "get_surface_type_by_name",
    "get_surface_type_by_code",
    "get_surface_types_by_category",
    "get_surface_types_by_finish_grade",
    "get_surface_types_by_spangle",
    "get_surface_categories",
    "get_finish_grades",
    "get_spangle_types",
]
