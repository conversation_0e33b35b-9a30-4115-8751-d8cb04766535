"""
Steel certificate reference data for RFQ processing.
"""

from typing import Any

CERTIFICATE_DATA: list[dict[str, Any]] = [
    {
        "catalog_type": "certificate",
        "code": "3.1C_GL",
        "name": "3.1C GL",
        "description": "Type 3.1C certificate with Germanischer Lloyd classification society approval",
        "properties": {
            "cert_type": "inspection",
            "classification_society": "GL",
            "standard": "EN 10204",
            "includes": ["chemical_analysis", "mechanical_properties", "inspection_results"],
        },
    },
    {
        "catalog_type": "certificate",
        "code": "3.2",
        "name": "3.2",
        "description": "Type 3.2 certificate - inspection by authorized inspector independent of manufacturer",
        "properties": {
            "cert_type": "inspection",
            "standard": "EN 10204",
            "inspector": "independent",
            "includes": ["chemical_analysis", "mechanical_properties", "dimensional_check"],
        },
    },
    {
        "catalog_type": "certificate",
        "code": "3.2_LRS",
        "name": "3.2 LRS",
        "description": "Type 3.2 certificate with Lloyd's Register of Shipping classification society approval",
        "properties": {
            "cert_type": "inspection",
            "classification_society": "LRS",
            "standard": "EN 10204",
            "includes": ["chemical_analysis", "mechanical_properties", "inspection_results"],
        },
    },
    {
        "catalog_type": "certificate",
        "code": "2.1",
        "name": "2.1",
        "description": "Type 2.1 certificate - test report based on non-specific inspection",
        "properties": {
            "cert_type": "test_report",
            "standard": "EN 10204",
            "inspection_type": "non_specific",
            "includes": ["routine_tests", "production_records"],
        },
    },
    {
        "catalog_type": "certificate",
        "code": "CHEM_ANALYSIS",
        "name": "Chemical analysis sheet",
        "description": "Detailed chemical composition analysis certificate",
        "properties": {"cert_type": "analysis", "test_type": "chemical", "includes": ["elemental_composition", "trace_elements", "impurities"]},
    },
    {
        "catalog_type": "certificate",
        "code": "3.1",
        "name": "3.1",
        "description": "Type 3.1 certificate - inspection by manufacturer's authorized inspector",
        "properties": {
            "cert_type": "inspection",
            "standard": "EN 10204",
            "inspector": "manufacturer_authorized",
            "includes": ["chemical_analysis", "mechanical_properties"],
        },
    },
    {
        "catalog_type": "certificate",
        "code": "3.1_EUR1",
        "name": "3.1 & EUR1",
        "description": "Type 3.1 certificate with EUR.1 movement certificate for preferential trade",
        "properties": {
            "cert_type": "inspection",
            "standard": "EN 10204",
            "trade_document": "EUR1",
            "includes": ["chemical_analysis", "mechanical_properties", "origin_certificate"],
        },
    },
    {
        "catalog_type": "certificate",
        "code": "2.1_EUR1",
        "name": "2.1 & EUR1",
        "description": "Type 2.1 certificate with EUR.1 movement certificate for preferential trade",
        "properties": {
            "cert_type": "test_report",
            "standard": "EN 10204",
            "trade_document": "EUR1",
            "includes": ["routine_tests", "origin_certificate"],
        },
    },
    {
        "catalog_type": "certificate",
        "code": "ATR",
        "name": "ATR",
        "description": "ATR movement certificate for preferential trade within EFTA countries",
        "properties": {
            "cert_type": "trade_document",
            "document_type": "ATR",
            "trade_agreement": "EFTA",
            "includes": ["origin_verification", "preferential_treatment"],
        },
    },
    {
        "catalog_type": "certificate",
        "code": "INSPECT_CERT",
        "name": "Inspection certificate (chemical and mechanical values)",
        "description": "Comprehensive inspection certificate with chemical and mechanical test results",
        "properties": {
            "cert_type": "inspection",
            "test_scope": "comprehensive",
            "includes": ["chemical_analysis", "mechanical_properties", "dimensional_verification"],
        },
    },
    {
        "catalog_type": "certificate",
        "code": "LTSD_EUR1",
        "name": "LTSD/EUR.1",
        "description": "Long Term Supply Declaration with EUR.1 movement certificate",
        "properties": {
            "cert_type": "trade_document",
            "document_type": "LTSD",
            "trade_document": "EUR1",
            "includes": ["supply_declaration", "origin_certificate"],
        },
    },
    {
        "catalog_type": "certificate",
        "code": "COO",
        "name": "Certificate of Origin (not EUR. 1)",
        "description": "Standard certificate of origin (non-preferential trade)",
        "properties": {
            "cert_type": "trade_document",
            "document_type": "certificate_of_origin",
            "trade_type": "non_preferential",
            "includes": ["origin_verification"],
        },
    },
    {
        "catalog_type": "certificate",
        "code": "3.2_EUR1",
        "name": "3.2 & EUR1",
        "description": "Type 3.2 certificate with EUR.1 movement certificate for preferential trade",
        "properties": {
            "cert_type": "inspection",
            "standard": "EN 10204",
            "trade_document": "EUR1",
            "includes": ["chemical_analysis", "mechanical_properties", "origin_certificate"],
        },
    },
    {
        "catalog_type": "certificate",
        "code": "3.2_DNV",
        "name": "3.2 DNV",
        "description": "Type 3.2 certificate with Det Norske Veritas classification society approval",
        "properties": {
            "cert_type": "inspection",
            "classification_society": "DNV",
            "standard": "EN 10204",
            "includes": ["chemical_analysis", "mechanical_properties", "inspection_results"],
        },
    },
    {
        "catalog_type": "certificate",
        "code": "2.2",
        "name": "2.2",
        "description": "Type 2.2 certificate - test report based on specific inspection",
        "properties": {
            "cert_type": "test_report",
            "standard": "EN 10204",
            "inspection_type": "specific",
            "includes": ["specific_tests", "batch_records"],
        },
    },
    {
        "catalog_type": "certificate",
        "code": "DATASHEET",
        "name": "Datasheet",
        "description": "Technical datasheet with material specifications and properties",
        "properties": {
            "cert_type": "technical_document",
            "document_type": "datasheet",
            "includes": ["material_specifications", "technical_properties", "performance_data"],
        },
    },
    {
        "catalog_type": "certificate",
        "code": "3.1C",
        "name": "3.1C",
        "description": "Type 3.1C certificate - inspection by manufacturer with specific certification requirements",
        "properties": {
            "cert_type": "inspection",
            "standard": "EN 10204",
            "inspector": "manufacturer_certified",
            "includes": ["chemical_analysis", "mechanical_properties", "special_requirements"],
        },
    },
    {
        "catalog_type": "certificate",
        "code": "3.1A",
        "name": "3.1A",
        "description": "Type 3.1A certificate - inspection by manufacturer's authorized inspector with validation",
        "properties": {
            "cert_type": "inspection",
            "standard": "EN 10204",
            "inspector": "manufacturer_authorized",
            "validation": "required",
            "includes": ["chemical_analysis", "mechanical_properties", "validation_records"],
        },
    },
    {
        "catalog_type": "certificate",
        "code": "3.2_GL",
        "name": "3.2 GL",
        "description": "Type 3.2 certificate with Germanischer Lloyd classification society approval",
        "properties": {
            "cert_type": "inspection",
            "classification_society": "GL",
            "standard": "EN 10204",
            "includes": ["chemical_analysis", "mechanical_properties", "inspection_results"],
        },
    },
    {
        "catalog_type": "certificate",
        "code": "3.1B",
        "name": "3.1B",
        "description": "Type 3.1B certificate - inspection by manufacturer with batch-specific validation",
        "properties": {
            "cert_type": "inspection",
            "standard": "EN 10204",
            "inspector": "manufacturer",
            "validation": "batch_specific",
            "includes": ["chemical_analysis", "mechanical_properties", "batch_validation"],
        },
    },
    {
        "catalog_type": "certificate",
        "code": "2.2_EUR1",
        "name": "2.2 & EUR1",
        "description": "Type 2.2 certificate with EUR.1 movement certificate for preferential trade",
        "properties": {
            "cert_type": "test_report",
            "standard": "EN 10204",
            "trade_document": "EUR1",
            "includes": ["specific_tests", "origin_certificate"],
        },
    },
]


def get_certificate_data() -> list[dict[str, Any]]:
    return CERTIFICATE_DATA


def get_certificate_by_name(name: str) -> dict[str, Any] | None:
    for certificate in CERTIFICATE_DATA:
        if certificate["name"] == name:
            return certificate
    return None


def get_certificate_by_code(code: str) -> dict[str, Any] | None:
    for certificate in CERTIFICATE_DATA:
        if certificate["code"] == code:
            return certificate
    return None


def get_certificates_by_type(cert_type: str) -> list[dict[str, Any]]:
    return [cert for cert in CERTIFICATE_DATA if cert.get("properties", {}).get("cert_type") == cert_type]


def get_certificate_types() -> list[str]:
    cert_types = set()
    for certificate in CERTIFICATE_DATA:
        cert_type = certificate.get("properties", {}).get("cert_type")
        if cert_type:
            cert_types.add(cert_type)
    return sorted(list(cert_types))


def get_certificates_with_trade_documents() -> list[dict[str, Any]]:
    return [cert for cert in CERTIFICATE_DATA if "trade_document" in cert.get("properties", {})]


def get_classification_society_certificates() -> list[dict[str, Any]]:
    return [cert for cert in CERTIFICATE_DATA if "classification_society" in cert.get("properties", {})]


__all__ = [
    "CERTIFICATE_DATA",
    "get_certificate_data",
    "get_certificate_by_name",
    "get_certificate_by_code",
    "get_certificates_by_type",
    "get_certificate_types",
    "get_certificates_with_trade_documents",
    "get_classification_society_certificates",
]
