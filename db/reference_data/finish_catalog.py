"""
Steel finish reference data for RFQ processing.
"""

from typing import Any

FINISH_DATA: list[dict[str, Any]] = [
    {
        "catalog_type": "finish",
        "code": "+A",
        "name": "Soft Annealed (+A)",
        "description": "Heat treatment process that softens steel",
        "properties": {"finish_type": "heat_treatment", "process": "annealing"},
    },
    {
        "catalog_type": "finish",
        "code": "+AR",
        "name": "As Rolled (+AR)",
        "description": "Steel delivered as-rolled from mill",
        "properties": {"finish_type": "heat_treatment", "process": "hot_rolling"},
    },
    {
        "catalog_type": "finish",
        "code": "+AT",
        "name": "Solution Annealed (+AT)",
        "description": "Heat treatment for stainless steels",
        "properties": {"finish_type": "heat_treatment", "process": "solution_annealing"},
    },
    {
        "catalog_type": "finish",
        "code": "+C",
        "name": "Cold Drawn / Hard (+C)",
        "description": "Cold worked for increased hardness",
        "properties": {"finish_type": "heat_treatment", "process": "cold_drawing"},
    },
    {
        "catalog_type": "finish",
        "code": "+LC",
        "name": "Cold Drawn / Soft (+LC)",
        "description": "Cold drawn then annealed",
        "properties": {"finish_type": "heat_treatment", "process": "cold_drawing_and_annealing"},
    },
    {
        "catalog_type": "finish",
        "code": "Dry",
        "name": "Dry",
        "description": "Delivered in dry condition",
        "properties": {"finish_type": "delivery_condition", "process": "drying"},
    },
    {
        "catalog_type": "finish",
        "code": "PPGI",
        "name": "Prepainted (PPGI)",
        "description": "Pre-painted galvanized iron",
        "properties": {"finish_type": "coating", "process": "prepainted_galvanized"},
    },
    {
        "catalog_type": "finish",
        "code": "Cold Drawn",
        "name": "Cold Drawn",
        "description": "Cold drawn for improved surface",
        "properties": {"finish_type": "mechanical_treatment", "process": "cold_drawing"},
    },
    {
        "catalog_type": "finish",
        "code": "+M",
        "name": "Thermomechanically Rolled (+M)",
        "description": "Controlled rolling process",
        "properties": {"finish_type": "heat_treatment", "process": "thermomechanical_rolling"},
    },
    {
        "catalog_type": "finish",
        "code": "+N",
        "name": "Normalized (+N)",
        "description": "Heat treatment for grain refinement",
        "properties": {"finish_type": "heat_treatment", "process": "normalizing"},
    },
    {
        "catalog_type": "finish",
        "code": "+PE",
        "name": "Peeled (+PE)",
        "description": "Surface layer removed for precision",
        "properties": {"finish_type": "surface_treatment", "process": "peeling"},
    },
    {
        "catalog_type": "finish",
        "code": "+QT",
        "name": "Quenched and Tempered (+QT)",
        "description": "Heat treatment for strength and toughness",
        "properties": {"finish_type": "heat_treatment", "process": "quenching_and_tempering"},
    },
    {
        "catalog_type": "finish",
        "code": "+U",
        "name": "Untreated (+U)",
        "description": "No specific treatment",
        "properties": {"finish_type": "heat_treatment", "process": "none"},
    },
    {
        "catalog_type": "finish",
        "code": "As Cast",
        "name": "As Cast",
        "description": "As-cast condition",
        "properties": {"finish_type": "delivery_condition", "process": "casting"},
    },
    {
        "catalog_type": "finish",
        "code": "Cold Formed",
        "name": "Cold Formed",
        "description": "Shaped at room temperature",
        "properties": {"finish_type": "mechanical_treatment", "process": "cold_forming"},
    },
    {
        "catalog_type": "finish",
        "code": "Full Hard",
        "name": "Full Hard",
        "description": "Maximum cold work condition",
        "properties": {"finish_type": "mechanical_treatment", "process": "cold_working"},
    },
    {
        "catalog_type": "finish",
        "code": "Teardrop",
        "name": "Teardrop pattern",
        "description": "Anti-slip surface pattern",
        "properties": {"finish_type": "surface_pattern", "process": "embossing"},
    },
    {
        "catalog_type": "finish",
        "code": "SVA",
        "name": "SuperVitac (SVA)",
        "description": "Specialized coating system",
        "properties": {"finish_type": "specialty_coating", "process": "proprietary_treatment"},
    },
    {
        "catalog_type": "finish",
        "code": "+S1",
        "name": "Black (S1)",
        "description": "Black oxide treatment",
        "properties": {"finish_type": "surface_treatment", "process": "black_oxide"},
    },
    {
        "catalog_type": "finish",
        "code": "AZ",
        "name": "Aluzinc (+AZ)",
        "description": "Aluminum-zinc alloy coating",
        "properties": {"finish_type": "metallic_coating", "process": "hot_dip_coating"},
    },
    {
        "catalog_type": "finish",
        "code": "AS/AL",
        "name": "Aluminized (+AS/+AL)",
        "description": "Aluminum coating",
        "properties": {"finish_type": "metallic_coating", "process": "hot_dip_aluminizing"},
    },
    {
        "catalog_type": "finish",
        "code": "ZA",
        "name": "Galfan (+ZA)",
        "description": "Zinc-aluminum alloy coating",
        "properties": {"finish_type": "metallic_coating", "process": "hot_dip_coating"},
    },
    {
        "catalog_type": "finish",
        "code": "Z/GI",
        "name": "Hot-dip Galvanized (+Z/+GI)",
        "description": "Zinc coating for corrosion protection",
        "properties": {"finish_type": "metallic_coating", "process": "hot_dip_galvanizing"},
    },
    {
        "catalog_type": "finish",
        "code": "ZM",
        "name": "Hot-dip zinc magnesium (+ZM)",
        "description": "Zinc-magnesium coating",
        "properties": {"finish_type": "metallic_coating", "process": "hot_dip_coating"},
    },
    {
        "catalog_type": "finish",
        "code": "+S2",
        "name": "Pickled (S2)",
        "description": "Acid treatment to remove scale",
        "properties": {"finish_type": "surface_treatment", "process": "acid_pickling"},
    },
    {
        "catalog_type": "finish",
        "code": "No. 1",
        "name": "ASTM No. 1",
        "description": "Dull, non-reflective finish",
        "properties": {"finish_type": "surface_quality", "process": "hot_rolling_annealing_pickling"},
    },
    {
        "catalog_type": "finish",
        "code": "BA",
        "name": "ASTM BA",
        "description": "Bright annealed finish",
        "properties": {"finish_type": "surface_quality", "process": "bright_annealing"},
    },
    {
        "catalog_type": "finish",
        "code": "No. 4",
        "name": "ASTM No. 4",
        "description": "Brushed finish",
        "properties": {"finish_type": "surface_quality", "process": "mechanical_brushing"},
    },
    {
        "catalog_type": "finish",
        "code": "Bright",
        "name": "Bright (BR)",
        "description": "Bright surface finish",
        "properties": {"finish_type": "surface_quality", "process": "mechanical_polishing"},
    },
    {
        "catalog_type": "finish",
        "code": "Polished",
        "name": "Polished",
        "description": "Mirror-like finish",
        "properties": {"finish_type": "surface_quality", "process": "mechanical_polishing"},
    },
    {
        "catalog_type": "finish",
        "code": "K320",
        "name": "K320",
        "description": "320 grit mechanical finish",
        "properties": {"finish_type": "surface_quality", "process": "mechanical_grinding"},
    },
    {
        "catalog_type": "finish",
        "code": "ZE",
        "name": "Electro-Galvanized (+ZE)",
        "description": "Electroplated zinc coating",
        "properties": {"finish_type": "metallic_coating", "process": "electroplating"},
    },
    {
        "catalog_type": "finish",
        "code": "HR",
        "name": "Hot Rolled",
        "description": "Hot rolled condition",
        "properties": {"finish_type": "delivery_condition", "process": "hot_rolling"},
    },
    {
        "catalog_type": "finish",
        "code": "PVC 1",
        "name": "PVC 1",
        "description": "PVC coating",
        "properties": {"finish_type": "organic_coating", "process": "pvc_coating"},
    },
    {
        "catalog_type": "finish",
        "code": "FACE",
        "name": "FACE",
        "description": "Visible surface designation",
        "properties": {"finish_type": "surface_designation", "process": "surface_specification"},
    },
    {
        "catalog_type": "finish",
        "code": "2H",
        "name": "2h",
        "description": "Hardness designation",
        "properties": {"finish_type": "temper_designation", "process": "controlled_treatment"},
    },
    {
        "catalog_type": "finish",
        "code": "Chequered",
        "name": "Chequered",
        "description": "Checker plate pattern",
        "properties": {"finish_type": "surface_pattern", "process": "embossing_or_rolling"},
    },
    {
        "catalog_type": "finish",
        "code": "Metal Coated",
        "name": "Metal Coated",
        "description": "General metallic coating",
        "properties": {"finish_type": "metallic_coating", "process": "various_coating_processes"},
    },
    {
        "catalog_type": "finish",
        "code": "CR",
        "name": "Cold Rolled",
        "description": "Cold rolled for smooth surface",
        "properties": {"finish_type": "mechanical_treatment", "process": "cold_rolling"},
    },
    {
        "catalog_type": "finish",
        "code": "+ZF/+GA",
        "name": "Galvannealed (+ZF/+GA)",
        "description": "Zinc-iron alloy coating",
        "properties": {"finish_type": "metallic_coating", "process": "galvannealing"},
    },
]


def get_finish_data() -> list[dict[str, Any]]:
    return FINISH_DATA


def get_finish_by_code(code: str) -> dict[str, Any] | None:
    for finish in FINISH_DATA:
        if finish["code"] == code:
            return finish
    return None


def get_finishes_by_type(finish_type: str) -> list[dict[str, Any]]:
    return [finish for finish in FINISH_DATA if finish.get("properties", {}).get("finish_type") == finish_type]


def get_available_finish_types() -> list[str]:
    finish_types = set()
    for finish in FINISH_DATA:
        finish_type = finish.get("properties", {}).get("finish_type")
        if finish_type:
            finish_types.add(finish_type)
    return sorted(list(finish_types))


__all__ = ["FINISH_DATA", "get_finish_data", "get_finish_by_code", "get_finishes_by_type", "get_available_finish_types"]
