"""
Steel surface protection reference data for RFQ processing.
"""

from typing import Any

SURFACE_PROTECTION_DATA: list[dict[str, Any]] = [
    {
        "catalog_type": "surface_protection",
        "code": "X2",
        "name": "XIDE2",
        "description": "Advanced oxidation protection system XIDE2 for enhanced corrosion resistance",
        "properties": {
            "protection_type": "advanced_oxidation",
            "corrosion_resistance": "enhanced",
            "coating_system": "XIDE2",
            "applications": ["marine", "industrial", "harsh_environments"],
        },
    },
    {
        "catalog_type": "surface_protection",
        "code": "U",
        "name": "Without surface treatment (U)",
        "description": "No surface treatment applied - base material condition",
        "properties": {
            "protection_type": "none",
            "treatment": "untreated",
            "corrosion_resistance": "base_material",
            "applications": ["indoor", "non_corrosive_environments", "temporary_use"],
        },
    },
    {
        "catalog_type": "surface_protection",
        "code": "O",
        "name": "Oiled (O)",
        "description": "Oil coating applied for temporary corrosion protection and handling",
        "properties": {
            "protection_type": "temporary",
            "treatment": "oil_coating",
            "corrosion_resistance": "temporary",
            "storage_protection": "yes",
            "applications": ["storage", "transport", "temporary_protection"],
        },
    },
    {
        "catalog_type": "surface_protection",
        "code": "C",
        "name": "Chemically passivated (C)",
        "description": "Chemical passivation treatment to improve corrosion resistance",
        "properties": {
            "protection_type": "chemical",
            "treatment": "passivation",
            "corrosion_resistance": "improved",
            "process": "chemical_treatment",
            "applications": ["moderate_corrosion", "indoor_exposed", "general_purpose"],
        },
    },
    {
        "catalog_type": "surface_protection",
        "code": "CO",
        "name": "Chemically passivated and oiled (CO)",
        "description": "Combined chemical passivation and oil coating for enhanced protection",
        "properties": {
            "protection_type": "chemical_plus_oil",
            "treatment": "passivation_and_oil",
            "corrosion_resistance": "enhanced",
            "storage_protection": "yes",
            "applications": ["storage", "moderate_corrosion", "transport"],
        },
    },
    {
        "catalog_type": "surface_protection",
        "code": "P",
        "name": "Phosphated (P)",
        "description": "Phosphate coating treatment for improved paint adhesion and corrosion resistance",
        "properties": {
            "protection_type": "phosphate",
            "treatment": "phosphating",
            "corrosion_resistance": "good",
            "paint_adhesion": "excellent",
            "applications": ["automotive", "painted_parts", "industrial"],
        },
    },
    {
        "catalog_type": "surface_protection",
        "code": "PO",
        "name": "Phosphated and oiled (PO)",
        "description": "Phosphate treatment with oil coating for enhanced protection and paint preparation",
        "properties": {
            "protection_type": "phosphate_plus_oil",
            "treatment": "phosphating_and_oil",
            "corrosion_resistance": "very_good",
            "paint_adhesion": "excellent",
            "storage_protection": "yes",
            "applications": ["automotive", "painted_parts", "storage"],
        },
    },
    {
        "catalog_type": "surface_protection",
        "code": "PC",
        "name": "Phosphated and chemically passivated (PC)",
        "description": "Combined phosphate and chemical passivation treatment for superior protection",
        "properties": {
            "protection_type": "phosphate_plus_chemical",
            "treatment": "phosphating_and_passivation",
            "corrosion_resistance": "superior",
            "paint_adhesion": "excellent",
            "applications": ["automotive", "high_performance", "demanding_environments"],
        },
    },
    {
        "catalog_type": "surface_protection",
        "code": "PCO",
        "name": "Phosphated, chemically passivated and oiled (PCO)",
        "description": "Complete protection system with phosphating, passivation, and oil coating",
        "properties": {
            "protection_type": "complete_system",
            "treatment": "phosphating_passivation_oil",
            "corrosion_resistance": "maximum",
            "paint_adhesion": "excellent",
            "storage_protection": "yes",
            "applications": ["automotive", "premium_parts", "long_term_storage"],
        },
    },
    {
        "catalog_type": "surface_protection",
        "code": "S",
        "name": "Sealed (S)",
        "description": "Sealed surface treatment to prevent moisture and contaminant ingress",
        "properties": {
            "protection_type": "sealing",
            "treatment": "surface_sealing",
            "moisture_protection": "excellent",
            "contamination_resistance": "high",
            "applications": ["precision_parts", "electronics", "clean_environments"],
        },
    },
    {
        "catalog_type": "surface_protection",
        "code": "L",
        "name": "Lightly Oiled (L)",
        "description": "Light oil coating for minimal protection during short-term storage and handling",
        "properties": {
            "protection_type": "light_temporary",
            "treatment": "light_oil_coating",
            "corrosion_resistance": "minimal",
            "storage_duration": "short_term",
            "applications": ["short_storage", "indoor_handling", "clean_environments"],
        },
    },
    {
        "catalog_type": "surface_protection",
        "code": "PL",
        "name": "Phosphated and lightly oiled (PL)",
        "description": "Phosphate treatment with light oil coating for balanced protection and handling",
        "properties": {
            "protection_type": "phosphate_plus_light_oil",
            "treatment": "phosphating_and_light_oil",
            "corrosion_resistance": "good",
            "paint_adhesion": "excellent",
            "handling_ease": "improved",
            "applications": ["automotive", "painted_parts", "easy_handling"],
        },
    },
    {
        "catalog_type": "surface_protection",
        "code": "ACP",
        "name": "Anti-Corrosion Protection (ACP)",
        "description": "Advanced anti-corrosion protection system for severe environments",
        "properties": {
            "protection_type": "advanced_anti_corrosion",
            "treatment": "specialized_coating",
            "corrosion_resistance": "maximum",
            "environment_resistance": "severe",
            "applications": ["marine", "chemical_plants", "offshore", "extreme_environments"],
        },
    },
    {
        "catalog_type": "surface_protection",
        "code": "E",
        "name": "Exposed (E)",
        "description": "Surface prepared for exposed applications with appropriate protection level",
        "properties": {
            "protection_type": "exposure_ready",
            "treatment": "exposure_preparation",
            "visibility": "exposed",
            "weather_resistance": "suitable",
            "applications": ["exterior_parts", "architectural", "visible_components"],
        },
    },
    {
        "catalog_type": "surface_protection",
        "code": "UC",
        "name": "Uncoated",
        "description": "Uncoated surface without any protective coating or treatment",
        "properties": {
            "protection_type": "none",
            "treatment": "uncoated",
            "corrosion_resistance": "minimal",
            "surface_state": "bare_metal",
            "applications": ["indoor_dry", "further_processing", "coating_substrate"],
        },
    },
    {
        "catalog_type": "surface_protection",
        "code": "PK",
        "name": "Pickled",
        "description": "Acid pickled surface with scale and oxide removal for clean metal finish",
        "properties": {
            "protection_type": "surface_preparation",
            "treatment": "acid_pickling",
            "surface_condition": "clean_metal",
            "scale_removal": "complete",
            "applications": ["coating_preparation", "welding", "clean_surface_required"],
        },
    },
    {
        "catalog_type": "surface_protection",
        "code": "PK+O",
        "name": "Pickled and Oiled",
        "description": "Acid pickled surface with oil coating for clean finish and temporary protection",
        "properties": {
            "protection_type": "preparation_plus_temporary",
            "treatment": "pickling_and_oil",
            "surface_condition": "clean_metal",
            "corrosion_resistance": "temporary",
            "storage_protection": "yes",
            "applications": ["coating_preparation", "storage", "clean_surface_with_protection"],
        },
    },
    {
        "catalog_type": "surface_protection",
        "code": "D",
        "name": "Dry",
        "description": "Dry surface condition without oil or moisture for specific applications",
        "properties": {
            "protection_type": "dry_condition",
            "treatment": "moisture_removal",
            "surface_state": "dry",
            "oil_free": "yes",
            "applications": ["welding", "precise_coating", "clean_assembly", "moisture_sensitive"],
        },
    },
]


def get_surface_protection_data() -> list[dict[str, Any]]:
    return SURFACE_PROTECTION_DATA


def get_surface_protection_by_name(name: str) -> dict[str, Any] | None:
    for protection in SURFACE_PROTECTION_DATA:
        if protection["name"] == name:
            return protection
    return None


def get_surface_protection_by_code(code: str) -> dict[str, Any] | None:
    for protection in SURFACE_PROTECTION_DATA:
        if protection["code"] == code:
            return protection
    return None


def get_surface_protections_by_type(protection_type: str) -> list[dict[str, Any]]:
    return [sp for sp in SURFACE_PROTECTION_DATA if sp.get("properties", {}).get("protection_type") == protection_type]


def get_surface_protections_by_corrosion_resistance(resistance_level: str) -> list[dict[str, Any]]:
    return [sp for sp in SURFACE_PROTECTION_DATA if sp.get("properties", {}).get("corrosion_resistance") == resistance_level]


def get_surface_protections_with_storage_protection() -> list[dict[str, Any]]:
    return [sp for sp in SURFACE_PROTECTION_DATA if sp.get("properties", {}).get("storage_protection") == "yes"]


def get_surface_protections_with_paint_adhesion() -> list[dict[str, Any]]:
    return [sp for sp in SURFACE_PROTECTION_DATA if "paint_adhesion" in sp.get("properties", {})]


def get_protection_types() -> list[str]:
    protection_types = set()
    for protection in SURFACE_PROTECTION_DATA:
        protection_type = protection.get("properties", {}).get("protection_type")
        if protection_type:
            protection_types.add(protection_type)
    return sorted(list(protection_types))


def get_corrosion_resistance_levels() -> list[str]:
    resistance_levels = set()
    for protection in SURFACE_PROTECTION_DATA:
        resistance = protection.get("properties", {}).get("corrosion_resistance")
        if resistance:
            resistance_levels.add(resistance)
    return sorted(list(resistance_levels))


def get_treatment_types() -> list[str]:
    treatments = set()
    for protection in SURFACE_PROTECTION_DATA:
        treatment = protection.get("properties", {}).get("treatment")
        if treatment:
            treatments.add(treatment)
    return sorted(list(treatments))


__all__ = [
    "SURFACE_PROTECTION_DATA",
    "get_surface_protection_data",
    "get_surface_protection_by_name",
    "get_surface_protection_by_code",
    "get_surface_protections_by_type",
    "get_surface_protections_by_corrosion_resistance",
    "get_surface_protections_with_storage_protection",
    "get_surface_protections_with_paint_adhesion",
    "get_protection_types",
    "get_corrosion_resistance_levels",
    "get_treatment_types",
]
