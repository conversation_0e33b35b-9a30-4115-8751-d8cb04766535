"""Database session management for Vanilla Steel AI.

This module provides database connection pooling, session management,
and utilities for database operations.
"""

import os
import time
from collections.abc import Generator
from contextlib import contextmanager

from sqlalchemy import create_engine, event, text
from sqlalchemy.exc import DatabaseError as SQLAlchemyDatabaseError
from sqlalchemy.orm import Session, declarative_base, sessionmaker
from sqlalchemy.pool import NullPool, QueuePool

from db.settings import db_settings
from utils.exceptions import DatabaseConnectionError, DatabaseQueryError
from utils.log import get_app_logger, log_database_operation

# Get environment-specific settings
runtime_env = os.getenv("RUNTIME_ENV", "dev")
pool_size = db_settings.db_pool_size
max_overflow = db_settings.db_max_overflow
pool_timeout = db_settings.db_pool_timeout
pool_recycle = db_settings.db_pool_recycle

# Get database URL
db_url = db_settings.get_db_url()

# Log database configuration (without sensitive info)


# Create application logger
logger = get_app_logger()

logger.info(
    f"Database configuration: host={db_settings.db_host}, "
    f"port={db_settings.db_port}, database={db_settings.db_database}, "
    f"pool_size={pool_size}, max_overflow={max_overflow}"
)

# Configure engine with appropriate pooling settings
if runtime_env == "test":
    # Use NullPool for testing to avoid connection issues
    engine_args = {
        "poolclass": NullPool,
    }
else:
    engine_args = {
        "poolclass": QueuePool,
        "pool_size": pool_size,
        "max_overflow": max_overflow,
        "pool_timeout": pool_timeout,
        "pool_recycle": pool_recycle,
        "echo_pool": runtime_env == "dev",  # Log pool checkouts in dev
    }

# Add SSL configuration for production and staging
ssl_config = db_settings.get_ssl_config()
if ssl_config:
    engine_args["connect_args"] = ssl_config

# Create engine with query logging in development
db_engine = create_engine(
    db_url,
    echo=runtime_env == "dev" and os.getenv("SQL_ECHO", "false").lower() == "true",
    future=True,  # Use SQLAlchemy 2.0 style
    **engine_args,
)


# Add event listeners for monitoring
@event.listens_for(db_engine, "connect")
def receive_connect(dbapi_connection, connection_record):
    """Log successful database connections."""
    logger.debug("Database connection established")


@event.listens_for(db_engine, "checkout")
def receive_checkout(dbapi_connection, connection_record, connection_proxy):
    """Log connection checkouts from pool."""
    logger.debug("Connection checked out from pool")


# Create session factory with proper configuration
SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=db_engine,
    expire_on_commit=False,  # Don't expire objects after commit
    class_=Session,
)

# Create base class for models
Base = declarative_base()

# Add a name property to Base for better debugging
Base.__repr__ = lambda self: f"<{self.__class__.__name__}({', '.join(f'{k}={v}' for k, v in self.__dict__.items() if not k.startswith('_'))})>"


def get_db() -> Generator[Session, None, None]:
    """
    Get database session for dependency injection.

    This function is designed to be used with FastAPI's Depends().
    It ensures that database sessions are properly closed after use.

    Yields:
        Session: SQLAlchemy database session

    Raises:
        DatabaseConnectionError: If connection cannot be established

    Example:
        ```python
               from fastapi import Depends
               from sqlalchemy.orm import Session


               @app.get("/items")
               def read_items(db: Session = Depends(get_db)):
                   return db.query(Item).all()
        ```
    """
    db = SessionLocal()
    start_time = time.time()

    try:
        # Test the connection
        db.execute(text("SELECT 1"))
        yield db

        # Log successful transaction
        duration_ms = (time.time() - start_time) * 1000
        log_database_operation(
            operation="SESSION",
            table="N/A",
            duration_ms=duration_ms,
            success=True,
        )

    except SQLAlchemyDatabaseError as e:
        # Log database error
        duration_ms = (time.time() - start_time) * 1000
        log_database_operation(
            operation="SESSION",
            table="N/A",
            duration_ms=duration_ms,
            success=False,
        )

        logger.error(f"Database error in session: {e}")
        raise DatabaseConnectionError(
            "Failed to establish database connection",
            details={"error": str(e)},
        ) from e

    finally:
        db.close()


@contextmanager
def get_db_context() -> Generator[Session, None, None]:
    """
    Get database session as a context manager.

    This function is for use outside of FastAPI routes,
    such as in background tasks or scripts.

    Yields:
        Session: SQLAlchemy database session

    Raises:
        DatabaseConnectionError: If connection cannot be established

    Example:
        ```python
               with get_db_context() as db:
                   user = db.query(User).filter_by(email=email).first()
                   db.commit()
        ```
    """
    db = SessionLocal()

    try:
        yield db
        db.commit()
    except Exception:
        db.rollback()
        raise
    finally:
        db.close()


def test_database_connection() -> bool:
    """
    Test database connectivity.

    Returns:
        bool: True if connection successful, False otherwise
    """
    try:
        logger.info("Testing database connection...")
        with db_engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        logger.info("Database connection successful")
        return True
    except Exception as e:
        logger.error(f"Database connection test failed: {e}")
        return False


def init_database() -> None:
    """
    Initialize database tables.

    This function creates all tables defined in the models
    if they don't already exist.

    Raises:
        DatabaseError: If table creation fails
    """
    try:
        Base.metadata.create_all(bind=db_engine)
        logger.info("Database tables initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize database tables: {e}")
        raise DatabaseQueryError(
            "Failed to initialize database tables",
            details={"error": str(e)},
        ) from e
