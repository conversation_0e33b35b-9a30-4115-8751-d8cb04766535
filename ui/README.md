# Vanilla Steel AI - Client Management UI

A comprehensive Streamlit-based user interface for managing clients in the Vanilla Steel AI RFQ processing system.

## Features

### 🆕 Client Onboarding
- **3-Step Wizard**: Guided client creation process
- **Custom Configuration**: Support for custom schemas and rules
- **Default Fallbacks**: Automatic use of default configurations when custom ones aren't provided
- **Real-time Validation**: JSON validation for custom configurations
- **Progress Tracking**: Visual progress indicators throughout the process
- **API Integration**: Direct integration with the client onboarding API

### 👥 Client Management
- **Client Overview**: View all clients with filtering options
- **System Statistics**: Real-time metrics and status information
- **Client Details**: Detailed view of individual client configurations
- **Status Management**: Monitor client status and activity

### 🎨 Rich UI Features
- **Modern Design**: Beautiful gradient-based styling
- **Responsive Layout**: Works on desktop and mobile devices
- **Interactive Components**: Rich form controls and data visualization
- **Progress Indicators**: Step-by-step visual guidance
- **Status Cards**: Color-coded information cards
- **Data Tables**: Sortable and filterable client data

## Setup

### Prerequisites
- Python 3.8+
- Streamlit
- Access to the Vanilla Steel AI API
- Admin API key

### Installation

1. Install dependencies:
```bash
pip install -r ui/requirements.txt
```

2. Set environment variables:
```bash
export API_BASE_URL="http://localhost:8000"
export ADMIN_API_KEY="your-admin-api-key"
```

3. Run the Streamlit app:
```bash
streamlit run ui/Home.py
```

## Configuration

### Environment Variables

- `API_BASE_URL`: Base URL for the Vanilla Steel AI API (default: http://localhost:8000)
- `ADMIN_API_KEY`: Admin API key for accessing client management endpoints
- `STREAMLIT_PASSWORD`: Optional password for accessing the UI

### API Integration

The UI integrates with the following API endpoints:

- `POST /clients/onboard` - Create new clients with complete setup
- `GET /clients` - Retrieve list of existing clients
- Additional endpoints for client management (future implementation)

## Usage

### Onboarding a New Client

1. **Basic Information**:
   - Enter client name (required)
   - Optionally specify client code
   - Configure client flags (KB, instructions, output preferences)

2. **Advanced Configuration**:
   - Provide custom JSON schemas (optional)
   - Define custom processing rules (optional)
   - Set model preferences (optional)
   - All fields support JSON validation

3. **Review & Create**:
   - Review all configuration details
   - Create client with complete database setup
   - Receive API key and configuration details

### Managing Existing Clients

1. **Client List**:
   - View all clients with status information
   - Filter by status, knowledge base, etc.
   - Sort and search through client data

2. **Client Details**:
   - View detailed client information
   - Access configuration details
   - Manage API keys and settings

## File Structure

```
ui/
├── Home.py                     # Main dashboard and navigation
├── pages/
│   ├── 1_Client_Onboarding.py  # 3-step client onboarding wizard
│   └── 2_Client_Management.py  # Client list and management
├── css.py                      # Custom styling and themes
├── utils.py                    # Utility functions
├── requirements.txt            # Python dependencies
└── README.md                   # This file
```

## Customization

### Styling
The UI uses custom CSS defined in `css.py` with:
- Gradient color schemes
- Modern card layouts
- Responsive design
- Interactive hover effects

### Adding New Features
1. Create new pages in the `pages/` directory
2. Add navigation links in `Home.py`
3. Extend API integration in utility functions
4. Update styling in `css.py` as needed

## Security

- Admin API key authentication required
- Optional password protection for UI access
- Secure API communication
- Input validation and sanitization

## Troubleshooting

### Common Issues

1. **API Connection Errors**:
   - Verify `API_BASE_URL` is correct
   - Check if the API server is running
   - Ensure network connectivity

2. **Authentication Errors**:
   - Verify `ADMIN_API_KEY` is set correctly
   - Check API key permissions
   - Ensure key hasn't expired

3. **JSON Validation Errors**:
   - Verify JSON syntax in custom configurations
   - Check for proper object structure
   - Use JSON validators for complex schemas

### Debug Mode

Run with debug logging:
```bash
streamlit run ui/Home.py --logger.level=debug
```

## Contributing

1. Follow the existing code structure
2. Add proper error handling
3. Include input validation
4. Update documentation
5. Test with various client configurations

## License

Part of the Vanilla Steel AI system. See main project license for details.
