import asyncio
import os
import sys
from typing import Any

# Add project root to Python path for imports
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

import nest_asyncio
import pandas as pd
import requests
import streamlit as st
from agno.tools.streamlit.components import check_password

from ui.css import CUSTOM_CSS
from ui.utils import footer

nest_asyncio.apply()

st.set_page_config(
    page_title="Client Management - Vanilla Steel AI",
    page_icon="👥",
    layout="wide",
)
st.markdown(CUSTOM_CSS, unsafe_allow_html=True)

# Configuration
API_BASE_URL = os.getenv("API_BASE_URL", "http://localhost:8000")
ADMIN_API_KEY = os.getenv("ADMIN_API_KEY", "")


async def header():
    st.markdown("<h1 class='heading'>👥 Client Management</h1>", unsafe_allow_html=True)
    st.markdown(
        "<p class='subheading'>View and manage existing clients in the system</p>",
        unsafe_allow_html=True,
    )


async def fetch_clients() -> list[dict[str, Any]] | None:
    """Fetch all clients from the API."""
    try:
        headers = {"admin-key": ADMIN_API_KEY, "Content-Type": "application/json"}

        response = requests.get(f"{API_BASE_URL}/clients", headers=headers, timeout=30)

        if response.status_code == 200:
            return response.json().get("clients", [])
        else:
            error_detail = response.json().get("detail", "Unknown error") if response.content else "No response"
            st.error(f"API Error ({response.status_code}): {error_detail}")
            return None

    except requests.exceptions.RequestException as e:
        st.error(f"Connection error: {str(e)}")
        return None
    except Exception as e:
        st.error(f"Unexpected error: {str(e)}")
        return None


async def body():
    # Check if admin key is configured
    if not ADMIN_API_KEY:
        st.markdown(
            """
            <div class="warning-card">
                <h3>⚠️ Configuration Required</h3>
                <p>Admin API key is not configured. Please set the ADMIN_API_KEY environment variable.</p>
            </div>
            """,
            unsafe_allow_html=True,
        )
        return

    # Navigation
    st.markdown(
        """
        <div style="text-align: center; margin: 1rem 0;">
            <a href="/" style="text-decoration: none;">
                <button style="background: linear-gradient(45deg, #667eea, #764ba2); color: white; border: none; 
                       border-radius: 8px; padding: 0.5rem 1rem; margin: 0 0.5rem;">
                    🏠 Home
                </button>
            </a>
            <a href="/pages/1_Client_Onboarding.py" style="text-decoration: none;">
                <button style="background: linear-gradient(45deg, #4facfe, #00f2fe); color: white; border: none; 
                       border-radius: 8px; padding: 0.5rem 1rem; margin: 0 0.5rem;">
                    🆕 Onboard New Client
                </button>
            </a>
        </div>
        """,
        unsafe_allow_html=True,
    )

    # Fetch and display clients
    st.markdown("<h2 class='section-header'>📋 Client List</h2>", unsafe_allow_html=True)

    with st.spinner("Loading clients..."):
        clients = await fetch_clients()

    if clients is None:
        st.markdown(
            """
            <div class="warning-card">
                <h3>❌ Failed to Load Clients</h3>
                <p>Unable to fetch client data. Please check your connection and try again.</p>
            </div>
            """,
            unsafe_allow_html=True,
        )
        return

    if not clients:
        st.markdown(
            """
            <div class="info-card">
                <h3>📭 No Clients Found</h3>
                <p>No clients are currently registered in the system.</p>
                <p>Use the onboarding process to create your first client.</p>
            </div>
            """,
            unsafe_allow_html=True,
        )

        if st.button("🚀 Onboard First Client", use_container_width=True):
            st.switch_page("pages/1_Client_Onboarding.py")
        return

    # Display client statistics
    st.markdown("### 📊 System Overview")

    col1, col2, col3, col4 = st.columns(4)

    active_clients = len([c for c in clients if c.get("status") == "active"])
    inactive_clients = len([c for c in clients if c.get("status") != "active"])
    clients_with_kb = len([c for c in clients if c.get("has_kb")])
    clients_with_custom = len([c for c in clients if c.get("has_instruction")])

    with col1:
        st.metric(label="🏭 Total Clients", value=len(clients), help="Total number of clients in the system")

    with col2:
        st.metric(
            label="✅ Active Clients",
            value=active_clients,
            delta=f"{inactive_clients} inactive" if inactive_clients > 0 else None,
            help="Number of active vs inactive clients",
        )

    with col3:
        st.metric(label="📚 With Knowledge Base", value=clients_with_kb, help="Clients with custom knowledge bases")

    with col4:
        st.metric(label="⚙️ With Custom Config", value=clients_with_custom, help="Clients with custom instructions")

    # Client table
    st.markdown("### 📋 Client Details")

    # Prepare data for table
    client_data = []
    for client in clients:
        client_data.append(
            {
                "Name": client.get("name", "N/A"),
                "Client Code": client.get("client_code", "N/A"),
                "Status": client.get("status", "unknown"),
                "Knowledge Base": "✅" if client.get("has_kb") else "❌",
                "Custom Instructions": "✅" if client.get("has_instruction") else "❌",
                "Output Preferences": "✅" if client.get("has_output_preference") else "❌",
                "Created": client.get("created_at", "N/A")[:10] if client.get("created_at") else "N/A",
                "Client ID": client.get("client_id", "N/A"),
            }
        )

    if client_data:
        df = pd.DataFrame(client_data)

        # Add filters
        col_filter1, col_filter2 = st.columns(2)

        with col_filter1:
            status_filter = st.selectbox("Filter by Status", options=["All"] + list(set(c["Status"] for c in client_data)), index=0)

        with col_filter2:
            kb_filter = st.selectbox("Filter by Knowledge Base", options=["All", "With KB", "Without KB"], index=0)

        # Apply filters
        filtered_df = df.copy()

        if status_filter != "All":
            filtered_df = filtered_df[filtered_df["Status"] == status_filter]

        if kb_filter == "With KB":
            filtered_df = filtered_df[filtered_df["Knowledge Base"] == "✅"]
        elif kb_filter == "Without KB":
            filtered_df = filtered_df[filtered_df["Knowledge Base"] == "❌"]

        # Display table
        st.dataframe(
            filtered_df,
            use_container_width=True,
            hide_index=True,
            column_config={
                "Client ID": st.column_config.TextColumn("Client ID", help="Unique identifier for the client", width="medium"),
                "Status": st.column_config.TextColumn("Status", help="Current client status"),
                "Created": st.column_config.DateColumn("Created", help="Date when client was created"),
            },
        )

        # Client details section
        st.markdown("### 🔍 Client Details")

        selected_client = st.selectbox(
            "Select a client to view details:", options=["Select a client..."] + [f"{c['Name']} ({c['Client Code']})" for c in client_data], index=0
        )

        if selected_client != "Select a client...":
            # Find the selected client
            client_name = selected_client.split(" (")[0]
            selected_client_data = next((c for c in clients if c.get("name") == client_name), None)

            if selected_client_data:
                await display_client_details(selected_client_data)


async def display_client_details(client: dict[str, Any]):
    """Display detailed information about a specific client."""

    col1, col2 = st.columns(2)

    with col1:
        st.markdown(
            f"""
            <div class="feature-card">
                <h4>🏢 Basic Information</h4>
                <p><strong>Name:</strong> {client.get("name", "N/A")}</p>
                <p><strong>Client Code:</strong> {client.get("client_code", "N/A")}</p>
                <p><strong>Client ID:</strong> <code>{client.get("client_id", "N/A")}</code></p>
                <p><strong>Status:</strong> <span class="status-{"success" if client.get("status") == "active" else "warning"}">{client.get("status", "unknown").title()}</span></p>
                <p><strong>Created:</strong> {client.get("created_at", "N/A")}</p>
            </div>
            """,
            unsafe_allow_html=True,
        )

    with col2:
        st.markdown(
            f"""
            <div class="feature-card">
                <h4>⚙️ Configuration</h4>
                <p><strong>Knowledge Base:</strong> {"✅ Yes" if client.get("has_kb") else "❌ No"}</p>
                <p><strong>Custom Instructions:</strong> {"✅ Yes" if client.get("has_instruction") else "❌ No"}</p>
                <p><strong>Output Preferences:</strong> {"✅ Yes" if client.get("has_output_preference") else "❌ No"}</p>
            </div>
            """,
            unsafe_allow_html=True,
        )

    # Action buttons
    st.markdown("#### 🛠️ Actions")

    action_col1, action_col2, action_col3 = st.columns(3)

    with action_col1:
        if st.button("🔑 View API Key", key=f"api_key_{client.get('client_id')}", use_container_width=True):
            st.info("API key viewing functionality would be implemented here")

    with action_col2:
        if st.button("⚙️ Edit Configuration", key=f"edit_{client.get('client_id')}", use_container_width=True):
            st.info("Configuration editing functionality would be implemented here")

    with action_col3:
        if st.button("📊 View Schemas", key=f"schemas_{client.get('client_id')}", use_container_width=True):
            st.info("Schema viewing functionality would be implemented here")


async def main():
    await header()
    await body()
    await footer()


if __name__ == "__main__":
    if check_password():
        asyncio.run(main())
