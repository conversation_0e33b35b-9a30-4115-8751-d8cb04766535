import asyncio
import json
import os
import sys
from typing import Any

# Add project root to Python path for imports
# Handle both local development and Docker container paths
current_dir = os.path.dirname(os.path.abspath(__file__))
ui_dir = os.path.dirname(current_dir)  # Go up from pages/ to ui/
project_root = os.path.dirname(ui_dir)  # Go up from ui/ to project root

# In Docker, we might be in /app, so also try /app as project root
possible_roots = [
    project_root,
    "/app",  # Docker container path
    os.path.join(os.getcwd(), "../.."),  # Relative to current working directory
]

for root in possible_roots:
    if root not in sys.path and os.path.exists(root):
        sys.path.insert(0, root)

import nest_asyncio
import requests
import streamlit as st
from agno.tools.streamlit.components import check_password

from ui.css import CUSTOM_CSS
from ui.utils import footer

nest_asyncio.apply()

st.set_page_config(
    page_title="Client Onboarding - Vanilla Steel AI",
    page_icon="🆕",
    layout="wide",
)
st.markdown(CUSTOM_CSS, unsafe_allow_html=True)

# Configuration
API_BASE_URL = os.getenv("API_BASE_URL", "http://localhost:8000")
ADMIN_API_KEY = os.getenv("ADMIN_API_KEY", "")


async def header():
    st.markdown("<h1 class='heading'>🆕 Client Onboarding</h1>", unsafe_allow_html=True)
    st.markdown(
        "<p class='subheading'>Create a new client with complete RFQ processing setup</p>",
        unsafe_allow_html=True,
    )


def validate_json_input(json_str: str, field_name: str) -> dict[str, Any] | None:
    """Validate JSON input and return parsed dict or None if invalid."""
    if not json_str.strip():
        return None

    try:
        parsed = json.loads(json_str)
        if not isinstance(parsed, dict):
            st.error(f"{field_name} must be a valid JSON object")
            return None
        return parsed
    except json.JSONDecodeError as e:
        st.error(f"Invalid JSON in {field_name}: {str(e)}")
        return None


async def onboard_client(client_data: dict[str, Any]) -> dict[str, Any] | None:
    """Call the client onboarding API."""
    try:
        headers = {"admin-key": ADMIN_API_KEY, "Content-Type": "application/json"}

        response = requests.post(f"{API_BASE_URL}/clients/onboard", json=client_data, headers=headers, timeout=30)

        if response.status_code == 200:
            return response.json()
        else:
            error_detail = response.json().get("detail", "Unknown error") if response.content else "No response"
            st.error(f"API Error ({response.status_code}): {error_detail}")
            return None

    except requests.exceptions.RequestException as e:
        st.error(f"Connection error: {str(e)}")
        return None
    except Exception as e:
        st.error(f"Unexpected error: {str(e)}")
        return None


async def body():
    # Progress indicator
    st.markdown(
        """
        <div style="text-align: center; margin: 2rem 0;">
            <span class="progress-step active">1</span>
            <span style="margin: 0 10px;">→</span>
            <span class="progress-step">2</span>
            <span style="margin: 0 10px;">→</span>
            <span class="progress-step">3</span>
            <br><br>
            <strong>Step 1:</strong> Basic Information &nbsp;&nbsp;&nbsp;
            <strong>Step 2:</strong> Configuration &nbsp;&nbsp;&nbsp;
            <strong>Step 3:</strong> Review & Create
        </div>
        """,
        unsafe_allow_html=True,
    )

    # Check if admin key is configured
    if not ADMIN_API_KEY:
        st.markdown(
            """
            <div class="warning-card">
                <h3>⚠️ Configuration Required</h3>
                <p>Admin API key is not configured. Please set the ADMIN_API_KEY environment variable.</p>
            </div>
            """,
            unsafe_allow_html=True,
        )
        return

    # Initialize session state
    if "onboarding_step" not in st.session_state:
        st.session_state.onboarding_step = 1
    if "client_data" not in st.session_state:
        st.session_state.client_data = {}

    # Step 1: Basic Information
    if st.session_state.onboarding_step == 1:
        await step_1_basic_info()

    # Step 2: Configuration
    elif st.session_state.onboarding_step == 2:
        await step_2_configuration()

    # Step 3: Review & Create
    elif st.session_state.onboarding_step == 3:
        await step_3_review_create()


async def step_1_basic_info():
    st.markdown("<h2 class='section-header'>📝 Basic Client Information</h2>", unsafe_allow_html=True)

    with st.form("basic_info_form"):
        col1, col2 = st.columns(2)

        with col1:
            client_name = st.text_input(
                "Client Name *",
                value=st.session_state.client_data.get("name", ""),
                help="Enter the full name of the client company",
                placeholder="e.g., Steel Manufacturing Corp",
            )

            client_code = st.text_input(
                "Client Code (Optional)",
                value=st.session_state.client_data.get("client_code", ""),
                help="Custom client code (auto-generated if empty)",
                placeholder="e.g., steel_corp_001",
            )

        with col2:
            has_kb = st.checkbox(
                "Has Custom Knowledge Base",
                value=st.session_state.client_data.get("has_kb", False),
                help="Whether this client will have a custom knowledge base",
            )

            has_instruction = st.checkbox(
                "Has Custom Instructions",
                value=st.session_state.client_data.get("has_instruction", True),
                help="Whether this client will have custom processing instructions",
            )

            has_output_preference = st.checkbox(
                "Has Custom Output Preferences",
                value=st.session_state.client_data.get("has_output_preference", True),
                help="Whether this client will have custom output formatting preferences",
            )

        st.markdown("---")

        col_back, col_next = st.columns([1, 1])
        with col_back:
            if st.form_submit_button("🏠 Back to Home", use_container_width=True):
                st.switch_page("Home.py")

        with col_next:
            if st.form_submit_button("➡️ Next: Configuration", use_container_width=True):
                if not client_name.strip():
                    st.error("Client name is required!")
                else:
                    # Save data to session state
                    st.session_state.client_data.update(
                        {
                            "name": client_name.strip(),
                            "client_code": client_code.strip() if client_code.strip() else None,
                            "has_kb": has_kb,
                            "has_instruction": has_instruction,
                            "has_output_preference": has_output_preference,
                        }
                    )
                    st.session_state.onboarding_step = 2
                    st.rerun()


async def step_2_configuration():
    st.markdown("<h2 class='section-header'>⚙️ Advanced Configuration</h2>", unsafe_allow_html=True)

    # Update progress indicator
    st.markdown(
        """
        <div style="text-align: center; margin: 2rem 0;">
            <span class="progress-step completed">1</span>
            <span style="margin: 0 10px;">→</span>
            <span class="progress-step active">2</span>
            <span style="margin: 0 10px;">→</span>
            <span class="progress-step">3</span>
        </div>
        """,
        unsafe_allow_html=True,
    )

    st.markdown(
        """
        <div class="info-card">
            <h3>🎯 Schema Configuration</h3>
            <p>Configure custom schemas and rules for this client. Leave fields empty to use default configurations.</p>
            <p><strong>Note:</strong> JSON inputs must be valid JSON objects. Invalid JSON will prevent onboarding.</p>
        </div>
        """,
        unsafe_allow_html=True,
    )

    with st.form("configuration_form"):
        # Schema Configuration
        st.markdown("### 📊 Custom Schemas (Optional)")

        schema_col1, schema_col2 = st.columns(2)

        with schema_col1:
            custom_extraction_schema = st.text_area(
                "Custom Extraction Schema",
                value=st.session_state.client_data.get("custom_extraction_schema_str", ""),
                height=150,
                help="JSON schema for extraction agent output",
                placeholder='{"type": "object", "properties": {...}}',
            )

            custom_validation_schema = st.text_area(
                "Custom Validation Schema",
                value=st.session_state.client_data.get("custom_validation_schema_str", ""),
                height=150,
                help="JSON schema for validation agent output",
                placeholder='{"type": "object", "properties": {...}}',
            )

        with schema_col2:
            custom_normalizer_schema = st.text_area(
                "Custom Normalizer Schema",
                value=st.session_state.client_data.get("custom_normalizer_schema_str", ""),
                height=150,
                help="JSON schema for normalizer agent output",
                placeholder='{"type": "object", "properties": {...}}',
            )

            custom_formatter_schema = st.text_area(
                "Custom Formatter Schema",
                value=st.session_state.client_data.get("custom_formatter_schema_str", ""),
                height=150,
                help="JSON schema for formatter agent output",
                placeholder='{"type": "object", "properties": {...}}',
            )

        st.markdown("---")

        # Rules Configuration
        st.markdown("### 📋 Custom Rules (Optional)")

        rules_col1, rules_col2 = st.columns(2)

        with rules_col1:
            custom_extraction_rules = st.text_area(
                "Custom Extraction Rules",
                value=st.session_state.client_data.get("custom_extraction_rules_str", ""),
                height=150,
                help="JSON rules for extraction agent",
                placeholder='{"rules": [...], "instructions": "..."}',
            )

            custom_validation_rules = st.text_area(
                "Custom Validation Rules",
                value=st.session_state.client_data.get("custom_validation_rules_str", ""),
                height=150,
                help="JSON rules for validation agent",
                placeholder='{"rules": [...], "instructions": "..."}',
            )

        with rules_col2:
            custom_normalization_rules = st.text_area(
                "Custom Normalization Rules",
                value=st.session_state.client_data.get("custom_normalization_rules_str", ""),
                height=150,
                help="JSON rules for normalizer agent",
                placeholder='{"rules": [...], "instructions": "..."}',
            )

            custom_formatter_rules = st.text_area(
                "Custom Formatter Rules",
                value=st.session_state.client_data.get("custom_formatter_rules_str", ""),
                height=150,
                help="JSON rules for formatter agent",
                placeholder='{"rules": [...], "instructions": "..."}',
            )

        st.markdown("---")

        # Additional Configuration
        st.markdown("### 🔧 Additional Configuration")

        custom_formatter_config = st.text_area(
            "Custom Formatter Configuration",
            value=st.session_state.client_data.get("custom_formatter_config_str", ""),
            height=100,
            help="JSON configuration for formatter output preferences",
            placeholder='{"output_preferences": {...}, "value_formatting": {...}}',
        )

        model_preferences = st.text_area(
            "Model Preferences",
            value=st.session_state.client_data.get("model_preferences_str", ""),
            height=100,
            help="JSON configuration for AI model preferences",
            placeholder='{"model": "gpt-4", "temperature": 0.1, "max_tokens": 2000}',
        )

        st.markdown("---")

        # Navigation buttons
        col_back, col_next = st.columns([1, 1])
        with col_back:
            if st.form_submit_button("⬅️ Back: Basic Info", use_container_width=True):
                st.session_state.onboarding_step = 1
                st.rerun()

        with col_next:
            if st.form_submit_button("➡️ Next: Review", use_container_width=True):
                # Validate JSON inputs
                json_fields = {
                    "custom_extraction_schema": custom_extraction_schema,
                    "custom_validation_schema": custom_validation_schema,
                    "custom_normalizer_schema": custom_normalizer_schema,
                    "custom_formatter_schema": custom_formatter_schema,
                    "custom_extraction_rules": custom_extraction_rules,
                    "custom_validation_rules": custom_validation_rules,
                    "custom_normalization_rules": custom_normalization_rules,
                    "custom_formatter_rules": custom_formatter_rules,
                    "custom_formatter_config": custom_formatter_config,
                    "model_preferences": model_preferences,
                }

                validation_passed = True
                parsed_data = {}

                for field_name, json_str in json_fields.items():
                    if json_str.strip():
                        parsed = validate_json_input(json_str, field_name)
                        if parsed is None:
                            validation_passed = False
                        else:
                            parsed_data[field_name] = parsed

                    # Store string versions for form persistence
                    st.session_state.client_data[f"{field_name}_str"] = json_str

                if validation_passed:
                    # Update session state with parsed JSON
                    st.session_state.client_data.update(parsed_data)
                    st.session_state.onboarding_step = 3
                    st.rerun()


async def step_3_review_create():
    st.markdown("<h2 class='section-header'>🔍 Review & Create Client</h2>", unsafe_allow_html=True)

    # Update progress indicator
    st.markdown(
        """
        <div style="text-align: center; margin: 2rem 0;">
            <span class="progress-step completed">1</span>
            <span style="margin: 0 10px;">→</span>
            <span class="progress-step completed">2</span>
            <span style="margin: 0 10px;">→</span>
            <span class="progress-step active">3</span>
        </div>
        """,
        unsafe_allow_html=True,
    )

    # Review section
    st.markdown("### 📋 Client Information Review")

    col1, col2 = st.columns(2)

    with col1:
        st.markdown(
            f"""
            <div class="feature-card">
                <h4>🏢 Basic Information</h4>
                <p><strong>Name:</strong> {st.session_state.client_data.get("name", "N/A")}</p>
                <p><strong>Code:</strong> {st.session_state.client_data.get("client_code", "Auto-generated")}</p>
                <p><strong>Knowledge Base:</strong> {"✅ Yes" if st.session_state.client_data.get("has_kb") else "❌ No"}</p>
                <p><strong>Custom Instructions:</strong> {"✅ Yes" if st.session_state.client_data.get("has_instruction") else "❌ No"}</p>
                <p><strong>Output Preferences:</strong> {"✅ Yes" if st.session_state.client_data.get("has_output_preference") else "❌ No"}</p>
            </div>
            """,
            unsafe_allow_html=True,
        )

    with col2:
        # Count custom configurations
        custom_schemas = sum(
            1
            for key in st.session_state.client_data.keys()
            if key.startswith("custom_") and not key.endswith("_str") and st.session_state.client_data[key]
        )

        uses_defaults = custom_schemas == 0

        st.markdown(
            f"""
            <div class="feature-card">
                <h4>⚙️ Configuration Summary</h4>
                <p><strong>Custom Schemas:</strong> {custom_schemas}</p>
                <p><strong>Uses Defaults:</strong> {"✅ Yes" if uses_defaults else "❌ No"}</p>
                <p><strong>Model Preferences:</strong> {"✅ Custom" if st.session_state.client_data.get("model_preferences") else "🔧 Default"}</p>
                <p><strong>Total Schemas to Create:</strong> 9</p>
            </div>
            """,
            unsafe_allow_html=True,
        )

    # Custom configurations details
    if custom_schemas > 0:
        st.markdown("### 🔧 Custom Configurations")

        custom_items = []
        for key, value in st.session_state.client_data.items():
            if key.startswith("custom_") and not key.endswith("_str") and value:
                display_name = key.replace("custom_", "").replace("_", " ").title()
                custom_items.append(f"• {display_name}")

        if custom_items:
            st.markdown(
                f"""
                <div class="info-card">
                    <h4>📝 Custom Configurations Provided:</h4>
                    {"<br>".join(custom_items)}
                </div>
                """,
                unsafe_allow_html=True,
            )

    # Warning about defaults
    if uses_defaults:
        st.markdown(
            """
            <div class="warning-card">
                <h4>ℹ️ Using Default Configurations</h4>
                <p>This client will use default schemas and rules. You can customize them later through the client management interface.</p>
            </div>
            """,
            unsafe_allow_html=True,
        )

    st.markdown("---")

    # Create client section
    col_back, col_create = st.columns([1, 1])

    with col_back:
        if st.button("⬅️ Back: Configuration", use_container_width=True):
            st.session_state.onboarding_step = 2
            st.rerun()

    with col_create:
        if st.button("🚀 Create Client", use_container_width=True, type="primary"):
            await create_client()


async def create_client():
    """Create the client using the API."""
    st.markdown("### 🔄 Creating Client...")

    # Show progress
    progress_bar = st.progress(0)
    status_text = st.empty()

    try:
        # Prepare API payload
        status_text.text("Preparing client data...")
        progress_bar.progress(20)

        api_payload = {
            "name": st.session_state.client_data["name"],
            "has_kb": st.session_state.client_data.get("has_kb", False),
            "has_instruction": st.session_state.client_data.get("has_instruction", True),
            "has_output_preference": st.session_state.client_data.get("has_output_preference", True),
        }

        # Add optional fields
        if st.session_state.client_data.get("client_code"):
            api_payload["client_code"] = st.session_state.client_data["client_code"]

        # Add custom configurations if provided
        custom_fields = [
            "custom_extraction_schema",
            "custom_validation_schema",
            "custom_normalizer_schema",
            "custom_formatter_schema",
            "custom_extraction_rules",
            "custom_validation_rules",
            "custom_normalization_rules",
            "custom_formatter_rules",
            "custom_formatter_config",
            "model_preferences",
        ]

        for field in custom_fields:
            if field in st.session_state.client_data:
                api_payload[field] = st.session_state.client_data[field]

        # Call API
        status_text.text("Calling onboarding API...")
        progress_bar.progress(50)

        result = await onboard_client(api_payload)

        if result:
            progress_bar.progress(100)
            status_text.text("✅ Client created successfully!")

            # Display success
            st.markdown(
                """
                <div class="success-card">
                    <h3>🎉 Client Onboarding Successful!</h3>
                    <p>The client has been successfully created with complete configuration.</p>
                </div>
                """,
                unsafe_allow_html=True,
            )

            # Display results
            col1, col2 = st.columns(2)

            with col1:
                st.markdown("#### 🏢 Client Details")
                st.write(f"**Client ID:** `{result['client_id']}`")
                st.write(f"**Name:** {result['name']}")
                st.write(f"**Client Code:** {result['client_code']}")
                st.write(f"**Status:** {result['status']}")
                st.write(f"**Created:** {result.get('created_at', 'N/A')}")

            with col2:
                st.markdown("#### 🔑 API Access")
                st.write(f"**API Key:** `{result['api_key']}`")
                st.write(f"**Config ID:** `{result['config_id']}`")
                st.write(f"**Schemas Created:** {result['total_schemas_created']}")
                st.write(f"**Uses Defaults:** {'Yes' if result['uses_default_schemas'] else 'No'}")

            # Schema details
            if result.get("schemas_created"):
                st.markdown("#### 📊 Created Schemas")
                schema_data = []
                for schema_type, schema_id in result["schemas_created"].items():
                    schema_data.append({"Schema Type": schema_type.replace("_", " ").title(), "Schema ID": str(schema_id)})
                st.table(schema_data)

            # Action buttons
            st.markdown("---")
            col_home, col_new, col_manage = st.columns(3)

            with col_home:
                if st.button("🏠 Back to Home", use_container_width=True):
                    # Clear session state
                    for key in list(st.session_state.keys()):
                        if key.startswith(("onboarding_", "client_data")):
                            del st.session_state[key]
                    st.switch_page("Home.py")

            with col_new:
                if st.button("➕ Onboard Another", use_container_width=True):
                    # Clear session state for new onboarding
                    for key in list(st.session_state.keys()):
                        if key.startswith(("onboarding_", "client_data")):
                            del st.session_state[key]
                    st.rerun()

            with col_manage:
                if st.button("👥 Manage Clients", use_container_width=True):
                    st.switch_page("pages/2_Client_Management.py")

        else:
            progress_bar.progress(0)
            status_text.text("❌ Failed to create client")

            st.markdown(
                """
                <div class="warning-card">
                    <h3>❌ Onboarding Failed</h3>
                    <p>There was an error creating the client. Please check the error messages above and try again.</p>
                </div>
                """,
                unsafe_allow_html=True,
            )

    except Exception as e:
        progress_bar.progress(0)
        status_text.text("❌ Error occurred")
        st.error(f"Unexpected error during client creation: {str(e)}")


async def main():
    await header()
    await body()
    await footer()


if __name__ == "__main__":
    if check_password():
        asyncio.run(main())
