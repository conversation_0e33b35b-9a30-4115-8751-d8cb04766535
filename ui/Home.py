import asyncio

import nest_asyncio
import streamlit as st
from agno.tools.streamlit.components import check_password

from ui.css import C<PERSON><PERSON>M_CSS
from ui.utils import footer

nest_asyncio.apply()

st.set_page_config(
    page_title="Vanilla Steel AI - Client Management",
    page_icon="🏭",
    layout="wide",
)
st.markdown(CUSTOM_CSS, unsafe_allow_html=True)


async def header():
    st.markdown("<h1 class='heading'>Vanilla Steel AI</h1>", unsafe_allow_html=True)
    st.markdown(
        "<p class='subheading'>Intelligent RFQ Processing System - Client Management Portal</p>",
        unsafe_allow_html=True,
    )


async def body():
    st.markdown("<h2 class='section-header'>🏭 Client Management</h2>", unsafe_allow_html=True)

    # Welcome card
    st.markdown(
        """
        <div class="info-card">
            <h3>🎯 Welcome to the Client Management Portal</h3>
            <p>This portal allows administrators to onboard new clients for the Vanilla Steel AI RFQ processing system.
            Each client gets their own customized configuration, schemas, and API access.</p>
            <ul>
                <li><strong>🔐 Secure Onboarding:</strong> Admin-only access with comprehensive validation</li>
                <li><strong>⚙️ Custom Configuration:</strong> Tailored schemas and processing rules</li>
                <li><strong>🔑 API Key Generation:</strong> Automatic secure API key creation</li>
                <li><strong>📊 Complete Setup:</strong> Database, schemas, and configuration in one step</li>
            </ul>
        </div>
        """,
        unsafe_allow_html=True,
    )

    # Navigation options
    col1, col2 = st.columns(2)

    with col1:
        st.markdown(
            """
            <div class="feature-card">
                <h3>🆕 Onboard New Client</h3>
                <p>Create a new client with complete setup including:</p>
                <ul>
                    <li>Client record and configuration</li>
                    <li>Custom or default processing schemas</li>
                    <li>API key generation</li>
                    <li>Database initialization</li>
                </ul>
            </div>
            """,
            unsafe_allow_html=True,
        )
        if st.button("🚀 Start Client Onboarding", key="onboard_button", use_container_width=True):
            st.switch_page("pages/1_Client_Onboarding.py")

    with col2:
        st.markdown(
            """
            <div class="feature-card">
                <h3>📋 Manage Existing Clients</h3>
                <p>View and manage existing clients:</p>
                <ul>
                    <li>View client list and status</li>
                    <li>Update client configurations</li>
                    <li>Regenerate API keys</li>
                    <li>Monitor client activity</li>
                </ul>
            </div>
            """,
            unsafe_allow_html=True,
        )
        if st.button("👥 Manage Clients", key="manage_button", use_container_width=True):
            st.switch_page("pages/2_Client_Management.py")

    # System status
    st.markdown("<h2 class='section-header'>📊 System Status</h2>", unsafe_allow_html=True)

    status_col1, status_col2, status_col3 = st.columns(3)

    with status_col1:
        st.metric(label="🏭 Active Clients", value="Loading...", help="Number of active clients in the system")

    with status_col2:
        st.metric(label="🔑 API Keys", value="Loading...", help="Total number of active API keys")

    with status_col3:
        st.metric(label="⚙️ System Status", value="Operational", help="Current system operational status")


async def main():
    await header()
    await body()
    await footer()


if __name__ == "__main__":
    if check_password():
        asyncio.run(main())
