CUSTOM_CSS = """
<style>
/* Typography */
.heading {
    text-align: center;
    background: linear-gradient(45deg, #0147fe, #0099ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-size: 3rem;
    margin-bottom: 1rem;
}

.subheading {
    text-align: center;
    font-weight: 600;
    color: #666;
    font-size: 1.2rem;
    margin-bottom: 2rem;
}

.section-header {
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-size: 1.5rem;
    font-weight: 700;
    margin: 2rem 0 1rem 0;
    border-bottom: 2px solid #e0e0e0;
    padding-bottom: 0.5rem;
}

/* Cards and Containers */
.info-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 15px;
    margin: 1rem 0;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.success-card {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 15px;
    margin: 1rem 0;
    box-shadow: 0 8px 32px rgba(79, 172, 254, 0.3);
}

.warning-card {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 15px;
    margin: 1rem 0;
    box-shadow: 0 8px 32px rgba(250, 112, 154, 0.3);
}

.feature-card {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 10px;
    padding: 1.5rem;
    margin: 1rem 0;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
}

/* Form Elements */
.stSelectbox > div > div {
    background-color: #f8f9fa;
    border-radius: 8px;
}

.stTextInput > div > div > input {
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.stTextArea > div > div > textarea {
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

/* Buttons */
.stButton > button {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 0.5rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.stButton > button:hover {
    background: linear-gradient(45deg, #764ba2, #667eea);
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.4);
}

/* Status indicators */
.status-success {
    color: #28a745;
    font-weight: 600;
}

.status-warning {
    color: #ffc107;
    font-weight: 600;
}

.status-error {
    color: #dc3545;
    font-weight: 600;
}

/* Links */
a {
    text-decoration: underline;
    color: #3494E6;
    transition: color 0.3s ease;
}

a:hover {
    color: #FF416C;
}

/* Progress indicators */
.progress-step {
    display: inline-block;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: #e0e0e0;
    color: #666;
    text-align: center;
    line-height: 30px;
    margin: 0 10px;
    font-weight: 600;
}

.progress-step.active {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
}

.progress-step.completed {
    background: linear-gradient(45deg, #4facfe, #00f2fe);
    color: white;
}

/* Responsive design */
@media (max-width: 768px) {
    .heading {
        font-size: 2rem;
    }

    .feature-card {
        margin: 0.5rem 0;
        padding: 1rem;
    }
}
</style>
"""
