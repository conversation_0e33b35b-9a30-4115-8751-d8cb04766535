import os
import sys
from collections.abc import Callable
from typing import Any

# Add project root to Python path for imports
# Handle both local development and Docker container paths
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)  # Go up from ui/ to project root

# In Docker, we might be in /app, so also try /app as project root
possible_roots = [
    project_root,
    "/app",  # Docker container path
    os.path.join(os.getcwd(), ".."),  # Relative to current working directory
]

for root in possible_roots:
    if root not in sys.path and os.path.exists(root):
        sys.path.insert(0, root)

import streamlit as st
from agno.agent import Agent
from agno.document import Document
from agno.document.reader import Reader
from agno.document.reader.csv_reader import CSVReader
from agno.document.reader.docx_reader import DocxReader
from agno.document.reader.pdf_reader import PDFReader
from agno.document.reader.text_reader import TextReader
from agno.document.reader.website_reader import WebsiteReader

# Try to import the logging utility with fallback
try:
    from utils.log import get_app_logger

    logger = get_app_logger()
except ImportError:
    # Fallback to standard logging if utils.log is not available
    import logging

    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)


async def initialize_agent_session_state(agent_name: str):
    logger.info(f"---*--- Initializing session state for {agent_name} ---*---")
    if agent_name not in st.session_state:
        st.session_state[agent_name] = {
            "agent": None,
            "session_id": None,
            "messages": [],
        }


async def selected_model() -> str:
    """
    Display a model selector in the sidebar.
    """
    model_options = {
        "gpt-4o": "gpt-4o",
        "o3-mini": "o3-mini",
    }
    selected_model = st.sidebar.selectbox(
        "Choose a model",
        options=list(model_options.keys()),
        index=0,
        key="model_selector",
    )
    return model_options[selected_model]


async def add_message(
    agent_name: str,
    role: str,
    content: str,
    tool_calls: list[dict[str, Any]] | None = None,
) -> None:
    """
    Safely add a message to the Agent's session state.
    """
    # if role == "user":
    #     logger.info(f"👤  {role} → {agent_name}: {content}")
    # else:
    #     logger.info(f"🤖  {agent_name} → user: {content}")
    st.session_state[agent_name]["messages"].append({"role": role, "content": content, "tool_calls": tool_calls})


def display_tool_calls(tool_calls_container, tools):
    """
    Display tool calls in a streamlit container with expandable sections.

    Args:
            tool_calls_container: Streamlit container to display the tool calls
            tools: List of tool call dictionaries containing name, args, content, and metrics
    """
    if not tools:
        return

    try:
        with tool_calls_container.container():
            for tool_call in tools:
                tool_name = tool_call.get("tool_name", "Unknown Tool")
                tool_args = tool_call.get("tool_args", {})
                content = tool_call.get("content")
                metrics = tool_call.get("metrics", {})

                # Add timing information
                execution_time_str = "N/A"
                try:
                    if metrics:
                        execution_time = metrics.time
                        if execution_time is not None:
                            execution_time_str = f"{execution_time:.2f}s"
                except Exception as e:
                    logger.error(f"Error displaying tool calls: {str(e)}")
                    pass

                with st.expander(
                    f"🛠️ {tool_name.replace('_', ' ').title()} ({execution_time_str})",
                    expanded=False,
                ):
                    # Show query with syntax highlighting
                    if isinstance(tool_args, dict) and tool_args.get("query"):
                        st.code(tool_args["query"], language="sql")

                    # Display arguments in a more readable format
                    if tool_args and tool_args != {"query": None}:
                        st.markdown("**Arguments:**")
                        st.json(tool_args)

                    if content:
                        st.markdown("**Results:**")
                        try:
                            # Check if content is already a dictionary or can be parsed as JSON
                            if isinstance(content, dict) or (isinstance(content, str) and content.strip().startswith(("{", "["))):
                                st.json(content)
                            else:
                                # If not JSON, show as markdown
                                st.markdown(content)
                        except Exception:
                            # If JSON display fails, show as markdown
                            st.markdown(content)
    except Exception as e:
        logger.error(f"Error displaying tool calls: {str(e)}")
        tool_calls_container.error(f"Failed to display tool results: {str(e)}")


async def example_inputs(agent_name: str) -> None:
    """
    Show example inputs for an Agent.
    """
    with st.sidebar:
        st.markdown("#### :thinking_face: Try me!")
        if st.button("Who are you?"):
            await add_message(
                agent_name,
                "user",
                "Who are you?",
            )
        if st.button("What is your purpose?"):
            await add_message(
                agent_name,
                "user",
                "What is your purpose?",
            )

        # Agent-specific examples
        if agent_name == "sage" and st.button("Tell me about Agno"):
            await add_message(
                agent_name,
                "user",
                "Tell me about Agno. Github repo: https://github.com/agno-agi/agno. Documentation: https://docs.agno.com",
            )
        elif agent_name == "scholar" and st.button("Tell me about the US tariffs"):
            await add_message(
                agent_name,
                "user",
                "Tell me about the US tariffs",
            )


async def knowledge_widget(agent_name: str, agent: Agent) -> None:
    """
    Display a knowledge widget in the sidebar.
    """

    if agent is not None and agent.knowledge is not None:
        # Add websites to knowledge base
        if "url_scrape_key" not in st.session_state:
            st.session_state[agent_name]["url_scrape_key"] = 0
        input_url = st.sidebar.text_input(
            "Add URL to Knowledge Base",
            type="default",
            key=st.session_state[agent_name]["url_scrape_key"],
        )
        add_url_button = st.sidebar.button("Add URL")
        if add_url_button and input_url is not None:
            alert = st.sidebar.info("Processing URLs...", icon="ℹ️")
            if f"{input_url}_scraped" not in st.session_state:
                scraper = WebsiteReader(max_links=2, max_depth=1)
                web_documents: list[Document] = scraper.read(input_url)
                if web_documents:
                    agent.knowledge.load_documents(web_documents, upsert=True)
                else:
                    st.sidebar.error("Could not read website")
                st.session_state[f"{input_url}_uploaded"] = True
            alert.empty()

        # Add documents to knowledge base
        if "file_uploader_key" not in st.session_state:
            st.session_state[agent_name]["file_uploader_key"] = 100
        uploaded_file = st.sidebar.file_uploader(
            "Add a Document (.pdf, .csv, .txt, or .docx)",
            key=st.session_state[agent_name]["file_uploader_key"],
        )
        if uploaded_file is not None:
            alert = st.sidebar.info("Processing document...", icon="🧠")
            document_name = uploaded_file.name.split(".")[0]
            if f"{document_name}_uploaded" not in st.session_state:
                file_type = uploaded_file.name.split(".")[-1].lower()

                reader: Reader
                if file_type == "pdf":
                    reader = PDFReader()
                elif file_type == "csv":
                    reader = CSVReader()
                elif file_type == "txt":
                    reader = TextReader()
                elif file_type == "docx":
                    reader = DocxReader()
                else:
                    st.sidebar.error("Unsupported file type")
                    return
                uploaded_file_documents: list[Document] = reader.read(uploaded_file)
                if uploaded_file_documents:
                    agent.knowledge.load_documents(uploaded_file_documents, upsert=True)
                else:
                    st.sidebar.error("Could not read document")
                st.session_state[f"{document_name}_uploaded"] = True
            alert.empty()

        # Load and delete knowledge
        if st.sidebar.button("🗑️ Delete Knowledge"):
            agent.knowledge.delete()
            st.sidebar.success("Knowledge deleted!")


async def session_selector(
    agent_name: str,
    agent: Agent,
    get_agent: Callable,
    user_id: str,
    model_id: str,
) -> None:
    """
    Display a session selector in the sidebar, if a new session is selected, the agent is restarted with the new session

    Args:
            agent_name: The name of the agent to display.
            agent: The `Agent` instance to use for the session selector.
            get_agent: A function that takes user_id and model_id as arguments and returns an `Agent` instance.
            user_id: The user ID to use for the session selector.
            model_id: The model ID to use for the session selector.
    """


def export_chat_history(agent_name: str):
    """
    Export chat history in markdown format.

    Returns:
            str: Formatted markdown string of the chat history
    """
    if "messages" not in st.session_state[agent_name] or not st.session_state[agent_name]["messages"]:
        return f"# {agent_name} - Chat History\n\nNo messages to export."

    chat_text = f"# {agent_name} - Chat History\n\n"
    for msg in st.session_state[agent_name]["messages"]:
        role_label = "🤖 Assistant" if msg["role"] == "assistant" else "👤 User"
        chat_text += f"### {role_label}\n{msg['content']}\n\n"

        # Include tool calls if present
        if msg.get("tool_calls"):
            chat_text += "#### Tool Calls:\n"
            for i, tool_call in enumerate(msg["tool_calls"]):
                tool_name = tool_call.get("name", "Unknown Tool")
                chat_text += f"**{i + 1}. {tool_name}**\n\n"
                if "arguments" in tool_call:
                    chat_text += f"Arguments: ```json\n{tool_call['arguments']}\n```\n\n"
                if "content" in tool_call:
                    chat_text += f"Results: ```\n{tool_call['content']}\n```\n\n"

    return chat_text


async def utilities_widget(agent_name: str, agent: Agent) -> None:
    """
    Display a utilities widget in the sidebar.
    """
    st.sidebar.markdown("#### 🛠️ Utilities")
    col1, col2 = st.sidebar.columns(2)
    with col1:
        if st.button("🔄 Start New Chat"):
            restart_agent(agent_name)
    with col2:
        fn = f"{agent_name}_chat_history.md"
        if "session_id" in st.session_state[agent_name]:
            fn = f"{agent_name}_{st.session_state[agent_name]['session_id']}.md"
        if st.download_button(
            ":file_folder: Export Chat History",
            export_chat_history(agent_name),
            file_name=fn,
            mime="text/markdown",
        ):
            st.sidebar.success("Chat history exported!")


def restart_agent(agent_name: str):
    logger.debug("---*--- Restarting Agent ---*---")
    st.session_state[agent_name]["agent"] = None
    st.session_state[agent_name]["session_id"] = None
    st.session_state[agent_name]["messages"] = []
    if "url_scrape_key" in st.session_state[agent_name]:
        st.session_state[agent_name]["url_scrape_key"] += 1
    if "file_uploader_key" in st.session_state[agent_name]:
        st.session_state[agent_name]["file_uploader_key"] += 1
    st.rerun()


async def footer():
    st.markdown("---")
    st.markdown(
        "<p style='text-align: center; color: #666;'>Vanilla Steel AI - Intelligent RFQ Processing System | Built with ❤️ using <a href='https://github.com/agno-agi/agno' target='_blank'>Agno</a></p>",
        unsafe_allow_html=True,
    )
