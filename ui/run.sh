#!/bin/bash

# Vanilla Steel AI - Client Management UI Runner
# This script sets up and runs the Streamlit UI for client management

echo "🏭 Starting Vanilla Steel AI Client Management UI..."

# Check if required environment variables are set
if [ -z "$ADMIN_API_KEY" ]; then
    echo "⚠️  Warning: ADMIN_API_KEY environment variable is not set"
    echo "   Please set it with: export ADMIN_API_KEY='your-admin-key'"
fi

if [ -z "$API_BASE_URL" ]; then
    echo "ℹ️  Using default API_BASE_URL: http://localhost:8000"
    export API_BASE_URL="http://localhost:8000"
else
    echo "ℹ️  Using API_BASE_URL: $API_BASE_URL"
fi

# Install dependencies if needed
if [ ! -d "venv" ]; then
    echo "📦 Installing dependencies..."
    pip install -r requirements.txt
fi

# Run Streamlit
echo "🚀 Starting Streamlit UI..."
streamlit run Home.py --server.port=8501 --server.address=0.0.0.0

echo "✅ UI is running at http://localhost:8501"
