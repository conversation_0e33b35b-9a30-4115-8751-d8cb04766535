# This file was autogenerated by uv via the following command:
#    ./scripts/generate_requirements.sh
agno==1.4.2
agno-aws==0.0.1
agno-docker==0.0.1
aiofiles==24.1.0
aiohappyeyeballs==2.6.1
aiohttp==3.12.9
aiosignal==1.3.2
alembic==1.15.1
altair==5.5.0
annotated-types==0.7.0
anyio==4.9.0
asyncer==0.0.8
attrs==25.3.0
backoff==2.2.1
beautifulsoup4==4.13.3
blinker==1.9.0
boto3==1.37.19
botocore==1.37.19
cachetools==5.5.2
certifi==2025.1.31
charset-normalizer==3.4.1
click==8.1.8
cloudpickle==3.1.1
colorlog==6.9.0
coolname==2.2.0
datasets==3.6.0
decorator==5.2.1
dill==0.3.8
diskcache==5.6.3
distro==1.9.0
dnspython==2.7.0
docker==7.1.0
docstring-parser==0.16
dspy==2.6.27
dspy-ai==2.6.27
duckduckgo-search==7.5.3
email-validator==2.2.0
fastapi==0.115.12
fastapi-cli==0.0.7
filelock==3.18.0
frozenlist==1.6.2
fsspec==2025.3.0
gitdb==4.0.12
gitpython==3.1.44
googleapis-common-protos==1.70.0
h11==0.14.0
hf-xet==1.1.3
httpcore==1.0.7
httptools==0.6.4
huggingface-hub==0.32.4
idna==3.10
importlib-metadata==8.7.0
iniconfig==2.1.0
jinja2==3.1.6
jiter==0.9.0
jmespath==1.0.1
joblib==1.5.1
json-repair==0.46.1
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
langwatch==0.2.3
litellm==1.72.1
lxml==5.3.1
magicattr==0.1.6
mako==1.3.9
markdown-it-py==3.0.0
markupsafe==3.0.2
mdurl==0.1.2
multidict==6.4.4
multiprocess==0.70.16
mypy-extensions==1.0.0
nanoid==2.0.0
narwhals==1.32.0
nest-asyncio==1.6.0
numpy==2.2.4
openai==1.68.2
openinference-instrumentation==0.1.32
openinference-instrumentation-haystack==0.1.24
openinference-instrumentation-langchain==0.1.43
openinference-instrumentation-openai==0.1.30
openinference-semantic-conventions==0.1.17
opentelemetry-api==1.34.0
opentelemetry-exporter-otlp-proto-common==1.34.0
opentelemetry-exporter-otlp-proto-http==1.34.0
opentelemetry-instrumentation==0.55b0
opentelemetry-proto==1.34.0
opentelemetry-sdk==1.34.0
opentelemetry-semantic-conventions==0.55b0
optuna==4.3.0
packaging==24.2
pandas==2.2.3
pgvector==0.4.0
pillow==11.1.0
pluggy==1.5.0
primp==0.14.0
propcache==0.3.1
protobuf==5.29.4
psutil
psycopg==3.2.6
psycopg-binary==3.2.6
psycopg2-binary==2.9.10
py==1.11.0
pyarrow==19.0.1
httpx>=0.27.0,<0.28.0
pydantic
pydantic-core
pydantic-settings
pydeck==0.9.1
pygments==2.19.1
pypdf==5.4.0
pytest==8.3.5
pytest-mock==3.14.0
python-dateutil==2.9.0.post0
python-docx==1.1.2
python-dotenv==1.1.0
python-multipart==0.0.20
pytz==2025.2
pyyaml==6.0.2
referencing==0.36.2
regex==2024.11.6
requests==2.32.3
retry==0.9.2
rich==13.9.4
rich-toolkit==0.13.2
rpds-py==0.23.1
s3transfer==0.11.4
shellingham==1.5.4
six==1.17.0
smmap==5.0.2
sniffio==1.3.1
soupsieve==2.6
sqlalchemy==2.0.39
starlette==0.46.1
streamlit==1.43.2
tenacity==9.0.0
termcolor==3.1.0
tiktoken==0.9.0
tokenize-rt==6.2.0
tokenizers==0.21.1
toml==0.10.2
tomli==2.2.1
tornado==6.4.2
tqdm==4.67.1
typer==0.15.2
# Type stubs for mypy compatibility
types-psutil
types-aiofiles
typing-extensions==4.12.2
typing-inspect==0.9.0
typing-inspection==0.4.0
tzdata==2025.2
ujson==5.10.0
unkey.py==0.9.0
urllib3==2.3.0
uvicorn==0.34.0
uvloop==0.21.0
watchfiles==1.0.4
websockets==15.0.1
wrapt==1.17.2
xxhash==3.5.0
yarl==1.20.0
zipp==3.22.0
python-dotenv==1.1.0
# Type stubs for mypy compatibility
types-psutil
types-aiofiles
types-requests
# Add missing async packages
asyncpg
uv
