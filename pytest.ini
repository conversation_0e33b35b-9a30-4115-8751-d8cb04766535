[tool:pytest]
# Pytest Configuration for Vanilla Steel AI
minversion = 6.0
addopts =
    -ra
    --strict-markers
    --strict-config
    --cov=api
    --cov=db
    --cov=utils
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml
    --cov-fail-under=80
    -p no:warnings
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    e2e: marks tests as end-to-end tests
    db: marks tests that require database
    api: marks tests for API endpoints
    security: marks security-related tests
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
env =
    TESTING = True
    DB_DATABASE = sqlite:///:memory:
    RUNTIME_ENV = dev
    PYTHONPATH=/Users/<USER>/Projects/vanilla-steel-ai
