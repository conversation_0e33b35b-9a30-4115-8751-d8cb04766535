"""
RFQ Validation Tools for validating steel material specifications.

This module provides database tools for validating RFQ specifications against
reference catalogs and compatibility rules using exact matching only.
"""

import logging
import time
from contextlib import contextmanager
from typing import Any, cast

import psycopg2
import psycopg2.errors
from agno.tools import Toolkit
from psycopg2 import DatabaseError as PgDatabaseError
from psycopg2 import InterfaceError, OperationalError, ProgrammingError

from tools.rfq.database_tools import DatabaseConnectionError, DatabaseQueryError
from utils.rfq.config import get_default_client_id

# Configure logger for this module
logger = logging.getLogger(__name__)


# Generic User Template client ID for global reference data
GENERIC_CLIENT_ID = get_default_client_id()


class RFQValidationTools(Toolkit):
    """
    Database tools for RFQ validation with catalog-based validation capabilities.

    This class provides validation operations for RFQ specifications including:
    - Material grade validation
    - Coating designation validation
    - Form specification validation
    - Finish specification validation
    - Surface type validation
    - Surface protection validation
    - Combination compatibility validation

    Note: Only exact matching is performed - no similarity/fuzzy matching.
    Supports client-specific validation based on has_kb flag.
    """

    def __init__(self, db_url: str, client_id: str, max_retries: int = 3, retry_delay: float = 1.0):
        """
        Initialize RFQ Validation Tools.

        Args:
            db_url: Database connection URL
            client_id: Specific client id
            max_retries: Maximum number of retry attempts for failed operations
            retry_delay: Delay between retry attempts in seconds

        Raises:
            ValueError: If db_url is invalid
        """
        super().__init__(name="rfq_validation_tools")

        if not db_url or not isinstance(db_url, str):
            raise ValueError("Database URL must be a non-empty string")

        # Convert SQLAlchemy URL to psycopg2 format
        if db_url.startswith("postgresql+psycopg://"):
            self.db_url = db_url.replace("postgresql+psycopg://", "postgresql://")
        else:
            self.db_url = db_url

        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.client_id = client_id

        logger.info(f"RFQ Validation Tools initialized for: {self._get_safe_db_info()}")
        self._client_has_kb: bool | None = None  # Cache for has_kb flag

    def _get_safe_db_info(self) -> str:
        """Get safe database connection info for logging (without credentials)."""
        try:
            import re

            match = re.search(r"://(?:[^:]+:)?[^@]*@([^:/]+)(?::(\\d+))?/([^?]+)", self.db_url)
            if match:
                host, port, database = match.groups()
                port_str = f":{port}" if port else ""
                return f"{host}{port_str}/{database}"
            return "unknown_database"
        except Exception:
            return "database_info_unavailable"

    @contextmanager
    def get_db_connection(self):
        """Context manager for database connections with proper error handling."""
        conn = None
        try:
            conn = psycopg2.connect(self.db_url)
            yield conn
        except OperationalError as e:
            logger.error(f"Database connection failed: {e}")
            raise DatabaseConnectionError(f"Could not connect to database: {e}") from e
        except InterfaceError as e:
            logger.error(f"Database interface error: {e}")
            raise DatabaseConnectionError(f"Database interface error: {e}") from e
        finally:
            if conn:
                conn.close()

    def execute_with_retry(self, query: str, params: tuple | None = None, fetch_all: bool = False) -> tuple | list[tuple] | None:
        """Execute a database query with retry logic."""
        last_exception = None

        for attempt in range(self.max_retries):
            try:
                with self.get_db_connection() as conn, conn.cursor() as cur:
                    if params:
                        cur.execute(query, params)
                    else:
                        cur.execute(query)

                    if fetch_all:
                        return cur.fetchall()
                    else:
                        return cur.fetchone()
            except (OperationalError, InterfaceError) as e:
                last_exception = e
                logger.warning(f"Database operation failed on attempt {attempt + 1}/{self.max_retries}: {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay * (attempt + 1))
                continue
            except ProgrammingError as e:
                logger.error(f"Database query error: {e}")
                raise DatabaseQueryError(f"Invalid query execution: {e}") from e
            except PgDatabaseError as e:
                logger.error(f"Database error: {e}")
                raise DatabaseQueryError(f"Database operation failed: {e}") from e

        raise DatabaseConnectionError(f"Database operation failed after {self.max_retries} attempts: {last_exception}")

    def _check_client_has_kb(self) -> bool:
        """
        Check if the current client has a custom knowledge base.

        Returns:
            bool: True if client has custom knowledge base, False otherwise
        """
        if self._client_has_kb is not None:
            return self._client_has_kb  # Return cached value

        try:
            query = """
            SELECT has_kb
            FROM rfq_clients
            WHERE client_id = %s
            """

            result = self.execute_with_retry(query, (self.client_id,))

            if result:
                self._client_has_kb = bool(result[0]) if result[0] is not None else False
            else:
                self._client_has_kb = False

            logger.info(f"Client {self.client_id} has_kb: {self._client_has_kb}")
            return self._client_has_kb

        except Exception as e:
            logger.error(f"Error checking client KB status: {e}")
            self._client_has_kb = False  # Default to False on error
            return self._client_has_kb

    def _validate_catalog_item(self, item_value: str, catalog_type: str, client_id: str | None = None) -> dict[str, Any]:
        """
        Generic validation method for catalog items with exact matching only.

        Args:
            item_value: Value to validate
            catalog_type: Type of catalog (grade, coating, finish, form, surface_type, surface_protection)
            client_id: Optional client ID for client-specific validation

        Returns:
            Dict containing validation result
        """
        if not item_value or not isinstance(item_value, str):
            return {
                "is_valid": False,
                "confidence": 0.0,
                "found_item": None,
                "validation_details": {"error": f"Invalid {catalog_type} input"},
            }

        # Clean and normalize the input
        item_clean = item_value.strip().upper() if catalog_type in ["grade", "coating"] else item_value.strip().lower()

        try:
            # First check if client has custom knowledge base
            has_kb = self._check_client_has_kb()

            if has_kb:
                # Try client-specific catalog first
                query = """
                SELECT rd.code, rd.name, rd.properties
                FROM rfq_reference_data rd
                JOIN rfq_catalog_types ct ON rd.catalog_type_id = ct.catalog_type_id
                WHERE ct.name = %s
                AND (CASE
                    WHEN %s IN ('grade', 'coating') THEN UPPER(rd.code) = %s
                    ELSE LOWER(rd.code) = %s
                END)
                AND rd.is_active = true
                AND rd.client_id = %s
                LIMIT 1
                """

                result = self.execute_with_retry(query, (catalog_type, catalog_type, item_clean, item_clean, self.client_id))

                if result:
                    return {
                        "is_valid": True,
                        "confidence": 1.0,
                        "found_item": result[0],
                        "validation_details": {
                            "name": result[1],
                            "properties": result[2] or {},
                            "match_type": "exact",
                            "catalog_type": catalog_type,
                            "source": "client_specific",
                        },
                    }

            # Fall back to generic catalog or if client doesn't have custom KB
            query = """
            SELECT rd.code, rd.name, rd.properties
            FROM rfq_reference_data rd
            JOIN rfq_catalog_types ct ON rd.catalog_type_id = ct.catalog_type_id
            WHERE ct.name = %s
            AND (CASE
                WHEN %s IN ('grade', 'coating') THEN UPPER(rd.code) = %s
                ELSE LOWER(rd.code) = %s
            END)
            AND rd.is_active = true
            AND (rd.client_id = %s OR rd.client_id = %s)
            ORDER BY rd.client_id NULLS LAST
            LIMIT 1
            """

            result = self.execute_with_retry(query, (catalog_type, catalog_type, item_clean, item_clean, client_id, GENERIC_CLIENT_ID))

            if result:
                source = "client_specific" if has_kb else "generic"
                return {
                    "is_valid": True,
                    "confidence": 1.0,
                    "found_item": result[0],
                    "validation_details": {
                        "name": result[1],
                        "properties": result[2] or {},
                        "match_type": "exact",
                        "catalog_type": catalog_type,
                        "source": source,
                    },
                }

            # No exact match found - return invalid with no suggestions
            return {
                "is_valid": False,
                "confidence": 0.0,
                "found_item": None,
                "validation_details": {
                    "match_type": "none",
                    "search_term": item_clean,
                    "catalog_type": catalog_type,
                    "note": "Exact match required - no suggestions provided",
                },
            }

        except Exception as e:
            logger.error(f"Error validating {catalog_type} '{item_value}': {e}")
            return {"is_valid": False, "confidence": 0.0, "found_item": None, "validation_details": {"error": str(e)}}

    def validate_material_grade(self, grade: str, client_id: str | None = None) -> dict[str, Any]:
        """
        Validate material grade against standard steel grade codes.
        Only exact matches are accepted.

        Args:
            grade: Steel grade to validate (e.g., "S235JR", "DX51D")
            client_id: Optional client ID for client-specific validation

        Returns:
            Dict containing validation result
        """
        return self._validate_catalog_item(grade, "grade", client_id)

    def validate_coating_designation(self, coating: str, client_id: str | None = None) -> dict[str, Any]:
        """
        Validate coating designation against coating catalogs.
        Only exact matches are accepted.

        Args:
            coating: Coating designation to validate (e.g., "Z275", "AZ150")
            client_id: Optional client ID for client-specific validation

        Returns:
            Dict containing validation result
        """
        return self._validate_catalog_item(coating, "coating", client_id)

    def validate_finish_specification(self, finish: str, client_id: str | None = None) -> dict[str, Any]:
        """
        Validate finish specification against finish catalogs.
        Only exact matches are accepted.

        Args:
            finish: Finish specification to validate (e.g., "mill finish", "painted")
            client_id: Optional client ID for client-specific validation

        Returns:
            Dict containing validation result
        """
        return self._validate_catalog_item(finish, "finish", client_id)

    def validate_form_specification(self, form: str, client_id: str | None = None) -> dict[str, Any]:
        """
        Validate form specifications against available forms catalog.
        Only exact matches are accepted.

        Args:
            form: Form specification to validate (e.g., "coil", "sheet", "plate")
            client_id: Optional client ID for client-specific validation

        Returns:
            Dict containing validation result
        """
        return self._validate_catalog_item(form, "form", client_id)

    def validate_surface_type(self, surface_type: str, client_id: str | None = None) -> dict[str, Any]:
        """
        Validate surface type against surface type catalogs.
        Only exact matches are accepted.

        Args:
            surface_type: Surface type to validate (e.g., "hot rolled", "cold rolled")
            client_id: Optional client ID for client-specific validation

        Returns:
            Dict containing validation result
        """
        return self._validate_catalog_item(surface_type, "surface_type", client_id)

    def validate_surface_protection(self, surface_protection: str, client_id: str | None = None) -> dict[str, Any]:
        """
        Validate surface protection against surface protection catalogs.
        Only exact matches are accepted.

        Args:
            surface_protection: Surface protection to validate (e.g., "oiled", "passivated")
            client_id: Optional client ID for client-specific validation

        Returns:
            Dict containing validation result
        """
        return self._validate_catalog_item(surface_protection, "surface_protection", client_id)

    def validate_grade_coating_compatibility(self, grade: str, coating: str, client_id: str | None = None) -> dict[str, Any]:
        """Check for valid combinations (e.g., certain coatings with certain grades)."""
        if not grade or not coating:
            return {"is_compatible": False, "confidence": 0.0, "validation_details": {"error": "Missing grade or coating"}}

        grade_clean = grade.strip().upper()
        coating_clean = coating.strip().upper()

        try:
            query = """
            SELECT 1
            FROM rfq_grade_coating_compatibility gcc
            WHERE UPPER(gcc.grade_code) = %s
            AND UPPER(gcc.coating_code) = %s
            AND (gcc.client_id = %s OR gcc.client_id = %s)
            LIMIT 1
            """

            result = self.execute_with_retry(query, (grade_clean, coating_clean, client_id, GENERIC_CLIENT_ID))

            if result:
                return {
                    "is_compatible": True,
                    "confidence": 1.0,
                    "validation_details": {"match_type": "explicit_rule", "grade": grade_clean, "coating": coating_clean},
                }

            grade_valid = self.validate_material_grade(grade, client_id)
            coating_valid = self.validate_coating_designation(coating, client_id)

            if grade_valid["is_valid"] and coating_valid["is_valid"]:
                return {
                    "is_compatible": True,
                    "confidence": 0.7,
                    "validation_details": {
                        "match_type": "inferred",
                        "grade": grade_clean,
                        "coating": coating_clean,
                        "note": "No explicit compatibility rule found, but both grade and coating are valid",
                    },
                }

            return {
                "is_compatible": False,
                "confidence": 0.0,
                "validation_details": {
                    "match_type": "none",
                    "grade_valid": grade_valid["is_valid"],
                    "coating_valid": coating_valid["is_valid"],
                    "grade": grade_clean,
                    "coating": coating_clean,
                },
            }

        except Exception as e:
            logger.error(f"Error validating grade-coating compatibility '{grade}'-'{coating}': {e}")
            return {"is_compatible": False, "confidence": 0.0, "validation_details": {"error": str(e)}}

    def validate_all_specifications(self, specifications: dict[str, Any], client_id: str | None = None) -> dict[str, Any]:
        """
        Validate all material specifications in a single call.

        Args:
            specifications: Dict containing material specifications
            client_id: Optional client ID for client-specific validation

        Returns:
            Dict containing all validation results
        """
        results: dict[str, Any] = {"validation_timestamp": time.time(), "client_id": client_id, "validations": {}, "overall_summary": {}}

        # Define validation mappings
        validation_mappings = {
            "grade": self.validate_material_grade,
            "coating": self.validate_coating_designation,
            "finish": self.validate_finish_specification,
            "form": self.validate_form_specification,
            "surface_type": self.validate_surface_type,
            "surface_protection": self.validate_surface_protection,
        }

        # Validate each specification that exists
        for spec_name, validation_func in validation_mappings.items():
            if spec_name in specifications and specifications[spec_name] is not None:
                try:
                    results["validations"][spec_name] = validation_func(specifications[spec_name], client_id)
                except Exception as e:
                    logger.error(f"Error validating {spec_name}: {e}")
                    results["validations"][spec_name] = {
                        "is_valid": False,
                        "confidence": 0.0,
                        "found_item": None,
                        "validation_details": {"error": str(e)},
                    }

        # Check grade-coating compatibility if both exist
        if "grade" in specifications and "coating" in specifications and specifications["grade"] and specifications["coating"]:
            try:
                results["validations"]["grade_coating_compatibility"] = self.validate_grade_coating_compatibility(
                    specifications["grade"], specifications["coating"], client_id
                )
            except Exception as e:
                logger.error(f"Error validating grade-coating compatibility: {e}")
                results["validations"]["grade_coating_compatibility"] = {
                    "is_compatible": False,
                    "confidence": 0.0,
                    "validation_details": {"error": str(e)},
                }

        # Generate overall summary
        results["overall_summary"] = self.get_validation_summary(results["validations"])

        return results

    def suggest_corrections_from_text(self, field_name: str, extracted_value: Any, original_text: str, confidence: float) -> dict[str, Any]:
        """
        Suggest corrections for an extracted field by re-analyzing the original text.

        Note: With exact matching only, this method primarily validates confidence thresholds.
        """
        correction_result = {
            "needs_correction": False,
            "corrected_value": extracted_value,
            "correction_reason": None,
            "confidence_improvement": confidence,
            "source_text_reference": None,
        }

        if confidence >= 0.8:  # High confidence, probably correct
            return correction_result

        if not original_text or not isinstance(original_text, str):
            return correction_result

        # With exact matching only, we primarily flag low confidence items for manual review
        if confidence < 0.5:
            correction_result["needs_correction"] = True
            correction_result["correction_reason"] = "Low extraction confidence - manual review recommended"

        return correction_result

    def get_validation_summary(self, validations: dict[str, Any]) -> dict[str, Any]:
        """Generate a comprehensive validation summary."""
        summary = {
            "overall_valid": True,
            "overall_confidence": 0.0,
            "valid_fields": [],
            "invalid_fields": [],
            "warnings": [],
            "field_summary": {},
            "processing_time": time.time(),
            "validation_count": 0,
        }

        if not validations:
            summary["overall_valid"] = False
            return summary

        total_confidence = 0.0
        valid_count = 0
        valid_fields = cast(list[str], summary["valid_fields"])
        invalid_fields = cast(list[str], summary["invalid_fields"])
        warnings = cast(list[str], summary["warnings"])
        field_summary = cast(dict[str, Any], summary["field_summary"])

        for field_name, validation_result in validations.items():
            summary["validation_count"] = cast(int, summary["validation_count"]) + 1

            if isinstance(validation_result, dict):
                # Handle compatibility checks differently
                if "is_compatible" in validation_result:
                    is_valid = validation_result.get("is_compatible", False)
                    confidence = validation_result.get("confidence", 0.0)
                else:
                    is_valid = validation_result.get("is_valid", False)
                    confidence = validation_result.get("confidence", 0.0)

                if is_valid:
                    valid_fields.append(field_name)
                    total_confidence += confidence
                    valid_count += 1
                else:
                    invalid_fields.append(field_name)
                    summary["overall_valid"] = False

                field_summary[field_name] = {
                    "valid": is_valid,
                    "confidence": confidence,
                    "details": validation_result.get("validation_details", {}),
                }

        if valid_count > 0:
            summary["overall_confidence"] = total_confidence / float(valid_count)

        if cast(float, summary["overall_confidence"]) < 0.8:
            warnings.append("Low confidence in validation results")

        if len(invalid_fields) > len(valid_fields):
            warnings.append("More invalid fields than valid fields detected")

        return summary
