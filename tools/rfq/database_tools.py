import json
import logging
import time
from contextlib import contextmanager
from typing import Any, cast
from uuid import UUID

import psycopg2
import psycopg2.errors
from agno.tools import Toolkit
from psycopg2 import DatabaseError as PgDatabaseError
from psycopg2 import InterfaceError, OperationalError, ProgrammingError

from utils.exceptions import ClientConfigurationError
from utils.rfq.config import get_default_client_id

# Removed unused versioning imports

# Removed legacy schema imports - using database-driven approach

# Note: Form criteria and validation rules are now loaded dynamically from database
# Removed hard-coded imports to align with database-driven approach

# Configure logger for this module
logger = logging.getLogger(__name__)

# mypy: ignore-errors
# ruff: noqa: E501  # Ignore long lines in this file

# Fallback schemas - used only when database is completely unavailable
# These are minimal emergency fallbacks since database is the primary source
FALLBACK_VALIDATION_RULES = {
    "agent_identity": {"title": "Emergency Fallback Validator", "description": "Basic validation agent with minimal functionality"},
    "validation_algorithm": {"title": "Basic Validation Algorithm", "description": "Execute basic validation steps", "steps": []},
}

FALLBACK_VALIDATION_SCHEMA = {
    "validation_summary": {"overall_valid": None, "overall_confidence": None},
    "corrected_rfq_data": {"material_specs": []},
}

FALLBACK_NORMALIZER_RULES = {
    "agent_identity": {"title": "Emergency Fallback Normalizer", "description": "Basic normalizer agent with minimal functionality"},
    "normalization_process": {"title": "Basic Normalization Process", "description": "Execute basic normalization steps", "stages": []},
}

FALLBACK_NORMALIZER_SCHEMA = {
    "normalization_summary": {"overall_valid": None, "overall_confidence": None},
    "corrected_rfq_data": {"material_specs": []},
}


class DatabaseError(Exception):
    """Base exception for database operations."""

    pass


class DatabaseConnectionError(DatabaseError):
    """Raised when database connection fails."""

    pass


class DatabaseQueryError(DatabaseError):
    """Raised when database query fails."""

    pass


class ClientNotFoundError(DatabaseError):
    """Raised when client configuration is not found."""

    pass


class RFQDatabaseTools(Toolkit):
    def __init__(self, db_url: str, max_retries: int = 3, retry_delay: float = 1.0):
        super().__init__(name="rfq_database_tools")
        if not db_url or not isinstance(db_url, str):
            raise ValueError("Database URL must be a non-empty string")
        if db_url.startswith("postgresql+psycopg://"):
            self.db_url = db_url.replace("postgresql+psycopg://", "postgresql://")
        else:
            self.db_url = db_url
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        logger.info(f"RFQ Database Tools initialized for: {self._get_safe_db_info()}")

    def _get_safe_db_info(self) -> str:
        try:
            import re

            match = re.search(r"://(?:[^:]+:)?[^@]*@([^:/]+)(?::(\\d+))?/([^?]+)", self.db_url)
            if match:
                host, port, database = match.groups()
                port_str = f":{port}" if port else ""
                return f"{host}{port_str}/{database}"
            return "unknown_database"
        except Exception:
            return "database_info_unavailable"

    @contextmanager
    def get_db_connection(self):
        conn = None
        try:
            conn = psycopg2.connect(self.db_url)
            yield conn
        except OperationalError as e:
            logger.error(f"Database connection failed: {e}")
            raise DatabaseConnectionError(f"Could not connect to database: {e}") from e
        except InterfaceError as e:
            logger.error(f"Database interface error: {e}")
            raise DatabaseConnectionError(f"Database interface error: {e}") from e
        finally:
            if conn:
                conn.close()

    def execute_with_retry(self, query: str, params: tuple | None = None) -> tuple | None:
        last_exception = None
        for attempt in range(self.max_retries):
            try:
                with self.get_db_connection() as conn, conn.cursor() as cur:
                    if params:
                        cur.execute(query, params)
                    else:
                        cur.execute(query)
                    return cur.fetchone()
            except (OperationalError, InterfaceError) as e:
                last_exception = e
                logger.warning(f"Database operation failed on attempt {attempt + 1}/{self.max_retries}: {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay * (attempt + 1))
                continue
            except ProgrammingError as e:
                logger.error(f"Database query error: {e}")
                raise DatabaseQueryError(f"Invalid query execution: {e}") from e
            except PgDatabaseError as e:
                logger.error(f"Database error: {e}")
                raise DatabaseQueryError(f"Database operation failed: {e}") from e
        raise DatabaseConnectionError(f"Database operation failed after {self.max_retries} attempts: {last_exception}")

    def execute_all_with_retry(self, query: str, params: tuple | None = None) -> list[tuple]:
        """Execute query and return all results."""
        last_exception = None
        for attempt in range(self.max_retries):
            try:
                with self.get_db_connection() as conn, conn.cursor() as cur:
                    if params:
                        cur.execute(query, params)
                    else:
                        cur.execute(query)
                    return cur.fetchall()
            except (OperationalError, InterfaceError) as e:
                last_exception = e
                logger.warning(f"Database operation failed on attempt {attempt + 1}/{self.max_retries}: {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay * (attempt + 1))
                continue
            except ProgrammingError as e:
                logger.error(f"Database query error: {e}")
                raise DatabaseQueryError(f"Invalid query execution: {e}") from e
            except PgDatabaseError as e:
                logger.error(f"Database error: {e}")
                raise DatabaseQueryError(f"Database operation failed: {e}") from e
        raise DatabaseConnectionError(f"Database operation failed after {self.max_retries} attempts: {last_exception}")

    def get_versioned_schema_content(self, client_id: str | UUID, schema_type: str, version: str | None = None) -> dict[str, Any] | None:
        """Get versioned schema content using new ClientConfigurationService.

        DEPRECATED: This method now uses ClientConfigurationService for proper client preference handling.
        Consider using ClientConfigurationService.get_client_configuration() directly.

        Args:
            client_id: Client ID to get schema for
            schema_type: Type of schema (extraction_schema, validation_rules, etc.)
            version: Specific version to get (currently ignored - uses active versions from JSON config)

        Returns:
            Schema content as dict or None if not found
        """
        try:
            from utils.rfq.client_config_service import ClientConfigurationService

            # Map old schema types to new component/schema_type format
            schema_type_mapping = {
                "extraction_schema": ("extraction", "output_schema"),
                "validation_schema": ("validation", "output_schema"),
                "normalizer_schema": ("normalizer", "output_schema"),
                "formatter_schema": ("formatter", "output_schema"),
                "extraction_rules": ("extraction", "rules"),
                "validation_rules": ("validation", "rules"),
                "normalizer_rules": ("normalizer", "rules"),
                "formatter_rules": ("formatter", "rules"),
                "formatter_configuration": ("formatter", "configuration"),
            }

            if schema_type not in schema_type_mapping:
                logger.warning(f"Unknown schema type: {schema_type}")
                return None

            component, schema_subtype = schema_type_mapping[schema_type]

            # Use new service with proper client preference logic
            from db.session import get_db_context

            with get_db_context() as db_session:
                config = ClientConfigurationService.get_client_configuration(db_session, client_id, load_schemas=True)

                if component in config["schemas"] and schema_subtype in config["schemas"][component]:
                    schema_info = config["schemas"][component][schema_subtype]
                    if schema_info:
                        return schema_info["content"]

                return None

        except Exception as e:
            logger.error(f"Error loading versioned schema {schema_type} for client {client_id}: {e}")
            return None

    def get_client_configuration(self, client_id: str | UUID) -> dict[str, Any] | None:
        """Get client configuration using new versioned schema system."""
        try:
            client_id_str = str(client_id) if isinstance(client_id, UUID) else str(client_id)
            logger.info(f"Loading versioned configuration for client: {client_id_str}")

            # Load all schemas and rules using new service (which now handles client preferences)
            config = {
                "extraction_output_schema": self.get_versioned_schema_content(client_id_str, "extraction_schema"),
                "validation_output_schema": self.get_versioned_schema_content(client_id_str, "validation_schema"),
                "normalization_output_schema": self.get_versioned_schema_content(client_id_str, "normalizer_schema"),
                "extraction_rules": self.get_versioned_schema_content(client_id_str, "extraction_rules"),
                "validation_rules": self.get_versioned_schema_content(client_id_str, "validation_rules"),
                "normalization_rules": self.get_versioned_schema_content(client_id_str, "normalizer_rules"),
                "formatter_output_schema": self.get_versioned_schema_content(client_id_str, "formatter_schema"),
                "formatter_rules": self.get_versioned_schema_content(client_id_str, "formatter_rules"),
            }

            # Load model preferences from client configurations
            model_prefs_query = """
            SELECT model_preferences
            FROM rfq_client_configurations
            WHERE client_id = %s
            ORDER BY updated_at DESC
            LIMIT 1
            """
            result = self.execute_with_retry(model_prefs_query, (client_id_str,))
            if result:
                config["model_preferences"] = self._safe_json_parse(result[0], "model_preferences", client_id_str)

            # Log what was successfully loaded
            loaded_fields = [k for k, v in config.items() if v is not None]
            logger.info(f"Loaded versioned fields for client {client_id_str}: {loaded_fields}")

            return config if any(config.values()) else None

        except (DatabaseConnectionError, DatabaseQueryError):
            raise
        except Exception as e:
            logger.error(f"Unexpected error in get_client_configuration: {e}")
            raise DatabaseQueryError(f"Unexpected database error: {e}") from e

    def get_client_configuration_with_flags(self, client_id: str | UUID) -> tuple[dict[str, Any] | None, bool, bool, bool]:
        """Get client configuration with client flags including has_output_preference."""
        try:
            client_id_str = str(client_id) if isinstance(client_id, UUID) else str(client_id)
            logger.info(f"Loading versioned configuration with flags for client: {client_id_str}")

            # Get client flags
            flags_query = """
            SELECT has_kb, has_instruction, has_output_preference
            FROM rfq_clients
            WHERE client_id = %s
            """
            flags_result = self.execute_with_retry(flags_query, (client_id_str,))

            if not flags_result:
                logger.warning(f"No client found: {client_id_str}")
                return None, False, False, False

            has_kb, has_instruction, has_output_preference = flags_result
            has_kb = bool(has_kb) if has_kb is not None else False
            has_instruction = bool(has_instruction) if has_instruction is not None else False
            has_output_preference = bool(has_output_preference) if has_output_preference is not None else False

            # Get configuration using the updated method
            config = self.get_client_configuration(client_id_str)

            logger.info(
                f"Client flags for {client_id_str}: has_kb={has_kb}, has_instruction={has_instruction}, has_output_preference={has_output_preference}"
            )

            return config, has_kb, has_instruction, has_output_preference

        except (DatabaseConnectionError, DatabaseQueryError):
            raise
        except Exception as e:
            logger.error(f"Unexpected error in get_client_configuration_with_flags: {e}")
            raise DatabaseQueryError(f"Unexpected database error: {e}") from e

    def get_conditional_client_configuration(self, client_id: str | UUID) -> dict[str, Any]:
        """Get conditional client configuration using versioned schema system with fallback to generic."""
        try:
            # Get client-specific configuration with flags
            client_config, has_kb, has_instruction, has_output_preference = self.get_client_configuration_with_flags(client_id)

            # Get generic configuration as fallback
            generic_config, _, _, _ = self.get_client_configuration_with_flags(get_default_client_id())

            if not generic_config:
                logger.error("Generic configuration not found")
                raise ClientConfigurationError("Generic configuration not found in database")

            # Start with generic configuration
            merged_config = generic_config.copy()

            # Override with client-specific configurations if available
            if client_config:
                # Always use client-specific model preferences if available
                if client_config.get("model_preferences"):
                    merged_config["model_preferences"] = client_config["model_preferences"]

                # Always use client-specific normalization rules if available
                if client_config.get("normalization_rules"):
                    merged_config["normalization_rules"] = client_config["normalization_rules"]

                # Use client-specific instructions if has_instruction flag is True
                if has_instruction:
                    if client_config.get("extraction_rules"):
                        merged_config["extraction_rules"] = client_config["extraction_rules"]
                    if client_config.get("validation_rules"):
                        merged_config["validation_rules"] = client_config["validation_rules"]
                    logger.info(f"Using client-specific instructions for: {client_id}")
                else:
                    logger.info(f"Using generic instructions for: {client_id}")

                # Use client-specific output schemas if has_output_preference flag is True
                if has_output_preference:
                    if client_config.get("formatter_output_schema"):
                        merged_config["formatter_output_schema"] = client_config["formatter_output_schema"]
                    logger.info(f"Using client-specific output preferences for: {client_id}")
                else:
                    logger.info(f"Using generic output schemas for: {client_id}")

                # Always use generic schemas for extraction and validation (internal format)
                logger.info(f"Using generic schemas for extraction and validation (internal format) for: {client_id}")

            # Add client flags to configuration
            merged_config["_client_flags"] = {
                "has_kb": has_kb,
                "has_instruction": has_instruction,
                "has_output_preference": has_output_preference,
                "client_id": str(client_id),
            }

            logger.info(
                f"Configuration loaded for {client_id}: has_kb={has_kb}, has_instruction={has_instruction}, has_output_preference={has_output_preference}"
            )
            return merged_config

        except Exception as e:
            logger.error(f"Error in get_conditional_client_configuration: {e}")
            raise ClientConfigurationError(f"Failed to load conditional configuration for client {client_id}") from e

    def get_generic_schemas(self) -> dict[str, Any]:
        """Get generic schemas for internal processing using versioned schema system."""
        try:
            generic_client_id = get_default_client_id()

            # Load schemas from versioned table
            schemas = {
                "extraction_output_schema": self.get_versioned_schema_content(generic_client_id, "extraction_schema"),
                "validation_output_schema": self.get_versioned_schema_content(generic_client_id, "validation_schema"),
                "normalization_output_schema": self.get_versioned_schema_content(generic_client_id, "normalizer_schema"),
                "formatter_output_schema": self.get_versioned_schema_content(generic_client_id, "formatter_schema"),
            }

            # Validate that we have at least some schemas
            if not any(schemas.values()):
                logger.error("No generic schemas found in database")
                raise ClientConfigurationError("No generic schemas found in database")

            logger.info("Loaded generic schemas for internal processing")
            return schemas

        except Exception as e:
            logger.error(f"Error loading generic schemas: {e}")
            raise ClientConfigurationError(f"Failed to load generic schemas: {e}") from e

    def generate_dynamic_instructions(self, config_json: dict[str, Any], instruction_type: str) -> str:
        """
        Public method to generate instructions.
        Delegates to the appropriate generator based on instruction_type.

        Args:
            config_json: Configuration dictionary
            instruction_type: Type of instructions to generate (validation, extraction, normalizer, formatter)
            output_schema: Optional output schema to include in instructions

        """

        if instruction_type == "validation":
            return self._generate_systematic_validation_instructions(config_json)
        elif instruction_type == "extraction":
            return self._generate_extraction_instructions(config_json)
        elif instruction_type == "normalizer":
            return self._generate_normalization_instructions(config_json)
        elif instruction_type == "formatter":
            return self._generate_formatter_instructions(config_json)
        else:
            # Placeholder for other instruction types
            logger.warning(f"Instruction type '{instruction_type}' not fully implemented for generation.")
            return "No instructions generated."

    def get_agent_model_preferences(self, client_id: str | UUID, agent_type: str) -> dict[str, Any]:
        """Get agent-specific model preferences using versioned configuration system."""
        valid_agent_types = ["extraction", "validation", "normalizer", "formatter"]
        if agent_type not in valid_agent_types:
            raise ValueError(f"Invalid agent_type '{agent_type}'. Must be one of: {valid_agent_types}")

        try:
            # Get client configuration with flags
            client_config, has_kb, has_instruction, has_output_preference = self.get_client_configuration_with_flags(client_id)

            # Default preferences based on agent type
            default_preferences = {
                "model_id": "google/gemini-flash-1.5",
                "temperature": 0.0 if agent_type in ["extraction", "normalizer", "formatter"] else 0.1,
                "max_tokens": {
                    "extraction": 6144,
                    "validation": 8192,
                    "normalizer": 4096,
                    "formatter": 4096,
                }.get(agent_type, 6144),
                "source": "default",
            }

            if not client_config or not client_config.get("model_preferences"):
                logger.info(f"Using default model preferences for {agent_type} agent (client: {client_id})")
                return default_preferences

            model_prefs = client_config["model_preferences"]
            agent_models = model_prefs.get("agent_models", {})
            model_id = agent_models.get(agent_type)

            if not model_id:
                model_id = model_prefs.get("default_model")
            if not model_id:
                model_id = default_preferences["model_id"]

            performance_settings = model_prefs.get("performance_settings", {})
            agent_settings = performance_settings.get(agent_type, {})

            final_preferences = {
                "model_id": model_id,
                "temperature": agent_settings.get("temperature", default_preferences["temperature"]),
                "max_tokens": agent_settings.get("max_tokens", default_preferences["max_tokens"]),
                "source": "client_specific" if model_id != default_preferences["model_id"] else "default",
                "client_id": str(client_id),
                "agent_type": agent_type,
            }

            logger.info(f"Model preferences for {agent_type} agent (client: {client_id}): model={model_id}, source={final_preferences['source']}")
            return final_preferences

        except Exception as e:
            logger.error(f"Error loading model preferences for {agent_type} agent: {e}")
            logger.info(f"Falling back to default model preferences for {agent_type} agent")
            return default_preferences

    def get_fallback_model_id(self, client_id: str | UUID) -> str:
        """Get fallback model ID using versioned configuration system."""
        try:
            client_config, _, _, _ = self.get_client_configuration_with_flags(client_id)
            if client_config and client_config.get("model_preferences"):
                model_prefs = client_config["model_preferences"]
                fallback = model_prefs.get("fallback_model")
                if fallback:
                    return fallback
            return "google/gemini-flash-1.5"
        except Exception as e:
            logger.error(f"Error getting fallback model: {e}")
            return "google/gemini-flash-1.5"

    def get_formatter_output_schema(self, client_id: str | UUID, format_type: str = "standard") -> dict[str, Any] | None:
        """Get formatter output schema with output preference support.

        Args:
            client_id: Client ID
            format_type: Format type (minimal, standard, api)

        Returns:
            Formatter output schema or None if not found
        """
        try:
            client_config, has_kb, has_instruction, has_output_preference = self.get_client_configuration_with_flags(client_id)

            schema = None
            if has_output_preference and client_config and client_config.get("formatter_output_schema"):
                # Use client-specific formatter schema
                schema = client_config["formatter_output_schema"]
                logger.info(f"Using client-specific formatter schema for {client_id}")
            else:
                # Use generic formatter schema
                generic_schema = self.get_versioned_schema_content(get_default_client_id(), "formatter_schema")
                schema = generic_schema
                logger.info(f"Using generic formatter schema for {client_id}")

            if schema and format_type in schema:
                return schema[format_type]
            else:
                logger.warning(f"Format type '{format_type}' not found in formatter schema")
                return schema

        except Exception as e:
            logger.error(f"Error loading formatter output schema: {e}")
            return None

    def get_client_output_preferences(self, client_id: str | UUID) -> dict[str, Any] | None:
        """Get client output preferences from CLIENT_CONFIGURATION_SCHEMA.

        Args:
            client_id: Client ID

        Returns:
            Client output preferences or None if not found
        """
        try:
            client_id_str = str(client_id) if isinstance(client_id, UUID) else str(client_id)

            # Query client output preferences from rfq_client_configurations
            query = """
            SELECT client_output_preferences
            FROM rfq_client_configurations
            WHERE client_id = %s
            ORDER BY updated_at DESC
            LIMIT 1
            """

            result = self.execute_with_retry(query, (client_id_str,))

            if result and result[0]:
                preferences = self._safe_json_parse(result[0], "client_output_preferences", client_id_str)
                if preferences:
                    logger.info(f"Loaded client output preferences for {client_id_str}")
                    return preferences

            # Fallback to default CLIENT_CONFIGURATION_SCHEMA
            logger.info(f"Using default output preferences for {client_id_str}")
            from utils.rfq.schemas.formatter_schema import CLIENT_CONFIGURATION_SCHEMA

            return CLIENT_CONFIGURATION_SCHEMA

        except Exception as e:
            logger.error(f"Error loading client output preferences for {client_id}: {e}")
            # Return default configuration as fallback
            from utils.rfq.schemas.formatter_schema import CLIENT_CONFIGURATION_SCHEMA

            return CLIENT_CONFIGURATION_SCHEMA

    def get_default_configuration(self) -> dict[str, Any]:
        logger.info("Loading default RFQ configuration")
        return {
            "extraction_output_schema": {
                "material_specs": [
                    {
                        "specification": {
                            "grade": None,
                            "coating": None,
                            "finish": None,
                            "form": None,
                            "choice": None,
                            "thickness_min": None,
                            "thickness_max": None,
                            "width_min": None,
                            "width_max": None,
                            "length_min": None,
                            "length_max": None,
                            "height_min": None,
                            "height_max": None,
                            "inner_diameter_min": None,
                            "inner_diameter_max": None,
                            "outer_diameter_min": None,
                            "outer_diameter_max": None,
                            "weight_min": None,
                            "weight_max": None,
                            "coil_max_weight": None,
                            "yield_strength_min": None,
                            "yield_strength_max": None,
                            "tensile_strength_min": None,
                            "tensile_strength_max": None,
                            "certificate": [],
                            "mandatory_tests": [],
                            "surface_type": None,
                            "surface_protection": None,
                        },
                        "metadata": {
                            "extraction_flags": {"ambiguities": [], "assumptions": [], "conflicts_resolved": []},
                            "extraction_confidence": {},
                            "field_completeness": {"total_fields": 20, "extracted_fields": 0, "completeness_ratio": 0.0},
                        },
                    }
                ],
                "extraction_metadata": {
                    "overall_confidence": 0.0,
                    "extraction_completeness": 0.0,
                    "language_detected": "English",
                    "terminology_notes": [],
                    "processing_notes": [],
                },
            },
            "extraction_rules": {
                "schema_validation": {
                    "enforce_separation": True,
                    "specification_only_business_data": True,
                    "metadata_only_extraction_data": True,
                },
                "multiple_items_rules": {
                    "create_separate_objects_for": [
                        "different_grades",
                        "different_coatings",
                        "different_dimensions",
                        "different_forms",
                    ]
                },
                "unit_conversions": {
                    "weight_to_kg": True,
                    "dimensions_to_mm": True,
                    "strength_to_n_per_mm2": True,
                },
                "tolerance_handling": {
                    "calculate_min_max_from_tolerance": True,
                    "handle_plus_minus_notation": True,
                },
            },
        }

    def validate_client_exists(self, client_id: str | UUID) -> bool:
        """Validate that a client exists and is active."""
        try:
            client_id_str = str(client_id) if isinstance(client_id, UUID) else str(client_id)
            query = """
            SELECT COUNT(*)
            FROM rfq_clients
            WHERE client_id = %s AND status = 'active'
            """
            result = self.execute_with_retry(query, (client_id_str,))
            result_tuple = cast(tuple[int], result) if result else None
            return bool(result_tuple and result_tuple[0] > 0)
        except (DatabaseConnectionError, DatabaseQueryError):
            raise
        except Exception as e:
            logger.error(f"Unexpected error in validate_client_exists: {e}")
            raise DatabaseQueryError(f"Client validation failed: {e}") from e

    def _format_section_recursively(self, data: Any, level: int) -> str:
        """A helper function to recursively format sections into clean markdown."""
        parts = []
        heading = "#" * level

        if isinstance(data, dict):
            if "title" in data:
                parts.append(f"{heading} {data['title']}")
            if "description" in data:
                parts.append(data["description"])
            if "content" in data:
                parts.append(data["content"])

            # Handle output_schema section specifically
            if "output_schema" in data:
                schema = data["output_schema"]
                parts.append(f"\n{heading}# {schema.get('title', 'Output Schema')}")
                if "description" in schema:
                    parts.append(schema["description"])
                if "template" in schema:
                    template_value = schema["template"]
                    # Remove surrounding quotes if present
                    if isinstance(template_value, str):
                        if template_value.startswith('"') and template_value.endswith('"'):
                            template_value = template_value[1:-1]
                        parts.append(f"\n**Template:**\n```json\n{template_value}\n```")
                    else:
                        parts.append(f"\n**Template:**\n```json\n{template_value}\n```")

            # Process other keys, creating subheadings
            for key, value in data.items():
                if key in ["title", "description", "content", "output_schema"]:
                    continue

                parts.append(f"\n**{key.replace('_', ' ').title()}:**")

                if isinstance(value, list):
                    for item in value:
                        if isinstance(item, dict):
                            # Format nested dictionaries in lists (like in the rules)
                            for sub_key, sub_val in item.items():
                                parts.append(f"- **{sub_key.title()}:** {sub_val}")
                        else:
                            parts.append(f"- {item}")
                else:
                    parts.append(self._format_section_recursively(value, level + 1))

        elif isinstance(data, list):
            parts.extend([f"- {item}" for item in data])

        return "\n".join(parts)

    def _generate_extraction_instructions(self, config_json: dict[str, Any]) -> str:
        """
        Builds a clean, targeted, and human-readable prompt for the Extraction Agent.

        This function reads the structured config file and renders it into a clear
        markdown format that is easier for an LLM to follow than a raw JSON dump.
        """
        extraction_rules = config_json["extraction_rules"]

        if "agent_prompt" not in extraction_rules:
            raise ValueError("Config JSON must contain 'agent_prompt' key.")

        # Build the prompt from the main sections in a logical order
        prompt_parts = [
            self._format_section_recursively(extraction_rules["agent_prompt"]["critical_instructions"], 2),
            self._format_section_recursively(extraction_rules["extraction_rules"], 2),
            self._format_section_recursively(extraction_rules["systematic_examples"], 2),
            self._format_section_recursively(extraction_rules["agent_prompt"]["json_requirements"], 2),
        ]

        # Handle the schema injection
        schema_section = self._format_section_recursively(extraction_rules["agent_prompt"], 2)
        final_schema_section = schema_section.replace("<OUTPUT_SCHEMA_TEMPLATE>", json.dumps(config_json["extraction_output_schema"]))
        prompt_parts.append(final_schema_section)

        # Add the final reminders and the actual text to process at the end
        prompt_parts.append(self._format_section_recursively(extraction_rules["critical_reminders"], 2))

        return "\n\n---\n\n".join(prompt_parts)

    def _generate_validation_instructions_(self, config_json: dict[str, Any], confidence_threshold: float = 0.7) -> str:
        """
        Generate validation agent instructions using validation_rules from database config
        + dynamically loaded steel knowledge.
        This method is specifically for the Categorical Validation Agent.
        It ensures comprehensive coverage of correction scenarios, accurate knowledge formatting,
        and includes examples from the validation rules JSON.
        """
        if not config_json or "validation_rules" not in config_json:
            raise ValueError("Invalid config_json: missing validation_rules")

        validation_config = config_json["validation_rules"]
        if not validation_config:
            raise ValueError("validation_rules is empty in config")

        # Load steel industry knowledge based on client flags
        client_flags = config_json.get("_client_flags", {"has_kb": False, "has_instruction": False})
        client_id = client_flags.get("client_id", get_default_client_id())
        has_kb = client_flags.get("has_kb", False)

        steel_knowledge = self._load_steel_knowledge_conditional(client_id, has_kb)

        instructions = ""

        # Agent identity section
        if "agent_identity" in validation_config:
            identity = validation_config["agent_identity"]
            instructions += f"You are a {identity.get('role', 'steel RFQ validation specialist')}. "
            instructions += f"{identity.get('description', 'Expert in validating steel material specifications.')}\n\n"

        # Confidence threshold
        instructions += "## CONFIDENCE THRESHOLD\n"
        instructions += f"Re-evaluate any extracted field with confidence score below {confidence_threshold}\n\n"

        # Validation process from database config
        if "validation_process" in validation_config:
            process = validation_config["validation_process"]
            instructions += f"## {process.get('title', 'VALIDATION PROCESS')}\n"
            instructions += f"{process.get('description', '')}\n\n"

            # Process stages - Focus on categorical validation
            if "stages" in process:
                stages = process["stages"]

                # Categorical Extraction Validation & Correction Stage
                if "stage_1_categorical_validation_and_correction" in stages:
                    stage_categorical = stages["stage_1_categorical_validation_and_correction"]
                    instructions += f"### {stage_categorical.get('title', 'STAGE: CATEGORICAL VALIDATION & CORRECTION')}\n\n"
                    instructions += f"{stage_categorical.get('description', '')}\n\n"

                    # Validation checks
                    if "validation_checks" in stage_categorical:
                        instructions += "**Validation Checks:**\n"
                        for check in stage_categorical["validation_checks"]:
                            instructions += f"- **{check.get('check_type', '')}**: {check.get('description', '')}\n"
                        instructions += "\n"

                    # Correction scenarios for categorical fields
                    if "correction_scenarios" in stage_categorical:
                        instructions += "**Correction Scenarios (Categorical Fields):**\n"
                        scenarios = stage_categorical["correction_scenarios"]

                        # Detailed Combined Field Parsing
                        if "combined_field_parsing" in scenarios:
                            instructions += "- **Combined Field Parsing:** "
                            instructions += f"{scenarios['combined_field_parsing'].get('description', '')}\n"
                            instructions += f"  Rule: {scenarios['combined_field_parsing'].get('rule_detail', '')}\n"
                            instructions += "  **Example:** If extracted `coating` is 'Z100MBC' and original text mentions 'geb, gef.':\n"
                            instructions += "    - `coating` should become 'Z100'\n"
                            instructions += "    - `surface_type` should become 'Minimized spangle, improved surface' (from 'MB')\n"
                            instructions += "    - `surface_protection` should become 'Pickled and Oiled' (from 'geb, gef.', takes precedence)\n"
                            instructions += "\n"

                        # Detailed Finish Inference
                        if "finish_inference" in scenarios:
                            instructions += "- **Finish Inference:** "
                            instructions += f"{scenarios['finish_inference'].get('description', '')}\n"
                            instructions += f"  Rule: {scenarios['finish_inference'].get('rule_detail', '')}\n"
                            instructions += "  **Example Priority Cases:**\n"
                            instructions += "    - Input: 'Hot Rolled' → `finish`: 'Hot Rolled'\n"
                            instructions += "    - Input: 'Z100 coating' → `finish`: 'Hot-dip Galvanized (+Z/+GI)' (Inferred from coating group)\n"
                            instructions += (
                                "    - Input: 'DC01 grade' (no coating/direct finish) → `finish`: 'Cold Rolled' (Inferred from grade prefix)\n"
                            )
                            instructions += "    - Input: 'Warmbreitband' (no coating/direct finish) → `finish`: 'Hot Rolled'\n"
                            instructions += "\n"

                        # Detailed Default Value Application
                        if "default_value_application" in scenarios:
                            instructions += "- **Default Value Application:** "
                            instructions += f"{scenarios['default_value_application'].get('description', '')}\n"
                            instructions += f"  Rule: {scenarios['default_value_application'].get('rule_detail', '')}\n"
                            instructions += "  **Example:** If 'choice' is `None` in extracted data and not mentioned in text → `choice`: '1st'\n\n"

                        # Detailed Certificate Standardization
                        if "certificate_standardization" in scenarios:
                            instructions += "- **Certificate Standardization:** "
                            instructions += f"{scenarios['certificate_standardization'].get('description', '')}\n"
                            instructions += f"  Rule: {scenarios['certificate_standardization'].get('rule_detail', '')}\n"
                            instructions += "  **Example:** If extracted `certificate` is ['chem. Values', 'mech. Values'] → `certificate`: ['CHEM_ANALYSIS', 'INSPECT_CERT'] and `mandatory_tests`: []\n\n"

                # Database Validation Stage
                if "stage_2_database_validation" in stages:
                    stage2_database = stages["stage_2_database_validation"]
                    instructions += f"### {stage2_database.get('title', 'STAGE: DATABASE VALIDATION')}\n\n"
                    instructions += f"{stage2_database.get('description', '')}\n\n"

                    # Validation targets
                    if "validation_targets" in stage2_database:
                        instructions += "**Validation Targets (Categorical Fields):**\n"
                        for target in stage2_database["validation_targets"]:
                            catalog = target.get("catalog_type", "")
                            fields = ", ".join(target.get("field_targets", []))
                            method = target.get("validation_method", "")
                            required = "REQUIRED" if target.get("required", False) else "OPTIONAL"
                            instructions += f"- **{catalog.upper()}**: Validate {fields} using {method} ({required})\n"
                        instructions += "\n"

                    # Compatibility checks
                    if "compatibility_checks" in stage2_database:
                        instructions += "**Compatibility Checks:**\n"
                        for check in stage2_database["compatibility_checks"]:
                            instructions += f"- {check.get('description', '')}\n"
                        instructions += "\n"

                    # Suggestion generation
                    if "suggestion_generation" in stage2_database and stage2_database["suggestion_generation"].get("enabled", False):
                        instructions += "**Suggestion Generation:**\n"
                        instructions += f"- {stage2_database['suggestion_generation'].get('description', '')}\n"
                        methods = ", ".join(stage2_database["suggestion_generation"].get("methods", []))
                        instructions += f"  Methods: {methods}\n\n"

        # Input requirements
        if "input_requirements" in validation_config:
            inputs = validation_config["input_requirements"]
            instructions += f"## {inputs.get('title', 'INPUT REQUIREMENTS')}\n"
            instructions += f"{inputs.get('description', '')}\n"
            if "required_sections" in inputs:
                for section_info in inputs["required_sections"]:
                    instructions += f"- **{section_info.get('section', '')}**: {section_info.get('description', '')} (Required: {section_info.get('required', False)})\n"
            instructions += "\n"

        # Output requirements
        if "output_requirements" in validation_config:
            outputs = validation_config["output_requirements"]
            instructions += f"## {outputs.get('title', 'OUTPUT REQUIREMENTS')}\n"
            instructions += f"{outputs.get('description', '')}\n"
            instructions += f"Format: {outputs.get('format', '')}\n"
            instructions += f"Structure: {outputs.get('structure', '')}\n"
            if "critical_requirements" in outputs:
                instructions += "**Critical Output Requirements:**\n"
                for req in outputs["critical_requirements"]:
                    instructions += f"- {req}\n"
            instructions += "\n"

        # Steel industry knowledge (dynamically loaded and formatted)
        instructions += "## STEEL INDUSTRY KNOWLEDGE (Loaded from Database)\n\n"
        instructions += f"**Knowledge Source**: {steel_knowledge.get('source', 'database')}\n"
        instructions += f"**Last Updated**: {steel_knowledge.get('last_updated', 'unknown')}\n\n"
        instructions += self._format_steel_knowledge(steel_knowledge)  # Call helper to format knowledge

        # Validation examples from configuration (dynamically loaded)
        if "validation_examples" in validation_config:
            examples_config = validation_config["validation_examples"]
            instructions += f"## {examples_config.get('title', 'VALIDATION EXAMPLES')}\n"
            instructions += f"{examples_config.get('description', 'Example validation scenarios:')}\n\n"

            if "examples" in examples_config:
                for i, example in enumerate(examples_config["examples"], 1):
                    scenario = example.get("scenario", f"Scenario {i}")
                    instructions += f"### {scenario}\n"

                    if "input_rfq_text" in example:
                        instructions += "**Input RFQ Text:**\n```\n"
                        instructions += f"{example['input_rfq_text']}\n```\n"

                    if "initial_extracted_data" in example:
                        instructions += "**Initial Extracted Data:**\n```json\n"
                        instructions += json.dumps(example["initial_extracted_data"], indent=2)
                        instructions += "\n```\n"

                    if "expected_corrected_output" in example:
                        instructions += "**Expected Corrected Output:**\n```json\n"
                        instructions += json.dumps(example["expected_corrected_output"], indent=2)
                        instructions += "\n```\n"

                    if "reasoning" in example:
                        instructions += f"**Reasoning:** {example['reasoning']}\n\n---\n\n"
        else:
            # Fallback if no examples in config
            instructions += "## VALIDATION EXAMPLES\n"
            instructions += "Use the knowledge base and rules above to validate extracted specifications.\n\n"

        logger.info("Dynamic categorical validation instructions generated successfully.")
        return instructions

    def _generate_validation_instructions(self, config_json: dict[str, Any], confidence_threshold: float = 0.7) -> str:
        """
        Generates validation agent instructions by intelligently parsing the config
        and dynamically loading the required knowledge base.
        """
        if "validation_rules" not in config_json:
            raise ValueError("Invalid config_json: missing 'validation_rules'")

        rules = config_json["validation_rules"]
        prompt_parts = []

        # --- Part 1: Dynamically Build Main Instructions from Config ---

        # This section uses loops and safe .get() calls to be flexible and robust
        # It no longer has hardcoded logic for each stage or example.
        if "agent_identity" in rules:
            identity = rules["agent_identity"]
            prompt_parts.append(
                f"# {identity.get('title', 'Agent Identity')}\n"
                f"You are a **{identity.get('role', 'Systematic Steel Validator')}**.\n"
                f"{identity.get('description', '')}"
            )

        prompt_parts.append(f"## Confidence Threshold\nRe-evaluate any extracted field with a confidence score below {confidence_threshold}")

        if "validation_algorithm" in rules:
            algorithm = rules["validation_algorithm"]
            prompt_parts.append(f"## {algorithm.get('title', 'Validation Algorithm')}\n{algorithm.get('description', '')}")
            for i, step in enumerate(algorithm.get("steps", []), 1):
                prompt_parts.append(
                    f"### Step {i}: {step.get('name', '')}\n"
                    f"**Priority:** {step.get('priority', 'N/A')}\n"
                    f"**Description:** {step.get('description', '')}"
                )

        # --- Part 2: Load and Format the Knowledge Base (Re-integrated as requested) ---

        client_flags = config_json.get("_client_flags", {})
        client_id = client_flags.get("client_id", "default")
        has_kb = client_flags.get("has_kb", False)

        steel_knowledge = self._load_steel_knowledge_conditional(client_id, has_kb)
        formatted_knowledge_base = self._format_steel_knowledge(steel_knowledge)

        prompt_parts.append(
            f"## Knowledge Base\nUse the following catalogs and data to perform validation and correction.\n\n{formatted_knowledge_base}"
        )

        # --- Part 3: Add Examples and Final Reminders ---

        if "systematic_examples" in rules:
            examples_section = rules["systematic_examples"]
            prompt_parts.append(f"## {examples_section.get('title', 'Processing Examples')}\n{examples_section.get('description', '')}")
            for i, example in enumerate(examples_section.get("examples", []), 1):
                example_text = f"### Example {i}: {example.get('scenario', '')}\n"
                if "input_rfq_text" in example:
                    example_text += f"**Input Text:** `{example['input_rfq_text']}`\n"
                if "thought_process" in example:
                    reasoning = "\n".join([f"- {thought}" for thought in example["thought_process"]])
                    example_text += f"**Thought Process:**\n{reasoning}\n"
                if "expected_corrected_output" in example:
                    output_json = json.dumps(example["expected_corrected_output"], indent=2)
                    example_text += f"**Final Corrected Output:**\n```json\n{output_json}\n```"
                prompt_parts.append(example_text)

        if "execution_instructions" in rules:
            exec_section = rules["execution_instructions"]
            reminders = "\n".join([f"- {rule}" for rule in exec_section.get("critical_reminders", [])])
            prompt_parts.append(f"## {exec_section.get('title', 'Final Instructions')}\n**Critical Reminders:**\n{reminders}")

        # --- Assemble the final prompt from all generated parts ---
        logger.info("Dynamic categorical validation instructions generated successfully.")
        return "\n\n---\n\n".join(prompt_parts)

    # --- Start of _load_steel_knowledge_conditional and related helpers ---

    def _load_steel_knowledge_conditional(self, client_id: str | UUID, has_kb: bool) -> dict[str, Any]:
        """
        Load steel industry knowledge conditionally based on has_kb flag.

        Args:
            client_id: Client ID for potential client-specific knowledge
            has_kb: Whether client has custom knowledge base

        Returns:
            Dict[str, Any]: Dict containing steel industry knowledge
        """
        try:
            if has_kb:
                # Try to load client-specific knowledge first
                client_knowledge = self._load_client_specific_knowledge(client_id)
                if client_knowledge:
                    logger.info(f"Using client-specific knowledge base for: {client_id}")
                    return client_knowledge
                else:
                    logger.warning("Client marked as has_kb=True but no custom KB found, falling back to generic")

            # Load generic knowledge base
            logger.info(f"Using generic knowledge base for: {client_id}")
            return self._load_steel_knowledge()

        except Exception as e:
            logger.error(f"Error loading conditional knowledge: {e}")
            # Return minimal fallback
            return {"source": "fallback", "error": str(e), "last_updated": "unknown"}

    def _load_client_specific_knowledge(self, client_id: str | UUID) -> dict[str, Any] | None:
        """
        Load client-specific knowledge base if available.

        Args:
            client_id: Client ID to load knowledge for

        Returns:
            Optional[Dict[str, Any]]: Dict containing client-specific knowledge or None if not found
        """
        try:
            client_id_str = str(client_id)

            # Query client-specific knowledge base entries
            query = """
            SELECT rd.code, rd.name, rd.properties, ct.name as catalog_type
            FROM rfq_reference_data rd
            JOIN rfq_catalog_types ct ON rd.catalog_type_id = ct.catalog_type_id
            WHERE rd.client_id = %s AND rd.is_active = true
            ORDER BY ct.name, rd.code
            """

            # Initialize knowledge structure with expected keys for _format_steel_knowledge
            knowledge: dict[str, Any] = {
                "source": f"client_{client_id_str}",
                "last_updated": time.time(),
                "language_translations": {"german_terms": {}},
                "steel_grades": {},  # For individual grade lookups
                "grade_families": {},  # For grade family lists
                "coating_patterns": {},  # For coatings organized by category (e.g. +Z, +GI)
                "finishes": [],  # List of dicts for rich finish properties
                "certificates": {},  # Dictionary of code:name for certificates
                "surface_designations": {"surface_types": {}, "surface_protections": {}},
                "choices": {},  # Dictionary of code:name for choices
                "forms": {},  # Dictionary of code:details for forms (for form_classification_criteria)
                "form_classification_criteria": {},  # Will be populated by _load_form_criteria_from_database logic
            }

            with self.get_db_connection() as conn, conn.cursor() as cur:
                cur.execute(query, (client_id_str,))
                result_rows = cur.fetchall()

                if not result_rows:
                    return None  # No client-specific knowledge found

                for row in result_rows:
                    if len(row) >= 4:
                        code = str(row[0])
                        name = str(row[1]) if row[1] else ""
                        properties = row[2]
                        catalog_type = str(row[3]).lower()  # Ensure lowercase for matching keys

                        if catalog_type == "language_translations":
                            if properties and isinstance(properties, dict) and properties.get("language_pair") == "de_to_en":
                                knowledge["language_translations"]["german_terms"][code] = name

                        elif catalog_type == "steel_grades":
                            knowledge["steel_grades"][code] = name  # Add individual grade code:name
                            if properties and isinstance(properties, dict) and "family" in properties:
                                family = str(properties["family"])
                                knowledge["grade_families"].setdefault(family, []).append(code)

                        elif catalog_type == "choice":
                            knowledge["choices"][code] = name

                        elif catalog_type == "form":
                            if properties and isinstance(properties, dict):
                                knowledge["forms"][code] = {
                                    "dimensional_criteria": properties.get("dimensional_criteria", ""),
                                    "name": name,
                                    "short_name": code,
                                }

                        elif catalog_type == "finish":
                            # Store with 'coating_group_name' if available
                            finish_entry = {
                                "name": name,
                                "short_name": code,
                                "coating_group_name": properties.get("coating_group_name") if properties and isinstance(properties, dict) else None,
                            }
                            knowledge["finishes"].append(finish_entry)

                        elif catalog_type == "coating":  # Populate coating_patterns
                            if properties and isinstance(properties, dict) and "coating_type" in properties:
                                coating_type = str(properties["coating_type"])
                                knowledge["coating_patterns"].setdefault(coating_type, []).append(name)
                            else:  # Default category if not specified
                                knowledge["coating_patterns"].setdefault("misc", []).append(name)

                        elif catalog_type == "surface_type":
                            knowledge["surface_designations"]["surface_types"][code] = name

                        elif catalog_type == "surface_protection":
                            knowledge["surface_designations"]["surface_protections"][code] = name

                        elif catalog_type == "certificate":
                            knowledge["certificates"][code] = name  # Changed key to plural 'certificates'

                # Explicitly load form classification criteria to be consistent whether generic or client-specific
                knowledge["form_classification_criteria"] = self._load_form_criteria_from_database(client_id_str)

                logger.info(f"Loaded client-specific knowledge for {client_id_str}: {len(result_rows)} entries")
                return knowledge

        except Exception as e:
            logger.error(f"Error loading client-specific knowledge for {client_id_str}: {e}", exc_info=True)
            return None

    def _load_steel_knowledge(self) -> dict[str, Any]:
        """
        Load steel industry knowledge from database. (Generic Fallback)
        This ensures all catalogs expected by _format_steel_knowledge are present.
        """
        try:
            # Initialize knowledge structure with all expected keys for _format_steel_knowledge
            knowledge = {
                "source": "database_generic",
                "last_updated": time.time(),
                "language_translations": {"german_terms": {}},
                "steel_grades": {},
                "grade_families": {},
                "coating_patterns": {},
                "finishes": [],
                "certificates": {},
                "surface_designations": {"surface_types": {}, "surface_protections": {}},
                "choices": {},
                "forms": {},  # To store raw form data from DB
                "form_classification_criteria": {},  # Populated below
            }

            with self.get_db_connection() as conn, conn.cursor() as cur:
                # Load German translations
                cur.execute(f"""
                SELECT rd.code, rd.name
                FROM rfq_reference_data rd
                JOIN rfq_catalog_types ct ON rd.catalog_type_id = ct.catalog_type_id
                WHERE ct.name = 'language_translations' AND rd.is_active = true
                AND rd.client_id = '{get_default_client_id()}' -- Generic client_id
                """)
                for row in cur.fetchall():
                    if len(row) >= 2:
                        knowledge["language_translations"]["german_terms"][str(row[0])] = str(row[1])

                # Load steel grades and build families
                cur.execute(f"""
                SELECT rd.code, rd.name, rd.properties
                FROM rfq_reference_data rd
                JOIN rfq_catalog_types ct ON rd.catalog_type_id = ct.catalog_type_id
                WHERE ct.name = 'steel_grades' AND rd.is_active = true
                AND rd.client_id = '{get_default_client_id()}'
                """)
                for row in cur.fetchall():
                    if len(row) >= 3:
                        code = str(row[0])
                        name = str(row[1])
                        properties = row[2]
                        knowledge["steel_grades"][code] = name
                        if properties and isinstance(properties, dict) and "family" in properties:
                            family = str(properties["family"])
                            knowledge["grade_families"].setdefault(family, []).append(code)
                # Load coatings and build patterns by coating_type
                cur.execute("""
                SELECT rd.code, rd.name, rd.properties
                FROM rfq_reference_data rd
                JOIN rfq_catalog_types ct ON rd.catalog_type_id = ct.catalog_type_id
                WHERE ct.name = 'coatings' AND rd.is_active = true
                AND rd.client_id IS NULL
                """)
                for row in cur.fetchall():
                    if len(row) >= 3:
                        code = str(row[0])
                        name = str(row[1])
                        properties = row[2]
                        if properties and isinstance(properties, dict) and "coating_type" in properties:
                            coating_type = str(properties["coating_type"])
                            knowledge["coating_patterns"].setdefault(coating_type, []).append(name)
                        else:
                            knowledge["coating_patterns"].setdefault("misc", []).append(name)

                default_client_id = get_default_client_id()

                # Load choices
                cur.execute(f"""
                SELECT rd.code, rd.name
                FROM rfq_reference_data rd
                JOIN rfq_catalog_types ct ON rd.catalog_type_id = ct.catalog_type_id
                WHERE ct.name = 'choices' AND rd.is_active = true
                AND rd.client_id = '{default_client_id}'
                """)
                for row in cur.fetchall():
                    if len(row) >= 2:
                        knowledge["choices"][str(row[0])] = str(row[1])

                # Load certificates
                cur.execute(f"""
                SELECT rd.code, rd.name
                FROM rfq_reference_data rd
                JOIN rfq_catalog_types ct ON rd.catalog_type_id = ct.catalog_type_id
                WHERE ct.name = 'certificate' AND rd.is_active = true
                AND rd.client_id = '{default_client_id}'
                """)
                for row in cur.fetchall():
                    if len(row) >= 2:
                        knowledge["certificates"][str(row[0])] = str(row[1])

                # Load surface types
                cur.execute("""
                SELECT rd.code, rd.name
                FROM rfq_reference_data rd
                JOIN rfq_catalog_types ct ON rd.catalog_type_id = ct.catalog_type_id
                WHERE ct.name = 'surface_type' AND rd.is_active = true
                AND rd.client_id IS NULL
                """)
                for row in cur.fetchall():
                    if len(row) >= 2:
                        knowledge["surface_designations"]["surface_types"][str(row[0])] = str(row[1])

                # Load surface protections
                cur.execute("""
                SELECT rd.code, rd.name
                FROM rfq_reference_data rd
                JOIN rfq_catalog_types ct ON rd.catalog_type_id = ct.catalog_type_id
                WHERE ct.name = 'surface_protection' AND rd.is_active = true
                AND rd.client_id IS NULL
                """)
                for row in cur.fetchall():
                    if len(row) >= 2:
                        knowledge["surface_designations"]["surface_protections"][str(row[0])] = str(row[1])

                # Load finishes
                cur.execute("""
                SELECT rd.code, rd.name, rd.properties
                FROM rfq_reference_data rd
                JOIN rfq_catalog_types ct ON rd.catalog_type_id = ct.catalog_type_id
                WHERE ct.name = 'finishes' AND rd.is_active = true
                AND rd.client_id IS NULL
                """)
                for row in cur.fetchall():
                    if len(row) >= 3:
                        code = str(row[0])
                        name = str(row[1])
                        properties = row[2]
                        finish_entry = {
                            "name": name,
                            "short_name": code,
                            "coating_group_name": properties.get("coating_group_name") if properties and isinstance(properties, dict) else None,
                        }
                        knowledge["finishes"].append(finish_entry)

                # Load coatings and build patterns by coating_type
                cur.execute(f"""
                SELECT rd.code, rd.name, rd.properties
                FROM rfq_reference_data rd
                JOIN rfq_catalog_types ct ON rd.catalog_type_id = ct.catalog_type_id
                WHERE ct.name = 'coatings' AND rd.is_active = true
                AND rd.client_id = '{get_default_client_id()}'
                """)
                for row in cur.fetchall():
                    if len(row) >= 3:
                        code = str(row[0])
                        name = str(row[1])
                        properties = row[2]
                        if properties and isinstance(properties, dict) and "coating_type" in properties:
                            coating_type = str(properties["coating_type"])
                            knowledge["coating_patterns"].setdefault(coating_type, []).append(name)
                        else:
                            knowledge["coating_patterns"].setdefault("misc", []).append(name)

                # Load surface types
                cur.execute(f"""
                SELECT rd.code, rd.name
                FROM rfq_reference_data rd
                JOIN rfq_catalog_types ct ON rd.catalog_type_id = ct.catalog_type_id
                WHERE ct.name = 'surface_types' AND rd.is_active = true
                AND rd.client_id = '{get_default_client_id()}'
                """)
                for row in cur.fetchall():
                    if len(row) >= 2:
                        knowledge["surface_designations"]["surface_types"][str(row[0])] = str(row[1])

                # Load surface protections
                cur.execute(f"""
                SELECT rd.code, rd.name
                FROM rfq_reference_data rd
                JOIN rfq_catalog_types ct ON rd.catalog_type_id = ct.catalog_type_id
                WHERE ct.name = 'surface_protections' AND rd.is_active = true
                AND rd.client_id = '{get_default_client_id()}'
                """)
                for row in cur.fetchall():
                    if len(row) >= 2:
                        knowledge["surface_designations"]["surface_protections"][str(row[0])] = str(row[1])

                # Load choices
                cur.execute(f"""
                SELECT rd.code, rd.name
                FROM rfq_reference_data rd
                JOIN rfq_catalog_types ct ON rd.catalog_type_id = ct.catalog_type_id
                WHERE ct.name = 'choices' AND rd.is_active = true
                AND rd.client_id = '{get_default_client_id()}'
                """)
                for row in cur.fetchall():
                    if len(row) >= 2:
                        knowledge["choices"][str(row[0])] = str(row[1])

                # Load certificates
                cur.execute(f"""
                SELECT rd.code, rd.name
                FROM rfq_reference_data rd
                JOIN rfq_catalog_types ct ON rd.catalog_type_id = ct.catalog_type_id
                WHERE ct.name = 'certificate' AND rd.is_active = true
                AND rd.client_id = '{get_default_client_id()}'
                """)
                for row in cur.fetchall():
                    if len(row) >= 2:
                        knowledge["certificates"][str(row[0])] = str(row[1])

                # Load finishes
                cur.execute(f"""
                SELECT rd.code, rd.name, rd.properties
                FROM rfq_reference_data rd
                JOIN rfq_catalog_types ct ON rd.catalog_type_id = ct.catalog_type_id
                WHERE ct.name = 'finish' AND rd.is_active = true
                AND rd.client_id = '{get_default_client_id()}'
                """)
                for row in cur.fetchall():
                    if len(row) >= 3:
                        code = str(row[0])
                        name = str(row[1])
                        properties = row[2]
                        finish_entry = {
                            "name": name,
                            "short_name": code,
                            "coating_group_name": properties.get("coating_group_name") if properties and isinstance(properties, dict) else None,
                        }
                        knowledge["finishes"].append(finish_entry)

                # Load forms (for form_classification_criteria)
                knowledge["form_classification_criteria"] = self._load_form_criteria_from_database(get_default_client_id())

            logger.info("Loaded generic steel knowledge from database")
            return knowledge

        except Exception as e:
            logger.warning(f"Failed to load generic steel knowledge from database: {e}", exc_info=True)
            # Return minimal fallback only if database completely fails
            return {
                "source": "minimal_fallback",
                "last_updated": "unknown",
                "language_translation": {"german_terms": {}},
                "steel_grades": {},
                "grade_families": {},
                "coating_patterns": {},
                "finishes": [],
                "certificates": {},
                "surface_designations": {"surface_types": {}, "surface_protections": {}},
                "choices": {},
                "form_classification_criteria": self._get_fallback_form_criteria(),
            }

    def _format_steel_knowledge(self, knowledge: dict[str, Any]) -> str:
        """
        Format steel industry knowledge for instruction text based on the database structure.
        FIXED: Proper formatting without duplications and correct data organization.
        """
        formatted = ""

        # Language Translation
        german_terms = knowledge.get("language_translations", {}).get("german_terms", {})
        if german_terms:
            formatted += "**Language Translations (German to English):**\n"
            for term, translation in sorted(german_terms.items()):
                formatted += f'- "{term}" → "{translation}"\n'
            formatted += "\n"

        # Steel Grades - Clean format
        steel_grades = knowledge.get("steel_grades", {})
        if steel_grades:
            formatted += "**Valid Steel Grades:**\n"
            # Group by families if available, otherwise list all
            grade_families = knowledge.get("grade_families", {})
            if grade_families:
                for family, grades in sorted(grade_families.items()):
                    formatted += f"- {family}: {', '.join(sorted(grades))}\n"
            else:
                # Direct listing if no families
                for code, name in sorted(steel_grades.items()):
                    if name and name != code:
                        formatted += f"- {code} ({name})\n"
                    else:
                        formatted += f"- {code}\n"
            formatted += "\n"

        # Coatings - Show all coatings organized by coating type
        coating_patterns = knowledge.get("coating_patterns", {})
        if coating_patterns:
            formatted += "**Valid Coatings:**\n"
            for coating_type, codes in sorted(coating_patterns.items()):
                # Remove duplicates and show ALL codes
                unique_codes = sorted(set(codes))
                formatted += f"- {coating_type}: {', '.join(unique_codes)}\n"
            formatted += "\n"

        # Finishes - Clean format with code: name pattern
        finishes = knowledge.get("finishes", [])
        if finishes:
            formatted += "**Valid Finishes:**\n"
            # Sort finishes by short_name (code) for consistent output
            for finish_item in sorted(finishes, key=lambda x: x.get("short_name", x.get("name", ""))):
                name = finish_item.get("name", "")
                short_name = finish_item.get("short_name", "")

                if short_name and short_name != name:
                    formatted += f"- {short_name}: {name}\n"
                else:
                    formatted += f"- {name}\n"
            formatted += "\n"

        # Certificates - Simple clean format
        certificates = knowledge.get("certificates", {})
        if certificates:
            formatted += "**Valid Certificates:**\n"
            for code, name in sorted(certificates.items()):
                formatted += f"- {code}: {name}\n"
            formatted += "\n"

        # Surface Designations - Well organized
        surface_designations = knowledge.get("surface_designations", {})
        surface_types = surface_designations.get("surface_types", {})
        surface_protections = surface_designations.get("surface_protections", {})

        if surface_types or surface_protections:
            formatted += "**Valid Surface Designations:**\n"

            if surface_types:
                formatted += "  Surface Types:\n"
                for code, name in sorted(surface_types.items()):
                    formatted += f"    - {code}: {name}\n"

            if surface_protections:
                formatted += "  Surface Protections:\n"
                for code, name in sorted(surface_protections.items()):
                    formatted += f"    - {code}: {name}\n"
            formatted += "\n"

        # Quality Choices
        choices = knowledge.get("choices", {})
        if choices:
            formatted += "**Valid Quality Choices:**\n"
            choices_list = sorted(choices.keys())
            formatted += f"- {', '.join(choices_list)}\n\n"

        # Form Classification Criteria
        form_criteria = knowledge.get("form_classification_criteria", {})
        if form_criteria:
            formatted += "**Form Classification Criteria:**\n"
            for form_code, details in sorted(form_criteria.items(), key=lambda item: item[1].get("name", item[0])):
                name = details.get("name", form_code)
                dimensional_criteria = details.get("dimensional_criteria", "No criteria defined")
                formatted += f"- {name} ({form_code}): {dimensional_criteria}\n"
            formatted += "\n"

        return formatted

    def _safe_json_parse(self, json_str: Any, field_name: str, client_id: str = "unknown") -> dict[str, Any] | list[Any] | None:
        """
        Safely parse JSON string with comprehensive error handling and logging.

        Args:
            json_str: The value to parse (string, dict, or None)
            field_name: Name of the field for logging purposes
            client_id: Client ID for logging context

        Returns:
            Parsed dictionary/list or None if parsing fails
        """
        if json_str is None:
            logger.debug(f"Field '{field_name}' is None for client {client_id}")
            return None

        if isinstance(json_str, dict | list):
            # Already parsed (shouldn't happen with psycopg2, but defensive)
            logger.debug(f"Field '{field_name}' already parsed for client {client_id}")
            return json_str

        if isinstance(json_str, str):
            if not json_str.strip():
                logger.debug(f"Field '{field_name}' is empty string for client {client_id}")
                return None

            try:
                parsed = json.loads(json_str)
                logger.debug(f"Successfully parsed {field_name} for client {client_id}")
                return parsed
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse JSON for {field_name} (client {client_id}): {e}")
                logger.error(f"Raw value: {json_str[:200]}...")  # Log first 200 chars
                return None
        else:
            logger.warning(f"Unexpected type for {field_name} (client {client_id}): {type(json_str)}")
            return None

    def _load_form_criteria_from_database(self, client_id: str | UUID | None = None) -> dict[str, Any]:
        """
        Load steel form dimensional criteria from database.
        Can load client-specific or generic.
        """
        try:
            client_id_str = get_default_client_id() if client_id is None else str(client_id)

            query = """
            SELECT rd.code, rd.name, rd.properties
            FROM rfq_reference_data rd
            JOIN rfq_catalog_types ct ON rd.catalog_type_id = ct.catalog_type_id
            WHERE ct.name = 'form' AND rd.is_active = true AND rd.client_id = %s
            """

            form_criteria = {}

            with self.get_db_connection() as conn, conn.cursor() as cur:
                cur.execute(query, (client_id_str,))
                form_result_rows = cur.fetchall()
                for row in form_result_rows:
                    if len(row) >= 3:
                        code = str(row[0])
                        name = str(row[1]) if row[1] else ""
                        properties = row[2]
                        if properties and isinstance(properties, dict):
                            form_criteria[code] = {
                                "qualifying_criteria": properties.get("qualifying_criteria", []),
                                "dimensional_criteria": properties.get("dimensional_criteria", ""),
                                "category": properties.get("category", "unknown"),
                                "name": name,
                                "short_name": code,
                            }

            if not form_criteria and client_id_str != get_default_client_id():
                # If no client-specific forms, try to load generic forms
                logger.info(f"No client-specific form criteria for {client_id_str}, trying generic.")
                return self._load_form_criteria_from_database(get_default_client_id())

            if not form_criteria:  # If even generic isn't found
                logger.warning("No form criteria found in database, using minimal fallback.")
                return self._get_fallback_form_criteria()  # Fallback to minimal criteria

            return form_criteria

        except Exception as e:
            logger.warning(f"Failed to load form criteria from database: {e}", exc_info=True)
            # Fallback to minimal criteria if database load fails
            return self._get_fallback_form_criteria()

    def _get_fallback_form_criteria(self) -> dict[str, Any]:
        """Provide minimal fallback form criteria when database is unavailable."""
        return {
            "Coils": {
                "qualifying_criteria": ["material_width", "material_thickness", "material_outer_diameter"],
                "dimensional_criteria": "material_outer_diameter IS_PRESENT AND material_width > 600",
                "name": "Coils",
                "short_name": "Coils",
            },
            "Sheets": {
                "qualifying_criteria": ["material_length", "material_width", "material_thickness"],
                "dimensional_criteria": "material_thickness < 3 AND material_width IS_PRESENT",
                "name": "Sheets",
                "short_name": "Sheets",
            },
            "Plates": {
                "qualifying_criteria": ["material_length", "material_width", "material_thickness"],
                "dimensional_criteria": "material_thickness >= 3 AND material_width IS_PRESENT AND material_thickness <= 50",
                "name": "Plates",
                "short_name": "Plates",
            },
            "Heavy Plates": {
                "qualifying_criteria": ["material_length", "material_width", "material_thickness"],
                "dimensional_criteria": "material_thickness > 50 AND material_width IS_PRESENT",
                "name": "Heavy Plates",
                "short_name": "Heavy Plates",
            },
        }

    def _get_form_classification_priority(self) -> list[str]:
        """Get form classification priority rules from database or fallback."""
        try:
            # Try to load from database first
            query = """
            SELECT rd.code, rd.name, rd.properties
            FROM rfq_reference_data rd
            JOIN rfq_catalog_types ct ON rd.catalog_type_id = ct.catalog_type_id
            WHERE ct.name = 'form_priority_rules' AND rd.is_active = true
            ORDER BY rd.properties->>'priority_order' ASC
            """

            with self.get_db_connection() as conn, conn.cursor() as cur:
                cur.execute(query)
                priority_rows = cur.fetchall()

                if priority_rows:
                    return [str(row[1]) for row in priority_rows if row[1]]

        except Exception as e:
            logger.warning(f"Failed to load form priority rules from database: {e}")

        # Fallback to default priority rules
        return [
            "Check catalog form validation first",
            "If catalog validation fails or has low confidence, apply dimensional classification",
            "Use dimensional criteria to suggest correct form classification",
            "Prefer more specific forms over generic ones (e.g., Heavy Plates over Plates)",
            "Consider multiple possible matches and rank by specificity",
        ]

    def _generate_systematic_validation_instructions(self, config_json: dict[str, Any]) -> str:
        """
        Builds the complete validation prompt from the systematic rules,
        dynamic knowledge base, and dynamic output schema.

        Args:
            config_json: Configuration dictionary containing validation rules
            output_schema: Optional output schema to override the one from config
        """
        if not config_json:
            logger.error("No config_json provided to validation instruction generator")
            rules = FALLBACK_VALIDATION_RULES
            validation_output_schema = config_json["validation_output_schema"] or FALLBACK_VALIDATION_SCHEMA
        elif "validation_rules" not in config_json:
            logger.error("config_json missing 'validation_rules' key")
            rules = FALLBACK_VALIDATION_RULES
            validation_output_schema = config_json.get("validation_output_schema", FALLBACK_VALIDATION_SCHEMA)
        else:
            rules = config_json["validation_rules"]
            validation_output_schema = config_json.get("validation_output_schema", FALLBACK_VALIDATION_SCHEMA)

            # Additional validation of rules content
            if rules is None:
                logger.error("validation_rules is None - likely JSON parsing failed")
                rules = FALLBACK_VALIDATION_RULES
            elif isinstance(rules, str):
                logger.error(f"validation_rules is still a string, not parsed: {rules[:100]}...")
                # Try to parse it here as a last resort
                try:
                    rules = json.loads(rules)
                    logger.info("Successfully parsed validation_rules string to dict")
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse validation_rules string: {e}")
                    rules = FALLBACK_VALIDATION_RULES
            elif not isinstance(rules, dict):
                logger.error(f"validation_rules has unexpected type: {type(rules)}")
                rules = FALLBACK_VALIDATION_RULES
            else:
                logger.info(f"Successfully loaded validation_rules with keys: {list(rules.keys())}")

            # Validate validation_output_schema
            if validation_output_schema is None:
                logger.warning("validation_output_schema is None, using fallback")
                validation_output_schema = FALLBACK_VALIDATION_SCHEMA
            elif isinstance(validation_output_schema, str):
                logger.error(f"validation_output_schema is still a string: {validation_output_schema[:100]}...")
                try:
                    validation_output_schema = json.loads(validation_output_schema)
                    logger.info("Successfully parsed validation_output_schema string to dict")
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse validation_output_schema string: {e}")
                    validation_output_schema = FALLBACK_VALIDATION_SCHEMA
            elif not isinstance(validation_output_schema, dict):
                logger.error(f"validation_output_schema has unexpected type: {type(validation_output_schema)}")
                validation_output_schema = FALLBACK_VALIDATION_SCHEMA
            else:
                logger.info(f"Successfully loaded validation_output_schema with keys: {list(validation_output_schema.keys())}")

        # Debug: Log fallback rules structure for comparison
        logger.debug(
            f"FALLBACK_VALIDATION_RULES keys: {list(FALLBACK_VALIDATION_RULES.keys()) if isinstance(FALLBACK_VALIDATION_RULES, dict) else 'Not a dict'}"
        )
        if isinstance(FALLBACK_VALIDATION_RULES, dict) and "validation_algorithm" in FALLBACK_VALIDATION_RULES:
            fallback_algo = FALLBACK_VALIDATION_RULES["validation_algorithm"]
            logger.debug(f"Fallback algorithm keys: {list(fallback_algo.keys()) if isinstance(fallback_algo, dict) else 'Not a dict'}")
            logger.debug(f"Fallback algorithm description: {fallback_algo.get('description', 'None')}")

        # Log what we're using for generation
        logger.info("Generating validation instructions with:")
        logger.info(f"  - Rules source: {'database' if rules != FALLBACK_VALIDATION_RULES else 'fallback'}")
        logger.info(f"  - Schema source: {'database' if validation_output_schema != FALLBACK_VALIDATION_SCHEMA else 'fallback'}")

        # Debug: Check rules structure
        logger.debug(f"Rules keys: {list(rules.keys()) if isinstance(rules, dict) else 'Not a dict'}")
        if isinstance(rules, dict) and "validation_algorithm" in rules:
            algorithm = rules["validation_algorithm"]
            logger.debug(f"validation_algorithm type: {type(algorithm)}")
            logger.debug(f"validation_algorithm keys: {list(algorithm.keys()) if isinstance(algorithm, dict) else 'Not a dict'}")
        else:
            logger.warning("validation_algorithm not found in rules")

        # 1. Agent Identity
        identity = rules.get("agent_identity", {})
        logger.debug(f"Agent identity: {identity}")
        prompt_parts = [
            f"<identity>\n{identity.get('title', 'Systematic RFQ Validator')}\n{identity.get('description', 'Expert agent that validates and corrects categorical steel specifications using dynamically loaded steel industry knowledge.')}\n</identity>"
        ]

        # 2. The Algorithm/Rules - Handle different database structures
        algorithm = rules.get("validation_algorithm", {})

        # Check if database uses 'validation_process' instead of 'validation_algorithm'
        if not algorithm and "validation_process" in rules:
            logger.info("Using validation_process from database (newer structure)")
            validation_process = rules["validation_process"]
            algorithm = {
                "title": validation_process.get("title", "Validation Process"),
                "description": validation_process.get("description", "Systematic validation process"),
                "steps": validation_process.get("stages", {}).values() if "stages" in validation_process else [],
            }

        # logger.debug(f"Algorithm object: {algorithm}")

        algo_title = algorithm.get("title", "Validation Algorithm")
        algo_description = algorithm.get("description", "Execute validation steps systematically")
        logger.debug(f"Algorithm title: '{algo_title}', description: '{algo_description}'")

        algo_str = f"\n<rules>\n# {algo_title}\n{algo_description}\n"

        algorithm_steps = algorithm.get("steps", [])
        logger.debug(f"Algorithm steps count: {len(algorithm_steps)}")

        # Handle both old format (list) and new format (dict values)
        if isinstance(algorithm_steps, dict):
            algorithm_steps = list(algorithm_steps.values())

        for i, step in enumerate(algorithm_steps):
            step_num = step.get("step", i + 1)
            step_name = step.get("name", step.get("title", f"Step {i + 1}"))
            step_priority = step.get("priority", "MEDIUM")
            step_desc = step.get("description", "No description available")

            algo_str += f"\n## Step {step_num}: {step_name} (Priority: {step_priority})\n"
            algo_str += f"- **Description**: {step_desc}\n"

            if "priority_order" in step.get("algorithm", {}):
                algo_str += f"- **Priority Order**: {' > '.join(step['algorithm']['priority_order'])}\n"

        algo_str += "</rules>"
        prompt_parts.append(algo_str)

        # 3. The Dynamic Knowledge Base
        client_flags = config_json.get("_client_flags", {"has_kb": False, "client_id": get_default_client_id()})
        knowledge = self._load_steel_knowledge_conditional(client_flags["client_id"], client_flags["has_kb"])
        prompt_parts.append(
            f"\n<knowledge_base>\n# KNOWLEDGE BASE CATALOGS\nUse this data for all lookup operations required by the rules.\n{self._format_steel_knowledge(knowledge)}\n</knowledge_base>"
        )

        # 4. Chain of Thought Examples - Use database validation_examples if available
        examples = rules.get("systematic_examples", rules.get("validation_examples", {}))
        if examples and examples.get("examples"):
            example_str = f"\n<examples>\n# {examples.get('title', 'Processing Examples')}\n{examples.get('description', '')}\n"
            for ex in examples["examples"]:
                scenario = ex.get("scenario", "Example Scenario")
                example_str += f"\n### Scenario: {scenario}\n"

                if "input_rfq_text" in ex:
                    example_str += f"**Input RFQ Text:** {ex['input_rfq_text']}\n"

                if "initial_extracted_data" in ex:
                    example_str += f"**Input**: `{json.dumps(ex['initial_extracted_data'])}`\n"

                if "thought_process" in ex:
                    example_str += "**Thought Process**:\n"
                    for thought in ex["thought_process"]:
                        example_str += f"- {thought}\n"

                if "expected_corrected_output" in ex:
                    example_str += f"**Final Output**: `{json.dumps(ex['expected_corrected_output'])}`\n"

                if "reasoning" in ex:
                    example_str += f"**Reasoning:** {ex['reasoning']}\n"

                example_str += "\n---\n\n"

            example_str += "</examples>"
            prompt_parts.append(example_str)
        elif examples:
            # Fallback for different example formats
            example_str = f"\n<examples>\n# {examples.get('title', 'Validation Examples')}\n{examples.get('description', 'Example validation scenarios:')}\n</examples>"
            prompt_parts.append(example_str)

        # 5. Execution Instructions from database
        execution = rules.get("execution_instructions", {})
        if execution:
            execution_str = "\n<execution_instructions>\n# EXECUTION INSTRUCTIONS\n"

            if "instructions" in execution:
                for instruction in execution["instructions"]:
                    execution_str += f"- {instruction}\n"
                execution_str += "\n"

            if "critical_reminders" in execution:
                execution_str += "**Critical Reminders:**\n"
                for reminder in execution["critical_reminders"]:
                    execution_str += f"- {reminder}\n"

            execution_str += "</execution_instructions>"
            prompt_parts.append(execution_str)

        # 6. The Dynamic Output Format
        prompt_parts.append(
            f"\n<output_format>\n# CRITICAL: REQUIRED JSON OUTPUT FORMAT\nYour entire response MUST be a single, valid JSON object that strictly conforms to this schema. Do not add any text or markdown outside the JSON object.\n\n```json\n{json.dumps(validation_output_schema, indent=2)}\n```\n</output_format>"
        )

        logger.info("Systematic validation instructions generated successfully.")
        return "\n".join(prompt_parts)

    def _generate_normalization_instructions(self, config_json: dict[str, Any]) -> str:
        """
        Generate normalization agent instructions using normalization_rules from database config
        + dynamically loaded steel knowledge.
        This method is specifically for the Numerical Normalization Agent.
        It ensures comprehensive coverage of unit conversion, numerical validation,
        and consistency checking scenarios.

        Args:
            config_json: Configuration dictionary containing normalization rules
            output_schema: Optional output schema to override the one from config
        """
        if not config_json or "normalization_rules" not in config_json:
            logger.warning("No normalization_rules found in config, using fallback")
            rules = FALLBACK_NORMALIZER_RULES
            normalization_output_schema = FALLBACK_NORMALIZER_SCHEMA
        else:
            rules = config_json["normalization_rules"]
            # Use provided output_schema or fall back to the one from config
            normalization_output_schema = config_json.get("normalization_output_schema", FALLBACK_NORMALIZER_SCHEMA)

            # Additional validation of rules content
            if rules is None:
                logger.error("normalization_rules is None - likely JSON parsing failed")
                rules = FALLBACK_NORMALIZER_RULES
            elif isinstance(rules, str):
                logger.error(f"normalization_rules is still a string, not parsed: {rules[:100]}...")
                # Try to parse it here as a last resort
                try:
                    rules = json.loads(rules)
                    logger.info("Successfully parsed normalization_rules string to dict")
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse normalization_rules string: {e}")
                    rules = FALLBACK_NORMALIZER_RULES
            elif not isinstance(rules, dict):
                logger.error(f"normalization_rules has unexpected type: {type(rules)}")
                rules = FALLBACK_NORMALIZER_RULES
            else:
                logger.info(f"Successfully loaded normalization_rules with keys: {list(rules.keys())}")

            # Validate normalization_output_schema
            if normalization_output_schema is None:
                logger.warning("normalization_output_schema is None, using fallback")
                normalization_output_schema = FALLBACK_NORMALIZER_SCHEMA
            elif isinstance(normalization_output_schema, str):
                logger.error(f"normalization_output_schema is still a string: {normalization_output_schema[:100]}...")
                try:
                    normalization_output_schema = json.loads(normalization_output_schema)
                    logger.info("Successfully parsed normalization_output_schema string to dict")
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse normalization_output_schema string: {e}")
                    normalization_output_schema = FALLBACK_NORMALIZER_SCHEMA
            elif not isinstance(normalization_output_schema, dict):
                logger.error(f"normalization_output_schema has unexpected type: {type(normalization_output_schema)}")
                normalization_output_schema = FALLBACK_NORMALIZER_SCHEMA
            else:
                logger.info(f"Successfully loaded normalization_output_schema with keys: {list(normalization_output_schema.keys())}")

        # Log what we're using for generation
        logger.info("Generating normalization instructions with:")
        logger.info(f"  - Rules source: {'database' if rules != FALLBACK_NORMALIZER_RULES else 'fallback'}")
        logger.info(f"  - Schema source: {'database' if normalization_output_schema != FALLBACK_NORMALIZER_SCHEMA else 'fallback'}")

        # 1. Agent Identity
        identity = rules.get("agent_identity", {})
        logger.debug(f"Agent identity: {identity}")
        prompt_parts = [
            f"<identity>\n{identity.get('role', 'RFQ Numerical Normalizer')}\n{identity.get('description', 'Expert agent that validates and standardizes all numerical values and units in steel material specifications.')}</identity>"
        ]

        # 2. The Normalization Process
        process = rules.get("normalization_process", {})
        logger.debug(f"Normalization process: {process}")

        process_title = process.get("title", "Numerical Normalization Process")
        process_description = process.get("description", "Systematic normalization and validation of numerical values")
        logger.debug(f"Process title: '{process_title}', description: '{process_description}'")

        process_str = f"\n<normalization_process>\n# {process_title}\n{process_description}\n"

        # Process stages
        stages = process.get("stages", {})
        logger.debug(f"Process stages count: {len(stages)}")

        for stage_key, stage in stages.items():
            stage_title = stage.get("title", stage_key)
            stage_priority = stage.get("priority", "MEDIUM")
            stage_desc = stage.get("description", "No description available")

            process_str += f"\n## {stage_title} (Priority: {stage_priority})\n"
            process_str += f"- **Description**: {stage_desc}\n"

            # Add specific stage content based on stage type
            if "conversion_rules" in stage:
                process_str += "\n**Unit Conversions:**\n"
                for conversion_type, conversion_data in stage["conversion_rules"].items():
                    if isinstance(conversion_data, dict) and "conversions" in conversion_data:
                        process_str += f"- {conversion_data.get('description', conversion_type)}\n"
                        for unit, rule in conversion_data["conversions"].items():
                            factor = rule.get("factor", 1)
                            target = rule.get("target_unit", "standard_unit")
                            process_str += f"  • {unit} → {target} (×{factor})\n"

            if "field_mapping_rules" in stage:
                process_str += "\n**Field Mapping Validation & Correction:**\n"
                field_mapping = stage["field_mapping_rules"]

                # Process dimensional field validation
                if "dimensional_field_validation" in field_mapping:
                    dim_validation = field_mapping["dimensional_field_validation"]
                    process_str += f"- {dim_validation.get('description', 'Dimensional field validation')}\n"

                    validation_logic = dim_validation.get("validation_logic", {})
                    for detection_type, detection_rule in validation_logic.items():
                        rule_text = detection_rule.get("rule", "")
                        range_info = detection_rule.get("range", {})
                        min_val = range_info.get("min", "?")
                        max_val = range_info.get("max", "?")
                        unit = range_info.get("unit", "")
                        action = detection_rule.get("correction_action", "")

                        process_str += f"  • **{detection_type.replace('_', ' ').title()}**: {rule_text}\n"
                        process_str += f"    Range: {min_val}-{max_val} {unit}\n"
                        process_str += f"    Action: {action}\n"

                    # Add correction process steps
                    correction_process = dim_validation.get("correction_process", {})
                    if correction_process:
                        process_str += "\n  **Correction Process:**\n"
                        for step_key, step_desc in correction_process.items():
                            if isinstance(step_desc, str):
                                process_str += f"    {step_key.replace('_', ' ').title()}: {step_desc}\n"

                # Process strength field validation
                if "strength_field_validation" in field_mapping:
                    strength_validation = field_mapping["strength_field_validation"]
                    process_str += f"\n- {strength_validation.get('description', 'Strength field validation')}\n"

                    strength_logic = strength_validation.get("validation_logic", {})
                    for detection_type, detection_rule in strength_logic.items():
                        rule_text = detection_rule.get("rule", "")
                        range_info = detection_rule.get("range", {})
                        min_val = range_info.get("min", "?")
                        max_val = range_info.get("max", "?")
                        unit = range_info.get("unit", "")
                        action = detection_rule.get("correction_action", "")

                        process_str += f"  • **{detection_type.replace('_', ' ').title()}**: {rule_text}\n"
                        process_str += f"    Range: {min_val}-{max_val} {unit}\n"
                        process_str += f"    Action: {action}\n"

                # Process diameter field validation
                if "diameter_field_validation" in field_mapping:
                    diameter_validation = field_mapping["diameter_field_validation"]
                    process_str += f"\n- {diameter_validation.get('description', 'Diameter field validation')}\n"

                    diameter_logic = diameter_validation.get("validation_logic", {})
                    for detection_type, detection_rule in diameter_logic.items():
                        rule_text = detection_rule.get("rule", "")
                        range_info = detection_rule.get("range", {})
                        min_val = range_info.get("min", "?")
                        max_val = range_info.get("max", "?")
                        unit = range_info.get("unit", "")
                        action = detection_rule.get("correction_action", "")

                        process_str += f"  • **{detection_type.replace('_', ' ').title()}**: {rule_text}\n"
                        process_str += f"    Range: {min_val}-{max_val} {unit}\n"
                        process_str += f"    Action: {action}\n"

                # Add correction examples if available
                correction_examples = field_mapping.get("correction_examples", [])
                if correction_examples:
                    process_str += "\n  **Field Mapping Correction Examples:**\n"
                    for example in correction_examples:
                        scenario = example.get("scenario", "Example")
                        analysis = example.get("analysis", "No analysis")
                        process_str += f"    • **{scenario}**: {analysis}\n"

                        input_data = example.get("input", {})
                        output_data = example.get("output", {})
                        if input_data and output_data:
                            process_str += f"      Input: {json.dumps(input_data)}\n"
                            process_str += f"      Output: {json.dumps(output_data)}\n"

                        conflicts = example.get("conflicts_resolved", [])
                        if conflicts:
                            process_str += f"      Conflicts: {'; '.join(conflicts)}\n"

            if "validation_ranges" in stage:
                process_str += "\n**Validation Ranges:**\n"
                for field, range_data in stage["validation_ranges"].items():
                    min_val = range_data.get("min")
                    max_val = range_data.get("max")
                    unit = range_data.get("unit", "")
                    process_str += f"- {field}: {min_val}-{max_val} {unit}\n"

            if "consistency_checks" in stage:
                process_str += "\n**Consistency Checks:**\n"
                for check_type, check_data in stage["consistency_checks"].items():
                    process_str += f"- {check_data.get('description', check_type)}\n"

        process_str += "</normalization_process>"
        prompt_parts.append(process_str)

        # 3. Input Requirements
        input_reqs = rules.get("input_requirements", {})
        if input_reqs:
            input_str = f"\n<input_requirements>\n# {input_reqs.get('title', 'INPUT REQUIREMENTS')}\n{input_reqs.get('description', '')}\n"
            if "required_sections" in input_reqs:
                for section_info in input_reqs["required_sections"]:
                    section = section_info.get("section", "")
                    desc = section_info.get("description", "")
                    required = "REQUIRED" if section_info.get("required", False) else "OPTIONAL"
                    input_str += f"- **{section}**: {desc} ({required})\n"
            input_str += "</input_requirements>"
            prompt_parts.append(input_str)

        # 4. Output Requirements - Updated to reflect loaded schema
        output_reqs = rules.get("output_requirements", {})
        if output_reqs:
            output_str = f"\n<output_requirements>\n# {output_reqs.get('title', 'OUTPUT REQUIREMENTS')}\n{output_reqs.get('description', '')}\n"
            output_str += f"Format: {output_reqs.get('format', 'JSON')}\n"

            # Dynamic structure description based on loaded schema
            if normalization_output_schema:
                schema_keys = list(normalization_output_schema.keys()) if isinstance(normalization_output_schema, dict) else []
                if schema_keys:
                    output_str += f"Structure: JSON object with keys: {', '.join(schema_keys)}\n"
                else:
                    output_str += f"Structure: {output_reqs.get('structure', 'Normalized JSON format')}\n"

                # Add schema-specific requirements
                output_str += "\n**Schema-Specific Requirements:**\n"

                if "normalization_summary" in schema_keys:
                    output_str += "- Include normalization_summary with overall_valid, corrections_made, warnings_issued\n"

                if "corrected_rfq_data" in schema_keys:
                    output_str += "- Include corrected_rfq_data with normalized material specifications\n"

                if "conflicts_resolved" in schema_keys:
                    output_str += "- Document all changes and corrections in conflicts_resolved array\n"

                if "normalization_metadata" in schema_keys:
                    output_str += "- Include normalization_metadata with units_used and conversion_summary\n"

                output_str += "\n"
            else:
                output_str += f"Structure: {output_reqs.get('structure', 'Normalized JSON format')}\n"

            if "critical_requirements" in output_reqs:
                output_str += "**Critical Output Requirements:**\n"
                for req in output_reqs["critical_requirements"]:
                    output_str += f"- {req}\n"
            output_str += "</output_requirements>"
            prompt_parts.append(output_str)

        # 5. Execution Instructions
        execution = rules.get("execution_instructions", {})
        if execution:
            execution_str = "\n<execution_instructions>\n# EXECUTION INSTRUCTIONS\n"

            if "instructions" in execution:
                for instruction in execution["instructions"]:
                    execution_str += f"- {instruction}\n"
                execution_str += "\n"

            if "critical_reminders" in execution:
                execution_str += "**Critical Reminders:**\n"
                for reminder in execution["critical_reminders"]:
                    execution_str += f"- {reminder}\n"

            execution_str += "</execution_instructions>"
            prompt_parts.append(execution_str)

        # 6. Examples from configuration
        examples = rules.get("normalization_examples", {})
        if examples and examples.get("examples"):
            example_str = f"\n<examples>\n# {examples.get('title', 'Normalization Examples')}\n{examples.get('description', 'Example normalization scenarios:')}\n\n"
            for i, example in enumerate(examples["examples"], 1):
                scenario = example.get("scenario", f"Scenario {i}")
                example_str += f"### {scenario}\n"

                if "original_text" in example:
                    example_str += f"**Original Text:** {example['original_text']}\n"

                if "validation_input" in example:
                    example_str += f"**Validation Input:** `{json.dumps(example['validation_input'])}`\n"

                if "normalization_output" in example:
                    example_str += f"**Normalization Output:** `{json.dumps(example['normalization_output'])}`\n"

                if "conflicts_resolved" in example:
                    example_str += "**Conflicts Resolved:**\n"
                    for conflict in example["conflicts_resolved"]:
                        example_str += f"- {conflict}\n"

                example_str += "\n---\n\n"

            example_str += "</examples>"
            prompt_parts.append(example_str)
        else:
            # Fallback if no examples in config
            example_str = (
                "\n<examples>\n# NORMALIZATION EXAMPLES\nUse the rules and knowledge above to normalize numerical specifications.\n</examples>"
            )
            prompt_parts.append(example_str)

        # 7. The Dynamic Output Format
        prompt_parts.append(
            f"\n<output_format>\n# CRITICAL: REQUIRED JSON OUTPUT FORMAT\nYour entire response MUST be a single, valid JSON object that strictly conforms to this schema. Do not add any text or markdown outside the JSON object.\n\n```json\n{json.dumps(normalization_output_schema, indent=2)}\n```\n</output_format>"
        )

        logger.info("Normalization instructions generated successfully.")
        return "\n".join(prompt_parts)

    def _generate_formatter_instructions(self, config_json: dict[str, Any]) -> str:
        """
        Generate formatter instructions based on client configuration and output preferences.

        This method:
        1. Loads formatter rules from database
        2. Loads client output preferences (or generic if has_output_preference=False)
        3. Generates language-specific instructions
        4. Creates format-specific output schema

        Args:
            config_json: Configuration dictionary containing formatter rules and schema

        Returns:
            Complete formatter instruction string
        """
        logger.info("Generating formatter instructions from database configuration")

        # Load formatter rules and output schema from config
        if not config_json:
            logger.error("No config_json provided to formatter instruction generator")
            return self._get_fallback_formatter_instructions()

        formatter_rules = config_json.get("formatter_rules")
        formatter_output_schema = config_json.get("formatter_output_schema")

        # Validate formatter rules
        if not formatter_rules:
            logger.error("formatter_rules not found in config_json")
            return self._get_fallback_formatter_instructions()

        if isinstance(formatter_rules, str):
            try:
                formatter_rules = json.loads(formatter_rules)
                logger.info("Successfully parsed formatter_rules string to dict")
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse formatter_rules string: {e}")
                return self._get_fallback_formatter_instructions()

        # Validate output schema
        if not formatter_output_schema:
            logger.error("formatter_output_schema not found in config_json")
            return self._get_fallback_formatter_instructions()

        if isinstance(formatter_output_schema, str):
            try:
                formatter_output_schema = json.loads(formatter_output_schema)
                logger.info("Successfully parsed formatter_output_schema string to dict")
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse formatter_output_schema string: {e}")
                return self._get_fallback_formatter_instructions()

        # Get client output preferences
        client_id = config_json.get("client_id") or config_json.get("_client_flags", {}).get("client_id")
        output_preferences = self._get_client_output_preferences(client_id, config_json)

        logger.info(
            f"Generating formatter instructions with preferences: {output_preferences.get('output_preferences', {}).get('format_type', 'standard') if output_preferences else 'default'}"
        )

        # Build instruction components
        prompt_parts = []

        # 1. Agent Identity
        identity = formatter_rules.get("agent_identity", {})
        prompt_parts.append(
            f"<identity>\n{identity.get('role', 'RFQ Output Formatter')}\n"
            f"{identity.get('description', 'Expert agent that formats RFQ processing results according to client-specific output preferences.')}\n</identity>"
        )

        # 2. Formatting Instructions based on client preferences
        format_instructions = self._build_format_instructions(output_preferences, formatter_rules)
        prompt_parts.append(format_instructions)

        # 3. Language-specific instructions
        language_instructions = self._build_language_instructions(output_preferences)
        if language_instructions:
            prompt_parts.append(language_instructions)

        # 4. Value formatting instructions
        value_format_instructions = self._build_value_formatting_instructions(output_preferences)
        prompt_parts.append(value_format_instructions)

        # 5. Output schema based on format type
        schema_instructions = self._build_schema_instructions(output_preferences, formatter_output_schema)
        prompt_parts.append(schema_instructions)

        # 6. Examples from formatter rules
        examples = formatter_rules.get("formatting_examples", {})
        if examples and examples.get("examples"):
            example_str = f"\n<examples>\n# {examples.get('title', 'Formatting Examples')}\n"
            example_str += f"{examples.get('description', 'Example formatting scenarios:')}\n\n"

            for i, example in enumerate(examples["examples"], 1):
                scenario = example.get("scenario", f"Example {i}")
                example_str += f"### {scenario}\n"

                if "input_data" in example:
                    example_str += f"**Input:** `{json.dumps(example['input_data'])}`\n"

                if "output_data" in example:
                    example_str += f"**Output:** `{json.dumps(example['output_data'])}`\n"

                if "explanation" in example:
                    example_str += f"**Explanation:** {example['explanation']}\n"

                example_str += "\n---\n\n"

            example_str += "</examples>"
            prompt_parts.append(example_str)

        logger.info("Formatter instructions generated successfully")
        return "\n".join(prompt_parts)

    def _get_fallback_formatter_instructions(self) -> str:
        """Get fallback formatter instructions when database config fails."""
        return """
<identity>
RFQ Output Formatter
Expert agent that formats RFQ processing results according to standard output preferences.
</identity>

<formatting_instructions>
# OUTPUT FORMATTING REQUIREMENTS

## Format Type: Standard
- Use structured JSON output with clear sections
- Include all material specifications with proper grouping
- Maintain consistent field naming and structure

## Content Requirements
- Include metadata: processing information and statistics
- Include confidence scores: extraction and validation confidence
- Include warnings: any processing warnings or issues
- Language: English (en)

## Value Formatting
- Display None/empty values as: null
- Use standard decimal notation (dot for decimal point)
- Maintain consistent units (kg, mm, N/mm²)
</formatting_instructions>

<output_format>
# REQUIRED JSON OUTPUT FORMAT
Your response must be valid JSON following the standard format structure.
</output_format>
"""

    def _get_client_output_preferences(self, client_id: str | None, config_json: dict[str, Any]) -> dict[str, Any]:
        """Get client output preferences, considering has_output_preference flag."""
        try:
            # Check if client has output preferences enabled
            client_flags = config_json.get("_client_flags", {})
            has_output_preference = client_flags.get("has_output_preference", False)

            if not has_output_preference or not client_id:
                logger.info("Using generic output preferences (has_output_preference=False or no client_id)")
                # Use generic preferences from CLIENT_CONFIGURATION_SCHEMA
                from utils.rfq.schemas.formatter_schema import CLIENT_CONFIGURATION_SCHEMA

                return CLIENT_CONFIGURATION_SCHEMA

            # Try to get client-specific preferences
            client_preferences = self.get_client_output_preferences(client_id)
            if client_preferences:
                logger.info(f"Using client-specific output preferences for {client_id}")
                return client_preferences
            else:
                logger.info(f"No client-specific preferences found for {client_id}, using generic")
                from utils.rfq.schemas.formatter_schema import CLIENT_CONFIGURATION_SCHEMA

                return CLIENT_CONFIGURATION_SCHEMA

        except Exception as e:
            logger.error(f"Error getting client output preferences: {e}")
            # Fallback to default
            from utils.rfq.schemas.formatter_schema import CLIENT_CONFIGURATION_SCHEMA

            return CLIENT_CONFIGURATION_SCHEMA

    def _build_format_instructions(self, output_preferences: dict[str, Any], formatter_rules: dict[str, Any]) -> str:
        """Build format-specific instructions based on client preferences."""
        prefs = output_preferences.get("output_preferences", {})
        format_type = prefs.get("format_type", "standard")

        instructions = "\n<formatting_instructions>\n# OUTPUT FORMATTING REQUIREMENTS\n\n"
        instructions += f"## Format Type: {format_type.title()}\n"

        # Format-specific instructions
        if format_type == "minimal":
            instructions += "- Use minimal JSON structure with only essential fields\n"
            instructions += "- Exclude metadata and processing details\n"
            instructions += "- Focus on core material specifications\n"
        elif format_type == "standard":
            instructions += "- Use structured JSON output with clear sections\n"
            instructions += "- Include material properties, dimensions, and requirements\n"
            instructions += "- Group related fields logically\n"
        elif format_type == "detailed":
            instructions += "- Include comprehensive material specifications\n"
            instructions += "- Add detailed processing metadata\n"
            instructions += "- Include all available confidence scores and warnings\n"
        elif format_type == "api":
            instructions += "- Use API-friendly structure with status and data sections\n"
            instructions += "- Include metadata section for API consumers\n"
            instructions += "- Maintain consistent error handling format\n"
        elif format_type == "csv_friendly":
            instructions += "- Structure data for easy CSV conversion\n"
            instructions += "- Use flat structure where possible\n"
            instructions += "- Avoid nested objects in material specifications\n"

        # Content inclusion based on preferences
        instructions += "\n## Content Requirements\n"

        if prefs.get("include_metadata", True):
            instructions += "- Include metadata: processing information and statistics\n"
        else:
            instructions += "- Exclude metadata: omit processing information\n"

        if prefs.get("include_confidence_scores", True):
            instructions += "- Include confidence scores: extraction and validation confidence\n"
        else:
            instructions += "- Exclude confidence scores: omit confidence information\n"

        if prefs.get("include_processing_details", False):
            instructions += "- Include processing details: agent versions, timing, corrections\n"
        else:
            instructions += "- Exclude processing details: omit internal processing information\n"

        if prefs.get("include_warnings", True):
            instructions += "- Include warnings: any processing warnings or issues\n"
        else:
            instructions += "- Exclude warnings: omit warning messages\n"

        # Language setting
        output_language = prefs.get("output_langauge", "en")  # Note: keeping original typo for compatibility
        instructions += f"- Language: {output_language}\n"

        instructions += "</formatting_instructions>"
        return instructions

    def _build_language_instructions(self, output_preferences: dict[str, Any]) -> str | None:
        """Build language-specific instructions if language is not English."""
        prefs = output_preferences.get("output_preferences", {})
        output_language = prefs.get("output_langauge", "en")  # Note: keeping original typo

        if output_language == "en":
            return None  # No special instructions for English

        # Language mapping
        language_names = {
            "de": "German",
            "fr": "French",
            "es": "Spanish",
            "it": "Italian",
            "pt": "Portuguese",
            "nl": "Dutch",
            "pl": "Polish",
            "cs": "Czech",
            "hu": "Hungarian",
            "ro": "Romanian",
        }

        language_name = language_names.get(output_language, output_language.upper())

        instructions = "\n<language_instructions>\n# LANGUAGE REQUIREMENTS\n\n"
        instructions += f"## Output Language: {language_name} ({output_language})\n"
        instructions += f"- Translate all JSON field labels and values to {language_name}\n"
        instructions += "- Maintain technical terms in original language where appropriate\n"
        instructions += f"- Use proper {language_name} formatting conventions\n"
        instructions += "- Keep material grades and technical codes in original form\n"

        # Language-specific formatting notes
        if output_language == "de":
            instructions += "- Use German decimal notation if specified in value_formatting\n"
            instructions += "- Use appropriate German technical terminology\n"
        elif output_language == "fr":
            instructions += "- Use French decimal notation (comma) if specified\n"
            instructions += "- Apply French technical terminology standards\n"

        instructions += "</language_instructions>"
        return instructions

    def _build_value_formatting_instructions(self, output_preferences: dict[str, Any]) -> str:
        """Build value formatting instructions based on client preferences."""
        value_formatting = output_preferences.get("value_formatting", {})

        instructions = "\n<value_formatting>\n# VALUE FORMATTING REQUIREMENTS\n\n"

        # None value display
        none_display = value_formatting.get("None_value_display", "null")
        instructions += "## None/Empty Values\n"
        instructions += f"- Display None/empty values as: {none_display}\n"
        instructions += "- Apply consistently across all fields\n"

        # Decimal formatting
        use_german_floating = value_formatting.get("use_german_floating", False)
        if use_german_floating:
            instructions += "\n## Decimal Notation\n"
            instructions += "- Use comma (,) as decimal separator\n"
            instructions += "- Use period (.) as thousands separator\n"
            instructions += "- Example: 1.234,56 instead of 1,234.56\n"
        else:
            instructions += "\n## Decimal Notation\n"
            instructions += "- Use standard decimal notation (dot for decimal point)\n"
            instructions += "- Use comma (,) as thousands separator\n"
            instructions += "- Example: 1,234.56\n"

        # Unit formatting
        instructions += "\n## Unit Formatting\n"
        instructions += "- Maintain consistent units (kg, mm, N/mm²)\n"
        instructions += "- Include unit information where specified\n"
        instructions += "- Use standard unit abbreviations\n"

        instructions += "</value_formatting>"
        return instructions

    def _build_schema_instructions(self, output_preferences: dict[str, Any], formatter_output_schema: dict[str, Any]) -> str:
        """Build schema instructions based on format type and preferences."""
        prefs = output_preferences.get("output_preferences", {})
        format_type = prefs.get("format_type", "standard")

        instructions = "\n<output_format>\n# CRITICAL: REQUIRED JSON OUTPUT FORMAT\n"
        instructions += "Your entire response MUST be a single, valid JSON object that strictly conforms to this schema.\n"
        instructions += "Do not add any text or markdown outside the JSON object.\n"
        instructions += "Do not wrap the JSON in markdown code blocks (```json or ```).\n"
        instructions += "Return ONLY the raw JSON object, nothing else.\n\n"

        # Select appropriate schema based on format type
        if format_type in formatter_output_schema:
            selected_schema = formatter_output_schema[format_type]
            instructions += f"## {format_type.title()} Format Schema\n"
        else:
            # Fallback to standard if format type not found
            selected_schema = formatter_output_schema.get("standard", formatter_output_schema)
            instructions += "## Standard Format Schema (fallback)\n"
            logger.warning(f"Format type '{format_type}' not found in schema, using standard")

        # Filter schema based on preferences
        filtered_schema = self._filter_schema_by_preferences(selected_schema, prefs)

        instructions += f"```json\n{json.dumps(filtered_schema, indent=2)}\n```\n"
        instructions += "</output_format>"

        return instructions

    def _filter_schema_by_preferences(self, schema: dict[str, Any], preferences: dict[str, Any]) -> dict[str, Any]:
        """Filter schema fields based on client preferences."""
        if not isinstance(schema, dict):
            return schema

        filtered = schema.copy()

        # Remove metadata if not requested
        if not preferences.get("include_metadata", True):
            # Remove metadata-related fields
            filtered.pop("metadata", None)
            filtered.pop("processing_quality", None)

        # Remove confidence scores if not requested
        if not preferences.get("include_confidence_scores", True):
            # Remove confidence-related fields
            if "processing_quality" in filtered:
                pq = filtered["processing_quality"]
                if isinstance(pq, dict):
                    pq.pop("confidence_score", None)
            if "data" in filtered and isinstance(filtered["data"], dict):
                filtered["data"].pop("confidence_score", None)

        # Remove processing details if not requested
        if not preferences.get("include_processing_details", False):
            # Remove processing detail fields
            if "metadata" in filtered and isinstance(filtered["metadata"], dict):
                filtered["metadata"].pop("processing_time_ms", None)
                filtered["metadata"].pop("corrections_applied", None)
            if "processing_quality" in filtered and isinstance(filtered["processing_quality"], dict):
                filtered["processing_quality"].pop("corrections_applied", None)

        # Remove warnings if not requested
        if not preferences.get("include_warnings", True):
            # Remove warning fields
            if "metadata" in filtered and isinstance(filtered["metadata"], dict):
                filtered["metadata"].pop("warnings", None)
            if "processing_quality" in filtered and isinstance(filtered["processing_quality"], dict):
                filtered["processing_quality"].pop("warnings", None)

        return filtered

    def diagnose_validation_config(self, client_id: str | UUID) -> dict[str, Any]:
        """
        Diagnostic method to help debug validation configuration issues.
        """
        try:
            client_id_str = str(client_id)
            logger.info(f"🔍 Diagnosing validation config for client: {client_id_str}")

            # 1. Check raw database values
            config = self.get_client_configuration(client_id)
            logger.info(f"Raw config keys: {list(config.keys()) if config else 'None'}")

            if config:
                validation_rules = config.get("validation_rules")
                logger.info(f"validation_rules type: {type(validation_rules)}")
                logger.info(f"validation_rules content: {str(validation_rules)[:300] if validation_rules else 'None'}...")

                if isinstance(validation_rules, dict):
                    logger.info(f"validation_rules keys: {list(validation_rules.keys())}")
                    if "validation_algorithm" in validation_rules:
                        algo = validation_rules["validation_algorithm"]
                        logger.info(f"Algorithm type: {type(algo)}")
                        logger.info(f"Algorithm content: {algo}")
                    else:
                        logger.warning("validation_algorithm not found in validation_rules")

            # 2. Check conditional config
            conditional_config = self.get_conditional_client_configuration(client_id)
            logger.info(f"Conditional config keys: {list(conditional_config.keys()) if conditional_config else 'None'}")

            # 3. Check fallback rules
            logger.info(f"Fallback rules keys: {list(FALLBACK_VALIDATION_RULES.keys()) if FALLBACK_VALIDATION_RULES else 'Empty'}")

            return {
                "client_id": client_id_str,
                "raw_config_available": config is not None,
                "validation_rules_type": str(type(config.get("validation_rules"))) if config else "No config",
                "conditional_config_available": conditional_config is not None,
                "fallback_rules_available": bool(FALLBACK_VALIDATION_RULES),
                "fallback_has_algorithm": "validation_algorithm" in FALLBACK_VALIDATION_RULES if FALLBACK_VALIDATION_RULES else False,
            }

        except Exception as e:
            logger.error(f"Error in diagnosis: {e}")
            return {"error": str(e)}
