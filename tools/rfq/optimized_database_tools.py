"""
Optimized Database Tools for RFQ Processing.

This module provides an optimized database layer with caching and
improved query patterns using the existing database infrastructure.
"""

import json
import threading
import time
from typing import Any

from tools.rfq.database_tools import RFQDatabaseTools
from utils.exceptions import ClientConfigurationError
from utils.log import (
    get_app_logger,
)
from utils.rfq.config import get_default_client_id

logger = get_app_logger()


class SimpleCache:
    """Simple in-memory cache with TTL support."""

    def __init__(self, default_ttl: float = 300.0):
        self.cache = {}
        self.ttl = default_ttl
        self.lock = threading.RLock()
        self.hits = 0
        self.misses = 0

    def get(self, key: str) -> Any | None:
        with self.lock:
            if key in self.cache:
                value, expiry = self.cache[key]
                if time.time() < expiry:
                    self.hits += 1
                    return value
                else:
                    del self.cache[key]
            self.misses += 1
            return None

    def set(self, key: str, value: Any, ttl: float | None = None) -> None:
        with self.lock:
            expiry = time.time() + (ttl or self.ttl)
            self.cache[key] = (value, expiry)

    def clear(self) -> None:
        with self.lock:
            self.cache.clear()
            self.hits = 0
            self.misses = 0

    def get_stats(self) -> dict[str, Any]:
        with self.lock:
            total = self.hits + self.misses
            hit_rate = (self.hits / total * 100) if total > 0 else 0
            return {"hits": self.hits, "misses": self.misses, "hit_rate": f"{hit_rate:.1f}%", "size": len(self.cache)}


class OptimizedRFQDatabaseTools:
    """
    Optimized database tools with caching and performance improvements.

    Features:
    - Configuration caching to reduce database calls
    - Performance monitoring
    - Optimized query patterns
    - Thread-safe operations
    """

    def __init__(self, db_url: str):
        """
        Initialize optimized database tools.

        Args:
            db_url: Database connection URL
        """
        self.db_url = db_url
        self.sync_db_tools = RFQDatabaseTools(db_url)

        # Caches with different TTLs
        self.config_cache = SimpleCache(default_ttl=600.0)  # 10 minutes
        self.model_prefs_cache = SimpleCache(default_ttl=300.0)  # 5 minutes
        self.schema_cache = SimpleCache(default_ttl=300.0)  # 5 minutes

        # Performance metrics
        self.query_count = 0
        self.start_time = time.time()

        logger.info("✅ Optimized database tools initialized")

    def get_client_configuration(self, client_id: str) -> dict[str, Any]:
        """
        Get client configuration with caching.

        Args:
            client_id: Client ID

        Returns:
            Client configuration dictionary
        """
        cache_key = f"config_{client_id}"

        # Try cache first
        cached_config = self.config_cache.get(cache_key)
        if cached_config is not None:
            return cached_config

        # Cache miss - load from database
        try:
            start_time = time.time()
            self.query_count += 1
            config = self.sync_db_tools.get_conditional_client_configuration(client_id)

            if not config:
                logger.warning("No configuration found for client %s, using default", client_id)
                config = self.sync_db_tools.get_conditional_client_configuration(get_default_client_id())

            # Cache the result and log performance
            self.config_cache.set(cache_key, config)
            duration_ms = (time.time() - start_time) * 1000
            logger.info("DB Operation: SELECT client_config -> SUCCESS (%.2fms)", duration_ms)
            return config

        except Exception as e:
            logger.error("Failed to get client configuration: %s: %s", type(e).__name__, str(e))
            raise ClientConfigurationError(f"Failed to load client configuration: {e}") from e

    def get_agent_model_preferences(self, client_id: str, agent_type: str) -> dict[str, Any]:
        """
        Get agent model preferences with caching.

        Args:
            client_id: Client ID
            agent_type: Agent type (extraction, validation, normalizer, formatter)

        Returns:
            Model preferences dictionary
        """
        cache_key = f"model_prefs_{client_id}_{agent_type}"

        # Try cache first
        cached_prefs = self.model_prefs_cache.get(cache_key)
        if cached_prefs is not None:
            return cached_prefs

        # Cache miss - load from database
        try:
            self.query_count += 1
            prefs = self.sync_db_tools.get_agent_model_preferences(client_id, agent_type)

            # Cache the result
            self.model_prefs_cache.set(cache_key, prefs)
            return prefs

        except Exception as e:
            logger.error(f"Failed to get model preferences for {agent_type}: {e}")
            # Return default preferences
            return {
                "model_id": "google/gemini-flash-1.5",
                "temperature": 0.0 if agent_type in ["extraction", "normalizer", "formatter"] else 0.1,
                "max_tokens": {
                    "extraction": 6144,
                    "validation": 8192,
                    "normalizer": 4096,
                    "formatter": 4096,
                }.get(agent_type, 6144),
                "source": "default",
            }

    def generate_dynamic_instructions(self, config_json: dict[str, Any], instruction_type: str) -> str:
        """
        Generate dynamic instructions for agents with caching.

        Args:
            config_json: Configuration dictionary
            instruction_type: Type of instructions (extraction, validation, normalizer, formatter)

        Returns:
            Generated instructions string
        """
        # Create cache key based on config hash and instruction type
        config_hash = hash(json.dumps(config_json, sort_keys=True))
        cache_key = f"instructions_{instruction_type}_{config_hash}"

        # Try cache first
        cached_instructions = self.schema_cache.get(cache_key)
        if cached_instructions is not None:
            return cached_instructions

        # Cache miss - generate instructions
        try:
            self.query_count += 1
            instructions = self.sync_db_tools.generate_dynamic_instructions(config_json, instruction_type)

            # Cache the result
            self.schema_cache.set(cache_key, instructions)
            return instructions

        except Exception as e:
            logger.error(f"Failed to generate {instruction_type} instructions: {e}")
            return f"Error generating {instruction_type} instructions. Please use basic processing."

    def get_client_output_preferences(self, client_id: str) -> dict[str, Any]:
        """
        Get client output preferences with caching.

        Args:
            client_id: Client ID

        Returns:
            Client output preferences dictionary
        """
        cache_key = f"output_prefs_{client_id}"

        # Try cache first
        cached_prefs = self.schema_cache.get(cache_key)
        if cached_prefs is not None:
            return cached_prefs

        # Cache miss - load from database
        try:
            self.query_count += 1
            prefs = self.sync_db_tools.get_client_output_preferences(client_id)

            # Cache the result
            self.schema_cache.set(cache_key, prefs)
            return prefs

        except Exception as e:
            logger.error(f"Failed to get client output preferences: {e}")
            from utils.rfq.schemas.formatter_schema import CLIENT_CONFIGURATION_SCHEMA

            return CLIENT_CONFIGURATION_SCHEMA

    def get_conditional_client_configuration(self, client_id: str) -> dict[str, Any]:
        """
        Get conditional client configuration (alias for get_client_configuration).

        Args:
            client_id: Client ID

        Returns:
            Client configuration dictionary
        """
        return self.get_client_configuration(client_id)

    def validate_client_exists(self, client_id: str) -> bool:
        """
        Validate that a client exists and is active.

        Args:
            client_id: Client ID to validate

        Returns:
            True if client exists and is active
        """
        try:
            self.query_count += 1
            return self.sync_db_tools.validate_client_exists(client_id)
        except Exception as e:
            logger.error(f"Client validation failed: {e}")
            return False

    def batch_get_client_configurations(self, client_ids: list[str]) -> dict[str, dict[str, Any]]:
        """
        Get multiple client configurations with caching optimization.

        Args:
            client_ids: List of client IDs

        Returns:
            Dictionary mapping client_id to configuration
        """
        result = {}
        uncached_ids = []

        # Check cache for each client
        for client_id in client_ids:
            cache_key = f"config_{client_id}"
            cached_config = self.config_cache.get(cache_key)
            if cached_config is not None:
                result[client_id] = cached_config
            else:
                uncached_ids.append(client_id)

        # Load uncached configurations
        for client_id in uncached_ids:
            try:
                config = self.get_client_configuration(client_id)
                result[client_id] = config
            except Exception as e:
                logger.error(f"Failed to get config for client {client_id}: {e}")
                result[client_id] = {}

        return result

    def get_performance_stats(self) -> dict[str, Any]:
        """Get performance statistics."""
        uptime = time.time() - self.start_time

        config_stats = self.config_cache.get_stats()
        model_stats = self.model_prefs_cache.get_stats()
        schema_stats = self.schema_cache.get_stats()

        total_hits = config_stats["hits"] + model_stats["hits"] + schema_stats["hits"]
        total_misses = config_stats["misses"] + model_stats["misses"] + schema_stats["misses"]
        total_requests = total_hits + total_misses
        overall_hit_rate = (total_hits / total_requests * 100) if total_requests > 0 else 0

        return {
            "query_count": self.query_count,
            "uptime_seconds": uptime,
            "cache_hit_rate": f"{overall_hit_rate:.1f}%",
            "config_cache": config_stats,
            "model_cache": model_stats,
            "schema_cache": schema_stats,
        }

    def clear_caches(self):
        """Clear all caches."""
        self.config_cache.clear()
        self.model_prefs_cache.clear()
        self.schema_cache.clear()
        logger.info("All caches cleared")


# Global instance for singleton pattern
_global_optimized_db_tools: OptimizedRFQDatabaseTools | None = None
_db_tools_lock = threading.Lock()


def get_optimized_db_tools(db_url: str = None) -> OptimizedRFQDatabaseTools:
    """
    Get or create global optimized database tools instance.

    Args:
        db_url: Database URL (uses default if not provided)

    Returns:
        OptimizedRFQDatabaseTools instance
    """
    global _global_optimized_db_tools

    with _db_tools_lock:
        if _global_optimized_db_tools is None:
            if not db_url:
                from db.session import db_url as default_db_url

                db_url = default_db_url

            _global_optimized_db_tools = OptimizedRFQDatabaseTools(db_url)
            logger.info("✅ Global optimized database tools initialized")

        return _global_optimized_db_tools
