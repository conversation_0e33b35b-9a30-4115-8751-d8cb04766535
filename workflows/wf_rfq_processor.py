"""
Workflow for processing RFQ documents using specialized agents.
"""

import asyncio
import json
import logging
import os
import re
import time
from collections.abc import <PERSON><PERSON><PERSON><PERSON><PERSON>, Iterator
from typing import Any, cast
from uuid import uuid4

from agno.agent import RunResponse
from agno.workflow import Workflow

from agents.rfq_extractor import get_rfq_extractor
from agents.rfq_formatter import get_rfq_formatter
from agents.rfq_normalizer import get_rfq_normalizer
from agents.rfq_validator import get_rfq_validator
from db.session import db_url
from tools.rfq.database_tools import RFQDatabaseTools
from utils.exceptions import (
    ClientConfigurationError,
    RFQExtractionError,
)
from utils.log import get_app_logger
from utils.rfq.config import get_default_client_id
from utils.rfq.domain_splitter import merge_agent_outputs, split_extraction_output, validate_extraction_output, validate_merge_inputs

# Initialize application logger
logger = get_app_logger()

# Get API timeout from environment variable with default of 120 seconds for better performance
API_TIMEOUT = float(os.getenv("API_TIMEOUT", "120"))


try:
    # Load configuration from database
    db_tools = RFQDatabaseTools(db_url)
except Exception as e:
    logger.error(f"Failed to initialize database tools: {e}")
    raise RFQExtractionError("Failed to initialize database tools", details={"error": str(e)}) from e


class RFQProcessingWorkflow(Workflow):
    """Workflow for processing RFQ documents using specialized agents"""

    def __init__(self, client_id: str | None = None, performance_mode: bool = False, debug_mode: bool = False, memory_optimized: bool = False):
        """
        Initialize the RFQ processing workflow with client-specific configuration.

        Args:
            client_id: Optional client ID for client-specific processing
            performance_mode: Enable performance optimizations (removes overhead, maintains extraction logic)
            debug_mode: Enable debug mode for detailed logging and error messages
            memory_optimized: Enable memory optimization for low-memory environments
        """
        super().__init__()
        self.client_id = client_id
        self.performance_mode = performance_mode
        self.debug_mode = debug_mode
        self.memory_optimized = memory_optimized
        self.workflow_session_id = str(uuid4())
        self.config = self._get_config()

        # Memory optimization: Use shared model instances if enabled
        if self.memory_optimized:
            logger.info("🧠 Memory optimization enabled - using shared model instances")
            # Use smaller context windows and shared model instances - configuration is hardcoded for agents
        # Standard configuration is used for agents - no need to store model_config

        # Initialize the specialized agents
        self.extractor = get_rfq_extractor(
            config=self.config,
            model_id="google/gemini-pro-1.5",
            user_id=self.client_id,
            session_id=self.workflow_session_id,
            debug_mode=self.debug_mode,
            performance_mode=self.performance_mode,
        )

        # Validator with performance mode support
        self.validator = get_rfq_validator(
            config=self.config,
            model_id="google/gemini-pro-1.5",
            user_id=self.client_id,
            session_id=self.workflow_session_id,
            debug_mode=self.debug_mode,
            confidence_threshold=0.7,
            performance_mode=self.performance_mode,
        )

        # Initialize the normalizer agent with performance optimizations
        self.normalizer = get_rfq_normalizer(
            config=self.config,
            model_id="google/gemini-pro-1.5",
            user_id=self.client_id,
            session_id=self.workflow_session_id,
            debug_mode=self.debug_mode,  # Disable debug for faster processing
            performance_mode=self.performance_mode,  # Force performance mode
        )

        # Initialize the normalizer agent with performance optimizations
        self.formatter = get_rfq_formatter(
            config=self.config,
            model_id="google/gemini-pro-1.5",
            user_id=self.client_id,
            session_id=self.workflow_session_id,
            debug_mode=self.debug_mode,  # Disable debug for faster processing
            performance_mode=self.performance_mode,  # Force performance mode
        )

    def _get_config(self) -> dict[str, Any]:
        """
        Load client-specific configuration from the database using caching.

        Returns:
            Dict[str, Any]: Client-specific configuration
        """
        from agents.base import config_cache

        try:
            client_id = self.client_id or get_default_client_id()
            logger.debug(f"Loading conditional configuration for client: {client_id}")
            config = config_cache.get_client_config(client_id)

            if not config:
                raise ClientConfigurationError("No configuration available for RFQ processing")

            return config
        except Exception as e:
            logger.error(f"Failed to load client configuration: {e}")
            raise ClientConfigurationError(f"Failed to load configuration for client {self.client_id}") from e

    def _parse_json_response(self, raw_content: Any) -> dict[str, Any]:
        """
        Parse JSON response from agent with optimized extraction order.

        Args:
            raw_content: Raw response content from the agent

        Returns:
            Dict[str, Any]: Parsed JSON data

        Raises:
            json.JSONDecodeError: If JSON parsing fails
        """

        if not isinstance(raw_content, str):
            # If it's already parsed, return as-is
            return raw_content if isinstance(raw_content, dict) else {"material_specs": [], "extraction_metadata": {}}

        # Clean the content
        content = raw_content.strip()

        # Debug: Log content info to help diagnose issues (only if needed)
        if logger.isEnabledFor(logging.DEBUG):
            logger.debug(f"Parsing content - Length: {len(content)}, Type: {type(raw_content)}")
            if len(content) < 500:
                logger.debug(f"Full content: {content}")
            else:
                logger.debug(f"Content preview - First 200 chars: {content[:200]}")
                logger.debug(f"Content preview - Last 200 chars: {content[-200:]}")

        # Strategy 1: Try extracting from markdown first (most common case)
        json_content = self._extract_json_from_markdown_simple(content)
        if json_content:
            try:
                result = json.loads(json_content)
                logger.info("✅ Successfully parsed JSON from markdown")
                return result
            except json.JSONDecodeError as e:
                logger.warning(f"Markdown-extracted JSON invalid: {e}")

        # Strategy 2: Try direct JSON parsing
        try:
            result = json.loads(content)
            logger.info("✅ Successfully parsed JSON directly")
            return result
        except json.JSONDecodeError as e:
            logger.warning(f"Direct JSON parsing failed: {e}")

        # Strategy 3: Try to repair truncated JSON
        repaired_json = self._try_repair_truncated_json(content)
        if repaired_json:
            try:
                result = json.loads(repaired_json)
                logger.info("✅ Successfully parsed repaired JSON")
                return result
            except json.JSONDecodeError as e:
                logger.warning(f"Repaired JSON still invalid: {e}")

        # Final fallback - log detailed error and raise
        logger.error(f"Failed to parse JSON from agent response. Content length: {len(content)}")
        logger.error(f"Content preview (first 500 chars): {content[:500] if content else 'EMPTY CONTENT'}...")

        # Try to find where the JSON breaks
        if content:
            # Look for common JSON issues
            if "..." in content:
                logger.error("❌ JSON appears to be truncated (contains '...')")
            if content.count("{") != content.count("}"):
                logger.error(f"❌ Mismatched braces: {content.count('{')} open, {content.count('}')} close")
            if content.count("[") != content.count("]"):
                logger.error(f"❌ Mismatched brackets: {content.count('[')} open, {content.count(']')} close")

            # Show the area around the error if we have position info
            try:
                json.loads(content)
            except json.JSONDecodeError as parse_error:
                error_pos = parse_error.pos
                start = max(0, error_pos - 100)
                end = min(len(content), error_pos + 100)
                logger.error(f"❌ JSON error at position {error_pos}:")
                logger.error(f"Context: ...{content[start:end]}...")

        raise json.JSONDecodeError(f"Agent did not return valid JSON. Content: {content[:200] if content else 'EMPTY'}...", content, 0)

    def _try_repair_truncated_json(self, content: str) -> str | None:
        """
        Attempt to repair truncated JSON by completing common incomplete structures.

        Args:
            content: The potentially truncated JSON string

        Returns:
            Optional[str]: Repaired JSON string if successful, None otherwise
        """
        try:
            # Count braces and brackets to see what might be missing
            open_braces = content.count("{")
            close_braces = content.count("}")
            open_brackets = content.count("[")
            close_brackets = content.count("]")

            # If we have unclosed structures, try to close them
            if open_braces > close_braces or open_brackets > close_brackets:
                repaired = content

                # Look for incomplete strings and try to close them
                if repaired.count('"') % 2 == 1:
                    repaired += '"'

                # Close missing brackets
                missing_brackets = open_brackets - close_brackets
                if missing_brackets > 0:
                    repaired += "]" * missing_brackets

                # Close missing braces
                missing_braces = open_braces - close_braces
                if missing_braces > 0:
                    repaired += "}" * missing_braces

                logger.debug(f"Attempted JSON repair: added {missing_braces} braces, {missing_brackets} brackets")
                return repaired

        except Exception as e:
            logger.debug(f"Error during JSON repair: {e}")

        return None

    def _parse_validation_response(self, raw_content: Any, fallback_data: dict[str, Any]) -> dict[str, Any]:
        """
        Enhanced parsing for validation responses with markdown and JSON support.

        Args:
            raw_content: Raw response content from the validator
            fallback_data: Fallback data if parsing fails

        Returns:
            Dict[str, Any]: Parsed validation response
        """
        logger.info(f"Parsing validation response - content type: {type(raw_content)}")

        # Handle empty responses
        if not raw_content:
            logger.warning("Validation agent returned empty content, using fallback data")
            return self._create_fallback_validation_response(fallback_data)

        try:
            # Parse the JSON response
            if isinstance(raw_content, str):
                content = raw_content.strip()

                # Handle markdown-wrapped JSON
                if content.startswith("```"):
                    import re

                    json_match = re.search(r"```(?:json)?\s*({[\s\S]*?})\s*```", content, re.IGNORECASE)
                    if json_match:
                        content = json_match.group(1)

                parsed_response = json.loads(content)
            else:
                parsed_response = raw_content

            # Check for complete validation response format
            if "corrected_rfq_data" in parsed_response and "validation_summary" in parsed_response:
                logger.info("✅ Found complete validation response with both corrected_rfq_data and validation_summary")
                return parsed_response

            # Handle other response formats with fallbacks
            if "corrected_rfq_data" in parsed_response:
                logger.info("Found corrected_rfq_data only, adding default validation_summary")
                return {
                    "corrected_rfq_data": parsed_response["corrected_rfq_data"],
                    "validation_summary": {
                        "overall_valid": True,
                        "overall_confidence": 0.8,
                        "corrected_fields": [],
                        "validation_notes": ["Validation completed - summary generated"],
                    },
                }

            # Handle direct material specs format
            if "material_specs" in parsed_response:
                logger.info("Found material_specs format, wrapping as validation response")
                return {
                    "corrected_rfq_data": parsed_response,
                    "validation_summary": {
                        "overall_valid": True,
                        "overall_confidence": 0.8,
                        "corrected_fields": [],
                        "validation_notes": ["Material specs format detected and wrapped"],
                    },
                }

            # Unexpected format
            logger.warning(f"Unexpected validation response format: {list(parsed_response.keys())}")
            return self._create_fallback_validation_response(fallback_data)

        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse validation JSON: {e}")
            return self._create_fallback_validation_response(fallback_data)
        except Exception as e:
            logger.error(f"Failed to parse validation response: {e}")
            return self._create_fallback_validation_response(fallback_data)

    def _create_fallback_validation_response(self, original_data: dict[str, Any]) -> dict[str, Any]:
        """
        Create a fallback validation response when validator fails.

        Args:
            original_data: Original extraction data to use as fallback

        Returns:
            Dict[str, Any]: Fallback validation response
        """
        return {
            "corrected_rfq_data": original_data,
            "validation_summary": {
                "overall_valid": False,
                "validation_notes": ["Validation failed, using original extraction"],
                "fallback_used": True,
            },
        }

    def _create_fallback_normalization_response(self, numerical_data: dict[str, Any]) -> dict[str, Any]:
        """
        Create a fallback normalization response when normalizer fails.

        Args:
            numerical_data: Original numerical data to use as fallback

        Returns:
            Dict[str, Any]: Fallback normalization response matching NORMALIZER_AGENT_OUTPUT_SCHEMA
        """

        # Create response matching the expected schema
        fallback_response: dict[str, Any] = {
            "request_id": numerical_data.get("request_id"),
            "client_id": numerical_data.get("client_id"),
            "material_specs": [],
            "normalization_summary": {
                "units_standardized": 0,
                "consistency_fixes_made": 0,
                "processing_time_ms": 0,
                "warnings_issued": ["Normalization failed, using fallback with empty numerical fields"],
                "normalization_agent_version": "2.0",
                "standard_units_used": {"weight": "kg", "dimensions": "mm", "strength": "N/mm²"},
            },
        }

        # Process each material spec from the numerical data
        material_specs = numerical_data.get("material_specs", [])
        if isinstance(material_specs, list):
            for spec in material_specs:
                if isinstance(spec, dict):
                    fallback_spec = {
                        "spec_id": spec.get("spec_id"),
                        "normalized_numerical_fields": {
                            # All numerical fields set to None (preserving null values)
                            "thickness_min": None,
                            "thickness_max": None,
                            "width_min": None,
                            "width_max": None,
                            "length_min": None,
                            "length_max": None,
                            "height_min": None,
                            "height_max": None,
                            "inner_diameter_min": None,
                            "inner_diameter_max": None,
                            "outer_diameter_min": None,
                            "outer_diameter_max": None,
                            "weight_min": None,
                            "weight_max": None,
                            "coil_max_weight": None,
                            "yield_strength_min": None,
                            "yield_strength_max": None,
                            "tensile_strength_min": None,
                            "tensile_strength_max": None,
                        },
                        "normalization_metadata": {
                            "unit_conversions": [],
                            "consistency_fixes": [],
                            "range_warnings": [],
                            "tolerance_parsing": [],
                            "field_mapping_corrections": [],
                            "normalization_confidence": 0.0,
                            "normalization_errors": ["Normalization agent failed, using fallback"],
                            "normalization_warnings": [],
                        },
                        "extraction_context": spec.get("extraction_context", {}),
                    }
                    fallback_response["material_specs"].append(fallback_spec)

        logger.info(f"Created fallback normalization response with {len(fallback_response['material_specs'])} specs")
        return fallback_response

    def _extract_json_from_markdown_simple(self, content: str) -> str | None:
        """
        Extract JSON from markdown code blocks OR from content with explanatory text.
        """
        # Strategy 1: Look for markdown blocks first
        pattern = r"```(?:json)?\s*({[\s\S]*?})\s*```"
        match = re.search(pattern, content, re.IGNORECASE)

        if match:
            json_content = match.group(1).strip()
            logger.debug(f"Found JSON in markdown block, length: {len(json_content)}")
            return json_content

        # Strategy 2: Look for JSON after explanatory text
        # Find the first occurrence of { that could start a JSON object
        json_start = content.find("{")
        if json_start != -1:
            # Extract from first { to end of content
            potential_json = content[json_start:].strip()

            # Try to find the matching closing brace for the JSON object
            brace_count = 0
            json_end = -1

            for i, char in enumerate(potential_json):
                if char == "{":
                    brace_count += 1
                elif char == "}":
                    brace_count -= 1
                    if brace_count == 0:
                        json_end = i + 1
                        break

            if json_end != -1:
                json_content = potential_json[:json_end]
                logger.debug(f"Found JSON after explanatory text, length: {len(json_content)}")
                return json_content

        return None

    def _process_extracted_specs(self, extracted_specs: Any) -> tuple[list, dict]:
        """
        Process extracted specifications and normalize to consistent format.

        Args:
            extracted_specs: Raw extracted specifications in various formats

        Returns:
            tuple: (material_specs_list, extraction_metadata)
        """
        extraction_metadata = {}

        if extracted_specs is None:
            logger.warning("Extracted specs is None, using empty structure")
            return [], {}

        # Handle different input formats
        if isinstance(extracted_specs, dict):
            if "material_specs" in extracted_specs:
                # New format: {"material_specs": [...], "extraction_metadata": {...}}
                material_specs = extracted_specs.get("material_specs", [])
                extraction_metadata = extracted_specs.get("extraction_metadata", {})

                # Validate material_specs is a list
                if not isinstance(material_specs, list):
                    logger.warning(f"material_specs is not a list: {type(material_specs)}, converting...")
                    material_specs = [material_specs] if material_specs else []

                logger.info(f"Using new extractor format. Found {len(material_specs)} specs")
                logger.info(f"Extraction metadata keys: {list(extraction_metadata.keys())}")

                return material_specs, extraction_metadata

            else:
                # Single spec object - wrap in list
                logger.info("Found single specification object, wrapping in list")
                return [extracted_specs], {}

        elif isinstance(extracted_specs, list):
            # Legacy format: just an array of specs
            logger.info(f"Using legacy format. Found {len(extracted_specs)} specs")

            # Validate all items in the list are dictionaries
            valid_specs = []
            for i, spec in enumerate(extracted_specs):
                if isinstance(spec, dict):
                    valid_specs.append(spec)
                else:
                    logger.warning(f"Spec at index {i} is not a dictionary: {type(spec)}, skipping")

            return valid_specs, {}

        else:
            # Fallback: unexpected format
            logger.warning(f"Unexpected extraction format: {type(extracted_specs)}, using empty list")
            return [], {}

    def _format_extraction_for_validation(self, data: dict[str, Any], request_id: str) -> dict:
        """
        Format extracted specifications into the structure expected by the domain splitter.

        Args:
            data: Extracted data containing material_specs
            request_id: Request ID for tracking

        Returns:
            Dict: Formatted extraction data compatible with EXTRACTION_AGENT_OUTPUT_SCHEMA
        """
        # Create the structure that matches EXTRACTION_AGENT_OUTPUT_SCHEMA
        formatted_data: dict[str, Any] = {
            "request_id": request_id,
            "client_id": self.client_id,
            "original_text": data.get("original_text", ""),  # Will be set by caller
            "processing_timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "material_specs": [],  # This should be a list, not a nested dict
            "extraction_summary": {
                "total_specs_extracted": len(data.get("material_specs", [])),
                "overall_confidence": 0.8,  # Default confidence
                "processing_time_ms": 0,
                "language_detected": "English",
                "extraction_agent_version": "2.0",
            },
        }

        # Process each material specification to match the expected schema
        for i, spec in enumerate(data.get("material_specs", [])):
            if isinstance(spec, dict):
                # spec_id should already be generated by _generate_spec_ids method
                spec_id = spec.get("spec_id")
                if not spec_id:
                    # This should not happen if _generate_spec_ids was called
                    logger.error(f"❌ spec_id missing for spec {i}! This indicates _generate_spec_ids was not called properly.")
                    # Emergency fallback
                    spec_id = f"emergency_spec_{request_id}_{i}_{str(uuid4())[:6]}"
                    logger.warning(f"🆘 Emergency fallback spec_id: {spec_id}")
                else:
                    logger.debug(f"✅ Using existing spec_id: {spec_id} for spec {i}")

                # Create a properly formatted specification
                formatted_spec = {
                    "spec_id": spec_id,
                    "specification": spec.get("specification", {}),  # The actual specification data
                    "extraction_metadata": {
                        "overall_confidence": spec.get("extraction_metadata", {}).get("overall_confidence", 0.8),
                        "source_references": spec.get("extraction_metadata", {}).get("source_references", {}),
                        "extraction_flags": spec.get("extraction_metadata", {}).get("extraction_flags", []),
                        "assumptions_made": spec.get("extraction_metadata", {}).get("assumptions_made", []),
                        "field_confidence": spec.get("extraction_metadata", {}).get("field_confidence", {}),
                    },
                }
                formatted_data["material_specs"].append(formatted_spec)
                logger.debug(f"Formatted spec {i} with spec_id: {spec_id}")

        logger.info(f"Formatted {len(formatted_data['material_specs'])} specifications with unique spec_ids")
        return formatted_data

    def _generate_spec_ids(self, extracted_specs: dict[str, Any], session_id: str) -> dict[str, Any]:
        """
        Generate unique spec_ids for all material specifications immediately after extraction parsing.

        Args:
            extracted_specs: Parsed extraction output
            session_id: Session ID for uniqueness

        Returns:
            Updated extraction output with spec_ids
        """
        if not isinstance(extracted_specs, dict):
            logger.warning("Invalid extracted_specs format for spec_id generation")
            return extracted_specs

        material_specs = extracted_specs.get("material_specs", [])
        if not isinstance(material_specs, list):
            logger.warning("material_specs is not a list, cannot generate spec_ids")
            return extracted_specs

        # Generate unique spec_ids for each material specification
        for i, spec in enumerate(material_specs):
            if isinstance(spec, dict):
                # Generate unique spec_id: session_id + index + short UUID
                spec_id = f"spec_{session_id[:8]}_{i:03d}_{str(uuid4())[:6]}"
                spec["spec_id"] = spec_id
                logger.debug(f"Generated spec_id: {spec_id} for material spec {i}")
            else:
                logger.warning(f"Spec at index {i} is not a dict, skipping spec_id generation")

        logger.info(f"Generated spec_ids for {len(material_specs)} material specifications")
        return extracted_specs

    async def arun(self, rfq_body: str, request_id: str | None = None, email_id: str | None = None) -> AsyncIterator[RunResponse]:
        """
        Run the RFQ processing workflow asynchronously with PARALLEL processing for better performance.

        Args:
            rfq_body: The RFQ email body text to process
            request_id: Optional request ID for tracking
            email_id: Optional email ID for tracking

        Yields:
            RunResponse: Run response objects from the workflow
        """
        workflow_start_time = time.time()

        # Generate IDs if not provided
        if not request_id:
            request_id = f"req_{str(uuid4())[:8]}"
        if not email_id:
            email_id = f"email_{str(uuid4())[:8]}"

        # Generate a unique session ID for this workflow run
        session_id = str(uuid4())

        # Log the start of processing
        logger.info(f"🚀 Starting PARALLEL RFQ processing for client {self.client_id}, session {session_id}")

        # Step 1: Extract information using the extractor agent (REQUIRED FIRST)
        logger.info(f"⚡ Phase 1: Extraction for session {session_id}")
        extraction_start = time.time()
        try:
            extractor_response = await asyncio.wait_for(
                self.extractor.arun(message=rfq_body),
                timeout=API_TIMEOUT,
            )
        except TimeoutError:
            error_msg = f"Extraction timed out for session {session_id} after {API_TIMEOUT}s"
            logger.error(error_msg)
            raise TimeoutError(error_msg) from None
        except Exception as e:
            logger.error(f"❌ Extraction failed for session {session_id}: {e}")
            raise

        extraction_time = time.time() - extraction_start
        logger.info(f"✅ Extraction completed in {extraction_time:.2f}s")

        # Parse JSON from the response
        raw_content = extractor_response.content
        # logger.info(f"🧠 EXTRACTION AGENT RAW OUTPUT (with thinking):\n{raw_content}")
        extracted_specs = None

        try:
            extracted_specs = self._parse_json_response(raw_content)
            logger.info(f"📝 EXTRACTION AGENT OUTPUT (JSON):\n{json.dumps(extracted_specs, indent=2)}")

            # CRITICAL: Generate spec_ids immediately after parsing JSON
            extracted_specs = self._generate_spec_ids(extracted_specs, session_id)
            logger.info(f"✅ Generated spec_ids for {len(extracted_specs.get('material_specs', []))} specifications")

        except Exception as e:
            logger.error(f"Failed to parse JSON response: {e}")
            logger.error(f"Raw content: {raw_content}")
            # Fallback to empty structure
            extracted_specs = {"material_specs": [], "extraction_metadata": {}}

        # Process the extracted specifications
        # extracted_specs_final, extraction_metadata = self._process_extracted_specs(extracted_specs)

        # Add debug logging for empty extractions
        if not extracted_specs:
            logger.warning(f"🚨 No material specifications extracted! Raw specs: {extracted_specs}")
            logger.warning(f"🚨 Input RFQ body length: {len(rfq_body)} characters")
            logger.warning(f"🚨 Input preview: {rfq_body[:500] if rfq_body else 'EMPTY'}...")
        else:
            logger.info(f"✅ Successfully extracted {len(extracted_specs)} material specifications")

        # Store the extracted information in the session state
        self.session_state["extracted_rfq"] = extracted_specs["material_specs"]

        # Format extraction data for validation and normalization
        formatted_extraction_data = self._format_extraction_for_validation(extracted_specs, request_id)

        # Step 2: MODULAR DOMAIN SPLITTING
        logger.info(f"🔄 Phase 2: Domain Splitting for session {session_id}")
        split_start = time.time()

        # Add original text to formatted extraction data for domain splitting
        formatted_extraction_data["original_text"] = rfq_body

        # Validate extraction data before splitting
        validation_errors = validate_extraction_output(formatted_extraction_data)
        if validation_errors:
            logger.warning(f"⚠️ Extraction data validation issues: {validation_errors}")

        # Log spec_ids before splitting for debugging
        extraction_specs = formatted_extraction_data.get("material_specs", [])
        spec_ids = [spec.get("spec_id") for spec in extraction_specs]
        # logger.info(f"🔍 Extraction spec_ids before splitting: {spec_ids}")

        # Split extraction output into categorical and numerical data streams
        try:
            categorical_data, numerical_data = split_extraction_output(formatted_extraction_data)
            split_time = time.time() - split_start

            # Validate split results
            cat_spec_ids = [spec.get("spec_id") for spec in categorical_data.get("material_specs", [])]
            num_spec_ids = [spec.get("spec_id") for spec in numerical_data.get("material_specs", [])]

            logger.info(f"✅ Domain splitting completed in {split_time:.3f}s")
            logger.info(f"📊 Categorical specs: {len(categorical_data.get('material_specs', []))} - spec_ids: {cat_spec_ids}")
            logger.info(f"📊 Numerical specs: {len(numerical_data.get('material_specs', []))} - spec_ids: {num_spec_ids}")

            # Validate spec_id alignment
            if cat_spec_ids != num_spec_ids:
                logger.warning(f"⚠️ spec_id mismatch after splitting! Cat: {cat_spec_ids}, Num: {num_spec_ids}")

        except Exception as e:
            logger.error(f"❌ Domain splitting failed: {e}")
            # Fallback to old format for compatibility
            categorical_data = formatted_extraction_data
            numerical_data = formatted_extraction_data
            split_time = 0

        # Step 3: PARALLEL PROCESSING of Validation and Normalization
        logger.info(f"⚡ Phase 3: PARALLEL Validation + Normalization for session {session_id}")
        parallel_start = time.time()

        # Prepare prompts for modular agents with explicit spec_id preservation
        validation_prompt = f"""
        ## ORIGINAL EMAIL TEXT:
        {rfq_body}

        ## CATEGORICAL DATA TO VALIDATE:
        {json.dumps(categorical_data, indent=2)}

        CRITICAL: You MUST preserve the exact spec_id from the input for each material specification.
        Never modify or regenerate spec_ids - they are used for matching with numerical data.

        Please validate and correct the categorical specifications (grades, coatings, finishes, forms, etc.)
        against the original email text and database catalogs. Return the results in the specified JSON format.
        """

        # Use the agent's built-in instructions instead of overriding them
        input_spec_count = len(numerical_data.get("material_specs", []))
        normalization_prompt = f"""
## NUMERICAL DATA TO NORMALIZE:
{json.dumps(numerical_data, indent=2)}

## CRITICAL: INPUT CONTAINS {input_spec_count} SPECIFICATIONS
Your output MUST contain exactly {input_spec_count} material_specs with the same spec_ids.

## IMPORTANT: JSON OUTPUT REQUIREMENTS
- Return ONLY valid JSON, no explanatory text before or after
- Do not include markdown code blocks (```json)
- Ensure all braces and brackets are properly closed
- Keep response concise to avoid truncation

Please process this data according to your instructions and output schema.
        """

        # Create parallel tasks for validation and normalization with individual timing
        validation_start = time.time()
        validation_task = asyncio.create_task(
            self._run_validation_with_fallback(validation_prompt, formatted_extraction_data, session_id), name="validation"
        )

        normalization_start = time.time()
        normalization_task = asyncio.create_task(
            self._run_normalization_with_fallback(normalization_prompt, numerical_data, session_id), name="normalization"
        )

        # Wait for both tasks to complete
        try:
            gather_result = await asyncio.gather(validation_task, normalization_task, return_exceptions=True)
            validated_specs_raw, normalized_specs_raw = gather_result

            parallel_time = time.time() - parallel_start
            validation_time = time.time() - validation_start
            normalization_time = time.time() - normalization_start

            logger.info(f"✅ Parallel processing completed in {parallel_time:.2f}s")
            logger.info(f"📊 Validation agent: {validation_time:.2f}s")
            logger.info(f"📊 Normalization agent: {normalization_time:.2f}s")
            logger.info(f"📊 Bottleneck: {'Normalization' if normalization_time > validation_time else 'Validation'}")

            # Handle exceptions from parallel tasks and ensure proper typing
            validated_specs: dict[str, Any]
            if isinstance(validated_specs_raw, Exception):
                logger.error(f"Validation task failed: {validated_specs_raw}")
                validated_specs = self._create_fallback_validation_response(formatted_extraction_data)
            else:
                # Type cast needed because asyncio.gather with return_exceptions=True returns Any
                validated_specs = cast(dict[str, Any], validated_specs_raw)

            normalized_specs: dict[str, Any]
            if isinstance(normalized_specs_raw, Exception):
                logger.error(f"Normalization task failed: {normalized_specs_raw}")
                normalized_specs = validated_specs if not isinstance(validated_specs_raw, Exception) else formatted_extraction_data
            else:
                # Type cast needed because asyncio.gather with return_exceptions=True returns Any
                normalized_specs = cast(dict[str, Any], normalized_specs_raw)

        except Exception as e:
            logger.error(f"❌ Parallel processing failed for session {session_id}: {e}")
            # Fallback to sequential processing
            logger.info("🔄 Falling back to sequential processing...")
            validated_specs = await self._run_validation_with_fallback(validation_prompt, formatted_extraction_data, session_id)
            normalized_specs = await self._run_normalization_with_fallback(normalization_prompt, formatted_extraction_data, session_id)

        # Store results
        self.session_state["validated_rfq"] = validated_specs
        self.session_state["normalized_rfq"] = normalized_specs

        # MERGE VALIDATION AND NORMALIZATION OUTPUTS USING MODULAR APPROACH
        logger.info(f"🔄 Phase 4: Merging modular outputs for session {session_id}")
        merge_start = time.time()

        try:
            # Validate merge inputs before attempting merge
            merge_validation_errors = validate_merge_inputs(validated_specs, normalized_specs)
            if merge_validation_errors:
                logger.warning(f"⚠️ Merge input validation issues: {merge_validation_errors}")

            # Log agent output structures for debugging
            logger.debug(f"🔍 Validation output keys: {list(validated_specs.keys())}")
            logger.debug(f"🔍 Normalization output keys: {list(normalized_specs.keys())}")

            # Extract and validate spec_ids in agent outputs
            val_specs = validated_specs.get("corrected_rfq_data", {}).get("material_specs", [])
            norm_specs = normalized_specs.get("material_specs", [])

            # Check if validation agent preserved spec_ids
            val_spec_ids = [spec.get("spec_id") for spec in val_specs if isinstance(spec, dict)]
            val_missing_ids = [i for i, spec_id in enumerate(val_spec_ids) if not spec_id]

            # Check if normalization agent preserved spec_ids
            norm_spec_ids = [spec.get("spec_id") for spec in norm_specs if isinstance(spec, dict)]
            norm_missing_ids = [i for i, spec_id in enumerate(norm_spec_ids) if not spec_id]

            logger.info(f"🔍 Before merge - Validation specs: {len(val_specs)} with spec_ids: {val_spec_ids}")
            logger.info(f"🔍 Before merge - Normalization specs: {len(norm_specs)} with spec_ids: {norm_spec_ids}")

            # Warn about missing spec_ids
            if val_missing_ids:
                logger.error(f"❌ Validation agent failed to preserve spec_ids at indices: {val_missing_ids}")
            if norm_missing_ids:
                logger.error(f"❌ Normalization agent failed to preserve spec_ids at indices: {norm_missing_ids}")

            # Check for spec_id mismatches
            if val_spec_ids != norm_spec_ids:
                logger.warning("⚠️ spec_id mismatch between agents!")
                logger.warning(f"Validation: {val_spec_ids}")
                logger.warning(f"Normalization: {norm_spec_ids}")

            # Use the proper modular merging function
            merged_result = merge_agent_outputs(
                categorical_output=validated_specs,
                numerical_output=normalized_specs,
                extraction_summary=extracted_specs.get("extraction_summary", {}) if isinstance(extracted_specs, dict) else {},
            )
            merge_time = time.time() - merge_start

            # Validate merge results
            merged_specs = merged_result.get("material_specs", [])
            merged_spec_ids = [spec.get("spec_id") for spec in merged_specs]
            logger.info(f"✅ Modular output merging completed in {merge_time:.3f}s")
            logger.info(f"🔍 After merge - Merged specs: {len(merged_specs)} {merged_spec_ids}")

        except Exception as e:
            logger.error(f"❌ Modular merging failed: {e}")
            logger.error(f"❌ Error details: {type(e).__name__}: {str(e)}")
            # Fallback to basic merging
            merged_result = {
                "material_specs": [],
                "processing_summary": {"pipeline_errors": [f"Merge failed: {str(e)}"], "processing_status": "partial_failure"},
            }
            merge_time = time.time() - merge_start

        # Store merged result
        self.session_state["merged_rfq"] = merged_result
        self.session_state["categorical_data"] = categorical_data
        self.session_state["numerical_data"] = numerical_data

        # Calculate total processing time
        workflow_end_time = time.time()
        processing_time = workflow_end_time - workflow_start_time

        # Update processing time in normalized specs
        if "corrected_rfq_data" in normalized_specs:
            normalized_specs["corrected_rfq_data"]["processing_time"] = processing_time
        elif "normalization_summary" in normalized_specs:
            normalized_specs["processing_time"] = processing_time
        else:
            normalized_specs["processing_time"] = processing_time

        self.session_state["processing_time"] = processing_time

        # Log performance metrics
        logger.info(f"🎯 PERFORMANCE SUMMARY for session {session_id}:")
        logger.info(f"   📊 Extraction: {extraction_time:.2f}s")
        logger.info(f"   📊 Parallel Phase: {parallel_time:.2f}s")
        logger.info(f"   📊 Total Time: {processing_time:.2f}s")
        logger.info(f"   🚀 Estimated Sequential Time: {extraction_time + (parallel_time * 2):.2f}s")
        logger.info(f"   ⚡ Time Saved: {(extraction_time + (parallel_time * 2)) - processing_time:.2f}s")

        # Create final merged specifications using the modular result
        final_merged_specs = merged_result.get("material_specs", [])

        # Create the final corrected RFQ data structure that matches the expected output format
        final_corrected_data = {
            "request_id": request_id,
            "email_id": email_id,
            "client_id": self.client_id,
            "material_specs": final_merged_specs,  # Fixed: Direct list, not nested structure
            "processing_log": [
                {"step": "extraction", "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"), "client_id": self.client_id, "status": "completed"},
                {"step": "validation", "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"), "client_id": self.client_id, "status": "completed"},
                {"step": "normalization", "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"), "client_id": self.client_id, "status": "completed"},
            ],
            "processing_time": processing_time,
            "schema_info": {},
        }

        # Log final structure for debugging
        logger.info(f"Final corrected data contains {len(final_merged_specs)} material specifications")
        if final_merged_specs:
            sample_spec = final_merged_specs[0]
            logger.debug(f"Sample spec structure: spec_id={sample_spec.get('spec_id')}, fields={len(sample_spec.get('specification', {}))}")

        # Validate final result before returning
        if not final_merged_specs:
            logger.warning("⚠️ No material specifications in final merged result!")
            logger.warning(f"Categorical specs count: {len(validated_specs.get('material_specs', []))}")
            logger.warning(f"Numerical specs count: {len(normalized_specs.get('material_specs', []))}")
            logger.warning(f"Merged specs count: {len(merged_result.get('material_specs', []))}")

        # PHASE 5: FORMAT OUTPUT ACCORDING TO CLIENT PREFERENCES
        logger.info(f"🔄 Phase 5: Formatting output for session {session_id}")
        format_start = time.time()

        try:
            # Prepare data for formatting
            format_input = {
                "request_id": request_id,
                "email_id": email_id,
                "client_id": self.client_id,
                "material_specs": final_corrected_data,
                "processing_metadata": {
                    "extraction": formatted_extraction_data.get("extraction_summary", {}),
                    "validation": validated_specs.get("validation_summary", {}),
                    "normalization": normalized_specs.get("normalization_summary", {}),
                    "processing_time": processing_time,
                },
            }

            # Create formatting prompt
            formatting_prompt = f"""
## NORMALIZED SPECIFICATIONS TO FORMAT:
{json.dumps(format_input, indent=2)}

Please format according to client preferences and output schema requirements.
"""

            # Call formatter agent
            formatter_response = await asyncio.wait_for(
                self.formatter.arun(message=formatting_prompt),
                timeout=API_TIMEOUT,
            )

            # Parse formatter response
            formatted_output = self._parse_json_response(formatter_response.content)
            format_time = time.time() - format_start

            logger.info(f"✅ Formatting completed in {format_time:.2f}s")
            logger.info(f"📋 FORMATTED OUTPUT:\n{json.dumps(formatted_output, indent=2)}")

            # Yield the formatted result
            yield RunResponse(content=formatted_output, run_id=session_id)
            return

        except Exception as e:
            format_time = time.time() - format_start
            logger.error(f"❌ Formatting failed after {format_time:.2f}s: {e}")
            logger.info("🔄 Returning unformatted result as fallback")

            # Fallback to unformatted result
            final_result = {
                "request_id": request_id,
                "email_id": email_id,
                "client_id": self.client_id,
                "material_specs": final_corrected_data,
                "processing_time": processing_time,
                "performance_metrics": {
                    "extraction_time": extraction_time,
                    "domain_split_time": split_time if "split_time" in locals() else 0,
                    "parallel_processing_time": parallel_time,
                    "merge_time": merge_time,
                    "format_time": format_time,
                    "total_time": processing_time,
                    "estimated_sequential_time": extraction_time + (parallel_time * 2),
                    "time_saved": (extraction_time + (parallel_time * 2)) - processing_time,
                },
                "workflow_trace": [
                    {
                        "step": "extraction",
                        "status": "completed",
                        "time": extraction_time,
                        "specs_count": len(formatted_extraction_data.get("material_specs", [])),
                    },
                    {
                        "step": "validation",
                        "status": "completed" if isinstance(validated_specs, dict) and "validation_summary" in validated_specs else "fallback",
                        "time": parallel_time,
                        "specs_count": len(validated_specs.get("material_specs", [])),
                    },
                    {
                        "step": "normalization",
                        "status": "completed"
                        if isinstance(normalized_specs, dict) and ("normalization_summary" in normalized_specs or normalized_specs != validated_specs)
                        else "fallback",
                        "time": parallel_time,
                        "specs_count": len(normalized_specs.get("material_specs", [])),
                    },
                    {
                        "step": "merge",
                        "status": "completed" if final_merged_specs else "failed",
                        "time": merge_time,
                        "specs_count": len(final_merged_specs),
                    },
                    {
                        "step": "format",
                        "status": "fallback",
                        "time": format_time,
                        "specs_count": len(final_corrected_data),
                    },
                ],
            }

        # Add validation summary if available
        if isinstance(validated_specs, dict) and "validation_summary" in validated_specs:
            final_result["validation_summary"] = validated_specs["validation_summary"]

        # Add processing summary metadata
        final_result["processing_summary"] = merged_result.get("processing_summary", {})

        # Log final data before yielding
        logger.info(f"🚀 WORKFLOW YIELDING: {len(final_corrected_data.get('material_specs', []))} specs")
        logger.info(f"🚀 FINAL CORRECTED DATA STRUCTURE: {list(final_corrected_data.keys())}")
        if final_corrected_data.get("material_specs"):
            first_spec = final_corrected_data["material_specs"][0] if final_corrected_data["material_specs"] else {}
            logger.info(f"🚀 SAMPLE SPEC STRUCTURE: {list(first_spec.keys()) if first_spec else 'EMPTY'}")

        yield RunResponse(content=final_result)

    async def _run_validation_with_fallback(self, validation_prompt: str, fallback_data: dict, session_id: str) -> dict[str, Any]:
        """Run validation with timeout and error handling"""
        try:
            validator_response = await asyncio.wait_for(
                self.validator.arun(message=validation_prompt),
                timeout=API_TIMEOUT,
            )
            logger.info(f"🧠 VALIDATION AGENT RAW OUTPUT (with thinking):\n{validator_response.content}")
            result = self._parse_validation_response(validator_response.content, fallback_data)
            logger.info(f"✅ VALIDATION AGENT OUTPUT (JSON):\n{json.dumps(result, indent=2)}")
            return result
        except TimeoutError:
            logger.error(f"⏰ Validation timed out for session {session_id}")
            return self._create_fallback_validation_response(fallback_data)
        except Exception as e:
            logger.error(f"❌ Validation failed for session {session_id}: {e}")
            return self._create_fallback_validation_response(fallback_data)

    async def _run_normalization_with_fallback(self, normalization_prompt: str, fallback_data: dict, session_id: str) -> dict[str, Any]:
        """Run normalization with timeout and error handling"""
        try:
            normalizer_response = await asyncio.wait_for(
                self.normalizer.arun(message=normalization_prompt),
                timeout=API_TIMEOUT,
            )
            raw_content = normalizer_response.content
            logger.info(f"🧠 NORMALIZATION AGENT RAW OUTPUT (length: {len(raw_content)}):\n{raw_content}")

            # Check for potential truncation issues
            if len(raw_content) > 10000:
                logger.warning(f"⚠️ Normalizer response is very long ({len(raw_content)} chars) - may cause parsing issues")

            result = self._parse_json_response(raw_content)
            logger.info(f"🔢 NORMALIZATION AGENT OUTPUT (JSON):\n{json.dumps(result, indent=2)}")

            # Debug: Check the structure of the normalizer output
            if isinstance(result, dict) and "material_specs" in result:
                material_specs = result["material_specs"]
                logger.info(f"🔍 NORMALIZER DEBUG: Found {len(material_specs)} material_specs")
                if material_specs and isinstance(material_specs, list):
                    sample_spec = material_specs[0]
                    logger.info(
                        f"🔍 NORMALIZER DEBUG: Sample spec keys: {list(sample_spec.keys()) if isinstance(sample_spec, dict) else 'Not a dict'}"
                    )
                    if isinstance(sample_spec, dict) and "normalized_numerical_fields" in sample_spec:
                        normalized_fields = sample_spec["normalized_numerical_fields"]
                        non_null_fields = {k: v for k, v in normalized_fields.items() if v is not None}
                        logger.info(f"🔍 NORMALIZER DEBUG: Found normalized_numerical_fields with {len(non_null_fields)} non-null values")
                        logger.info(f"🔍 NORMALIZER DEBUG: Non-null fields: {list(non_null_fields.keys())}")
                    else:
                        logger.warning("🔍 NORMALIZER DEBUG: Missing normalized_numerical_fields in sample spec")
            else:
                logger.warning(f"🔍 NORMALIZER DEBUG: Invalid result structure: {list(result.keys()) if isinstance(result, dict) else type(result)}")

            return result
        except TimeoutError:
            logger.error(f"⏰ Normalization timed out for session {session_id}")
            return fallback_data  # Use input data as fallback
        except json.JSONDecodeError as e:
            logger.error(f"❌ Normalization JSON parsing failed for session {session_id}: {e}")
            logger.warning("🔄 Creating fallback normalization response with empty numerical fields")

            # Create a proper fallback response that matches the expected schema
            fallback_response = self._create_fallback_normalization_response(fallback_data)
            return fallback_response

        except Exception as e:
            logger.error(f"❌ Normalization failed for session {session_id}: {e}")
            logger.warning("🔄 Creating fallback normalization response")

            # Create a proper fallback response that matches the expected schema
            fallback_response = self._create_fallback_normalization_response(fallback_data)
            return fallback_response

    def run(self, rfq_body: str, request_id: str | None = None, email_id: str | None = None) -> Iterator[RunResponse]:
        """
        Synchronous wrapper for backward compatibility.
        For better performance, use arun() instead.
        """
        import asyncio

        async def _async_wrapper():
            async for response in self.arun(rfq_body, request_id, email_id):
                return response

        # Run the async version
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # If already in an async context, create a new loop
                import concurrent.futures

                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, _async_wrapper())
                    result = future.result()
            else:
                result = loop.run_until_complete(_async_wrapper())
        except RuntimeError:
            # No event loop, create a new one
            result = asyncio.run(_async_wrapper())

        yield result

    async def arun_batch(self, rfq_data_list: list[dict]) -> AsyncIterator[RunResponse]:
        """
        Process multiple RFQs concurrently.

        Args:
            rfq_data_list: List of dictionaries with 'body', 'request_id', 'email_id'

        Yields:
            RunResponse: Batch processing results
        """
        batch_start_time = time.time()
        batch_id = str(uuid4())

        logger.info(f"Starting batch processing of {len(rfq_data_list)} RFQs, batch {batch_id}")

        try:
            # Create tasks for all RFQ processing
            tasks = []
            for i, rfq_data in enumerate(rfq_data_list):
                rfq_body = rfq_data.get("body", "")
                request_id = rfq_data.get("request_id", f"req_{batch_id}_{i}")
                email_id = rfq_data.get("email_id", f"email_{batch_id}_{i}")

                task = asyncio.create_task(self._process_single_rfq_async(rfq_body, request_id, email_id), name=f"rfq-{i}")
                tasks.append(task)

            # Process all RFQs concurrently
            logger.info(f"Processing {len(tasks)} RFQs concurrently")
            done, pending = await asyncio.wait(tasks, return_when=asyncio.ALL_COMPLETED)

            # Collect results
            results = []
            for task in done:
                try:
                    result = await task
                    results.append(result)
                    logger.info(f"Task {task.get_name()} completed successfully")
                except Exception as e:
                    logger.error(f"Task {task.get_name()} failed: {e}")
                    results.append({"error": str(e), "task_name": task.get_name()})

            # Handle any pending tasks (shouldn't happen with ALL_COMPLETED)
            for task in pending:
                task.cancel()

            batch_end_time = time.time()
            batch_processing_time = batch_end_time - batch_start_time

            logger.info(f"Completed batch processing in {batch_processing_time:.2f} seconds")

            final_result = {
                "batch_id": batch_id,
                "results": results,
                "batch_processing_time": batch_processing_time,
                "total_rfqs": len(rfq_data_list),
                "successful_rfqs": len([r for r in results if "error" not in r]),
            }

            yield RunResponse(content=final_result)

        except Exception as e:
            logger.error(f"Batch processing failed for batch {batch_id}: {e}")
            raise

    async def _process_single_rfq_async(self, rfq_body: str, request_id: str, email_id: str) -> dict:
        """Process a single RFQ asynchronously for batch processing."""
        try:
            start_time = time.time()

            # Extract information with timeout
            extractor_response = await asyncio.wait_for(self.extractor.arun(message=rfq_body), timeout=API_TIMEOUT)

            # Parse response
            raw_content = extractor_response.content
            extracted_specs = self._parse_json_response(raw_content)

            # Generate spec_ids immediately after parsing
            extracted_specs = self._generate_spec_ids(extracted_specs, request_id[:8])  # Use request_id as session

            extracted_specs_final, extraction_metadata = self._process_extracted_specs(extracted_specs)

            # Format for validation and normalization
            formatted_extraction_data = self._format_extraction_for_validation(extracted_specs, request_id)

            # Add original text for cross-referencing
            formatted_extraction_data["original_text"] = rfq_body

            # PARALLEL PROCESSING: Run validation and normalization in parallel
            validation_prompt = f"""
## ORIGINAL EMAIL TEXT:
{rfq_body}

## EXTRACTED RFQ DATA TO VALIDATE:
{json.dumps(formatted_extraction_data, indent=2)}

Please validate and correct the extracted specifications against the original email text,
then validate against database catalogs. Return the results in the specified JSON format.
            """

            normalization_prompt = f"""
## ORIGINAL EMAIL TEXT:
{rfq_body}

## EXTRACTED DATA TO NORMALIZE:
{json.dumps(formatted_extraction_data, indent=2)}

Please normalize and standardize all numerical values, ensuring:
- All weights are in kg
- All dimensions are in mm
- All strength values are in N/mm²
- Min ≤ Max for all paired values
- Cross-reference with original email text
- Document all corrections in conflicts_resolved
            """

            # Create parallel tasks
            validation_task = asyncio.create_task(self._run_validation_with_fallback(validation_prompt, formatted_extraction_data, request_id))
            normalization_task = asyncio.create_task(
                self._run_normalization_with_fallback(normalization_prompt, formatted_extraction_data, request_id)
            )

            # Wait for both to complete
            try:
                gather_result = await asyncio.gather(validation_task, normalization_task, return_exceptions=True)
                validated_specs_raw, normalized_specs_raw = gather_result

                # Handle exceptions and ensure proper typing
                validated_specs: dict[str, Any]
                if isinstance(validated_specs_raw, Exception):
                    logger.warning(f"Validation failed for {request_id}: {validated_specs_raw}")
                    validated_specs = self._create_fallback_validation_response(formatted_extraction_data)
                else:
                    # Type cast needed because asyncio.gather with return_exceptions=True returns Any
                    validated_specs = cast(dict[str, Any], validated_specs_raw)

                normalized_specs: dict[str, Any]
                if isinstance(normalized_specs_raw, Exception):
                    logger.warning(f"Normalization failed for {request_id}: {normalized_specs_raw}")
                    normalized_specs = validated_specs if not isinstance(validated_specs_raw, Exception) else formatted_extraction_data
                else:
                    # Type cast needed because asyncio.gather with return_exceptions=True returns Any
                    normalized_specs = cast(dict[str, Any], normalized_specs_raw)

            except Exception as e:
                logger.warning(f"Parallel processing failed for {request_id}: {e}, using fallbacks")
                validated_specs = self._create_fallback_validation_response(formatted_extraction_data)
                normalized_specs = validated_specs

            # MODULAR MERGE THE OUTPUTS
            try:
                # Add original text to formatted extraction data
                formatted_extraction_data["original_text"] = rfq_body

                # Split the extraction output for the single RFQ - use formatted data
                categorical_data, numerical_data = split_extraction_output(formatted_extraction_data)

                # Merge using modular approach
                merged_result = merge_agent_outputs(
                    categorical_output=validated_specs, numerical_output=normalized_specs, extraction_summary=extraction_metadata
                )
                final_merged_specs = merged_result.get("material_specs", [])
            except Exception as merge_e:
                logger.warning(f"Modular merge failed for {request_id}: {merge_e}, using fallback")
                merged_result = {"material_specs": [], "processing_summary": {"pipeline_errors": [f"Merge failed: {str(merge_e)}"]}}
                final_merged_specs = []

            # Create final corrected data structure
            final_corrected_data = {
                "request_id": request_id,
                "email_id": email_id,
                "material_specs": final_merged_specs,
                "processing_summary": merged_result.get("processing_summary", {}),
            }

            processing_time = time.time() - start_time

            # PHASE 5: FORMAT OUTPUT ACCORDING TO CLIENT PREFERENCES
            logger.info(f"🔄 Phase 5: Formatting output for request {request_id}")
            format_start = time.time()

            try:
                # Prepare data for formatting
                format_input = {
                    "request_id": request_id,
                    "email_id": email_id,
                    "client_id": self.client_id,
                    "material_specs": final_merged_specs,
                    "processing_metadata": {
                        "extraction": extraction_metadata,
                        "validation": validated_specs.get("validation_summary", {}),
                        "normalization": normalized_specs.get("normalization_summary", {}),
                        "processing_time": processing_time,
                    },
                }

                # Create formatting prompt
                formatting_prompt = f"""
## NORMALIZED SPECIFICATIONS TO FORMAT:
{json.dumps(format_input, indent=2)}

Please format according to client preferences and output schema requirements.
"""

                # Call formatter agent
                formatter_response = await asyncio.wait_for(
                    self.formatter.arun(message=formatting_prompt),
                    timeout=API_TIMEOUT,
                )

                # Parse formatter response
                formatted_output = self._parse_json_response(formatter_response.content)
                format_time = time.time() - format_start

                logger.info(f"✅ Formatting completed in {format_time:.2f}s")
                logger.info(f"📋 FORMATTED OUTPUT:\n{json.dumps(formatted_output, indent=2)}")

                # Return the formatted result
                return formatted_output

            except Exception as e:
                format_time = time.time() - format_start
                logger.error(f"❌ Formatting failed after {format_time:.2f}s: {e}")
                logger.info("🔄 Returning unformatted result as fallback")

            # Fallback to unformatted result
            return {
                "request_id": request_id,
                "email_id": email_id,
                "extraction_result": formatted_extraction_data,
                "validation_result": validated_specs,
                "normalization_result": normalized_specs,
                "merged_result": merged_result,
                "final_specifications": final_corrected_data,
                "processing_time": processing_time,
                "extraction_metadata": extraction_metadata,
                "processing_summary": merged_result.get("processing_summary", {}),
            }

        except Exception as e:
            logger.error(f"Failed to process RFQ {request_id}: {e}")
            return {
                "request_id": request_id,
                "email_id": email_id,
                "error": str(e),
                "processing_time": time.time() - start_time if "start_time" in locals() else 0,
            }


def create_rfq_workflow(client_id: str | None = None, performance_mode: bool | None = None, debug_mode: bool | None = None) -> RFQProcessingWorkflow:
    """
    Create a new RFQ processing workflow.

    Args:
        client_id: Optional client ID for client-specific processing
        performance_mode: Enable performance optimizations. If None, reads from DEBUG_MODE env var
        debug_mode: Enable debug mode. If None, reads from PERFORMANCE_MODE env var

    Returns:
        RFQProcessingWorkflow: A configured workflow instance
    """
    # Read from environment variables if not explicitly provided
    if performance_mode is None:
        performance_mode = os.getenv("PERFORMANCE_MODE", "false").lower() in ("true", "1", "yes", "on")

    if debug_mode is None:
        debug_mode = os.getenv("DEBUG_MODE", "false").lower() in ("true", "1", "yes", "on")

    logger.info(f"Creating RFQ workflow: client_id={client_id}, performance_mode={performance_mode}, debug_mode={debug_mode}")

    return RFQProcessingWorkflow(client_id=client_id, performance_mode=performance_mode, debug_mode=debug_mode)
