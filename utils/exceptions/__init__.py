"""
Custom exceptions for Vanilla Steel AI application.

This module defines all custom exceptions used throughout the application
for better error handling and debugging.
"""

from typing import Any, Optional


class VanillaSteelError(Exception):
    """Base exception for all Vanilla Steel AI errors."""

    def __init__(
        self,
        message: str,
        error_code: str | None = None,
        details: dict[str, Any] | None = None,
    ) -> None:
        """
        Initialize the base exception.

        Args:
            message: Human-readable error message
            error_code: Machine-readable error code for API responses
            details: Additional error details for debugging
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code or self.__class__.__name__
        self.details = details or {}


# RFQ Processing Exceptions
class RFQProcessingError(VanillaSteelError):
    """Base exception for RFQ processing errors."""

    pass


class RFQExtractionError(RFQProcessingError):
    """Raised when RFQ extraction fails."""

    pass


class RFQValidationError(RFQProcessingError):
    """Raised when RFQ validation fails."""

    pass


# Client and Configuration Exceptions
class ClientError(VanillaSteelError):
    """Base exception for client-related errors."""

    pass


class ClientNotFoundError(ClientError):
    """Raised when a client is not found."""

    def __init__(self, client_id: str) -> None:
        super().__init__(
            message=f"Client with ID {client_id} not found",
            error_code="CLIENT_NOT_FOUND",
            details={"client_id": client_id},
        )


class ClientConfigurationError(ClientError):
    """Raised when client configuration is invalid or missing."""

    pass


# Database Exceptions
class DatabaseError(VanillaSteelError):
    """Base exception for database-related errors."""

    pass


class DatabaseConnectionError(DatabaseError):
    """Raised when database connection fails."""

    pass


class DatabaseQueryError(DatabaseError):
    """Raised when a database query fails."""

    pass


# API Exceptions
class APIError(VanillaSteelError):
    """Base exception for API-related errors."""

    def __init__(
        self,
        message: str,
        status_code: int = 500,
        error_code: str | None = None,
        details: dict[str, Any] | None = None,
    ) -> None:
        """
        Initialize API exception with HTTP status code.

        Args:
            message: Human-readable error message
            status_code: HTTP status code
            error_code: Machine-readable error code
            details: Additional error details
        """
        super().__init__(message, error_code, details)
        self.status_code = status_code


class AuthenticationError(APIError):
    """Raised when authentication fails."""

    def __init__(self, message: str = "Authentication failed") -> None:
        super().__init__(
            message=message,
            status_code=401,
            error_code="AUTHENTICATION_FAILED",
        )


class AuthorizationError(APIError):
    """Raised when authorization fails."""

    def __init__(self, message: str = "Insufficient permissions") -> None:
        super().__init__(
            message=message,
            status_code=403,
            error_code="AUTHORIZATION_FAILED",
        )


class RateLimitError(APIError):
    """Raised when rate limit is exceeded."""

    def __init__(
        self,
        message: str = "Rate limit exceeded",
        retry_after: int | None = None,
    ) -> None:
        details = {"retry_after": retry_after} if retry_after else {}
        super().__init__(
            message=message,
            status_code=429,
            error_code="RATE_LIMIT_EXCEEDED",
            details=details,
        )


class ValidationError(APIError):
    """Raised when request validation fails."""

    def __init__(
        self,
        message: str,
        field_errors: dict[str, Any] | None = None,
    ) -> None:
        super().__init__(
            message=message,
            status_code=422,
            error_code="VALIDATION_ERROR",
            details={"field_errors": field_errors} if field_errors else {},
        )


# External Service Exceptions
class ExternalServiceError(VanillaSteelError):
    """Base exception for external service errors."""

    pass


class AgnoServiceError(ExternalServiceError):
    """Raised when Agno service fails."""

    pass


class OpenRouterError(ExternalServiceError):
    """Raised when OpenRouter API fails."""

    pass


class UnkeyError(ExternalServiceError):
    """Raised when Unkey API fails."""

    pass
