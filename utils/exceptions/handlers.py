"""
Error handlers for FastAPI application.

This module provides centralized error handling for the API,
converting exceptions to appropriate HTTP responses.
"""

import time
import traceback
from typing import Any
from uuid import uuid4

from fastapi import Request, status
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from sqlalchemy.exc import DatabaseError as SQLAlchemyDatabaseError
from starlette.exceptions import HTTPException as StarletteHTTPException

from utils.exceptions import (
    APIError,
    VanillaSteelError,
)
from utils.log import get_app_logger, log_error_with_context

# Create application logger
logger = get_app_logger()


def create_error_response(
    error_id: str,
    error_code: str,
    message: str,
    status_code: int,
    details: dict[str, Any] | None = None,
    path: str | None = None,
) -> JSONResponse:
    """
    Create a standardized error response.

    Args:
        error_id: Unique error identifier for tracking
        error_code: Machine-readable error code
        message: Human-readable error message
        status_code: HTTP status code
        details: Additional error details
        path: Request path that caused the error

    Returns:
        JSONResponse with standardized error format
    """
    response_body = {
        "error": {
            "id": error_id,
            "code": error_code,
            "message": message,
            "details": details or {},
        },
        "path": path,
        "timestamp": time.time(),
    }

    return JSONResponse(
        status_code=status_code,
        content=response_body,
        headers={
            "X-Error-Id": error_id,
            "X-Error-Code": error_code,
        },
    )


async def vanilla_steel_error_handler(request: Request, exc: VanillaSteelError) -> JSONResponse:
    """
    Handle custom VanillaSteelError exceptions.

    Args:
        request: FastAPI request object
        exc: VanillaSteelError exception

    Returns:
        JSONResponse with error details
    """
    error_id = str(uuid4())

    # Log the error with context
    log_error_with_context(
        exc,
        context={
            "error_id": error_id,
            "path": request.url.path,
            "method": request.method,
            "error_code": exc.error_code,
        },
    )

    # Determine status code
    status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    if isinstance(exc, APIError):
        status_code = exc.status_code

    return create_error_response(
        error_id=error_id,
        error_code=exc.error_code,
        message=exc.message,
        status_code=status_code,
        details=exc.details,
        path=request.url.path,
    )


async def validation_error_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
    """
    Handle FastAPI request validation errors.

    Args:
        request: FastAPI request object
        exc: RequestValidationError exception

    Returns:
        JSONResponse with validation error details
    """
    error_id = str(uuid4())

    # Extract field errors
    field_errors = {}
    for error in exc.errors():
        field_path = ".".join(str(loc) for loc in error["loc"])
        field_errors[field_path] = {
            "message": error["msg"],
            "type": error["type"],
        }

    # Log the validation error
    logger.warning(
        f"Validation error on {request.url.path}: {field_errors}",
        extra={"error_id": error_id},
    )

    return create_error_response(
        error_id=error_id,
        error_code="VALIDATION_ERROR",
        message="Request validation failed",
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        details={"field_errors": field_errors},
        path=request.url.path,
    )


async def http_exception_handler(request: Request, exc: StarletteHTTPException) -> JSONResponse:
    """
    Handle Starlette HTTP exceptions.

    Args:
        request: FastAPI request object
        exc: StarletteHTTPException

    Returns:
        JSONResponse with error details
    """
    error_id = str(uuid4())

    # Log the HTTP exception
    logger.warning(
        f"HTTP {exc.status_code} on {request.url.path}: {exc.detail}",
        extra={"error_id": error_id},
    )

    return create_error_response(
        error_id=error_id,
        error_code=f"HTTP_{exc.status_code}",
        message=str(exc.detail),
        status_code=exc.status_code,
        details={},
        path=request.url.path,
    )


async def database_error_handler(request: Request, exc: SQLAlchemyDatabaseError) -> JSONResponse:
    """
    Handle SQLAlchemy database errors.

    Args:
        request: FastAPI request object
        exc: SQLAlchemyDatabaseError

    Returns:
        JSONResponse with error details
    """
    error_id = str(uuid4())

    # Log the database error (don't expose internal details)
    log_error_with_context(
        exc,
        context={
            "error_id": error_id,
            "path": request.url.path,
            "method": request.method,
        },
    )

    return create_error_response(
        error_id=error_id,
        error_code="DATABASE_ERROR",
        message="A database error occurred",
        status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
        details={"temporary": True},
        path=request.url.path,
    )


async def generic_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """
    Handle unexpected exceptions.

    Args:
        request: FastAPI request object
        exc: Generic exception

    Returns:
        JSONResponse with error details
    """
    error_id = str(uuid4())

    # Log the full exception with traceback
    logger.error(
        f"Unexpected error on {request.url.path}",
        exc_info=True,
        extra={
            "error_id": error_id,
            "path": request.url.path,
            "method": request.method,
            "exception_type": type(exc).__name__,
        },
    )

    # In production, don't expose internal error details
    runtime_env = request.app.state.settings.runtime_env
    if runtime_env == "prd":
        message = "An unexpected error occurred"
        details = {}
    else:
        message = str(exc)
        details = {
            "exception_type": type(exc).__name__,
            "traceback": traceback.format_exc().split("\n"),
        }

    return create_error_response(
        error_id=error_id,
        error_code="INTERNAL_ERROR",
        message=message,
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        details=details,
        path=request.url.path,
    )


def register_error_handlers(app) -> None:
    """
    Register all error handlers with the FastAPI app.

    Args:
        app: FastAPI application instance
    """
    # Custom exception handlers
    app.add_exception_handler(VanillaSteelError, vanilla_steel_error_handler)

    # FastAPI/Starlette exception handlers
    app.add_exception_handler(RequestValidationError, validation_error_handler)
    app.add_exception_handler(StarletteHTTPException, http_exception_handler)

    # SQLAlchemy exception handlers
    app.add_exception_handler(SQLAlchemyDatabaseError, database_error_handler)

    # Generic exception handler (catch-all)
    app.add_exception_handler(Exception, generic_exception_handler)

    logger.info("Error handlers registered successfully")
