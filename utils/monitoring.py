"""
Monitoring and observability utilities for Vanilla Steel AI.

This module provides request tracking, performance monitoring,
and health check capabilities for the application.
"""

import time
from collections.abc import Callable
from datetime import datetime
from typing import Any
from uuid import uuid4

from fastapi import Request, Response
from fastapi.routing import APIRoute
from sqlalchemy import text
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

from utils.log import get_app_logger, log_api_request, log_api_response

# Create application logger
logger = get_app_logger()


class TimedRoute(APIRoute):
    """
    Custom APIRoute that tracks request processing time.

    This route handler automatically measures and logs the time
    taken to process each request.
    """

    def get_route_handler(self) -> Callable:
        """
        Get route handler with timing functionality.

        Returns:
            Callable: Route handler with timing
        """
        original_route_handler = super().get_route_handler()

        async def custom_route_handler(request: Request) -> Response:
            """
            Handle route with timing and request ID injection.

            Args:
                request: FastAPI request object

            Returns:
                Response: FastAPI response object
            """
            # Generate request ID
            request_id = request.headers.get("X-Request-ID", str(uuid4()))
            request.state.request_id = request_id

            # Start timing
            start_time = time.time()

            # Process request
            response: Response = await original_route_handler(request)

            # Calculate duration
            duration_ms = (time.time() - start_time) * 1000

            # Add headers
            response.headers["X-Request-ID"] = request_id
            response.headers["X-Response-Time"] = f"{duration_ms:.2f}ms"

            # Log response
            log_api_response(
                method=request.method,
                path=request.url.path,
                status_code=response.status_code,
                duration_ms=duration_ms,
            )

            return response

        return custom_route_handler


class MonitoringMiddleware(BaseHTTPMiddleware):
    """
    Middleware for monitoring HTTP requests and responses.

    This middleware tracks:
    - Request/response times
    - Request rates
    - Error rates
    - Slow requests
    """

    def __init__(
        self,
        app: ASGIApp,
        slow_request_threshold_ms: float = 1000.0,
    ) -> None:
        """
        Initialize monitoring middleware.

        Args:
            app: ASGI application
            slow_request_threshold_ms: Threshold for slow request warnings
        """
        super().__init__(app)
        self.slow_request_threshold_ms = slow_request_threshold_ms
        self.request_count = 0
        self.error_count = 0
        self.total_response_time = 0.0

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Process and monitor HTTP request.

        Args:
            request: FastAPI request object
            call_next: Next middleware in chain

        Returns:
            Response: FastAPI response object
        """
        # Generate or get request ID
        request_id = request.headers.get("X-Request-ID", str(uuid4()))
        request.state.request_id = request_id

        # Get client IP
        client_ip = request.client.host if request.client else "unknown"

        # Log incoming request
        log_api_request(
            method=request.method,
            path=request.url.path,
            client_ip=client_ip,
        )

        # Start timing
        start_time = time.time()

        # Process request
        try:
            response = await call_next(request)
        except Exception:
            # Log exception and re-raise
            self.error_count += 1
            logger.error(
                f"Unhandled exception in request {request_id}",
                exc_info=True,
                extra={
                    "request_id": request_id,
                    "method": request.method,
                    "path": request.url.path,
                },
            )
            raise

        # Calculate duration
        duration_ms = (time.time() - start_time) * 1000

        # Update metrics
        self.request_count += 1
        self.total_response_time += duration_ms

        if response.status_code >= 400:
            self.error_count += 1

        # Warn about slow requests
        if duration_ms > self.slow_request_threshold_ms:
            logger.warning(
                f"Slow request detected: {request.method} {request.url.path} took {duration_ms:.2f}ms",
                extra={
                    "request_id": request_id,
                    "duration_ms": duration_ms,
                    "threshold_ms": self.slow_request_threshold_ms,
                },
            )

        # Add response headers
        response.headers["X-Request-ID"] = request_id
        response.headers["X-Response-Time"] = f"{duration_ms:.2f}ms"

        return response


class HealthCheck:
    """
    Health check service for monitoring application health.

    This service provides endpoints for:
    - Basic health status
    - Detailed health metrics
    - Readiness checks
    - Liveness probes
    """

    def __init__(self, app: ASGIApp) -> None:
        """
        Initialize health check service.

        Args:
            app: FastAPI application instance
        """
        self.app = app
        self.health_checks: dict[str, Callable] = {}

    def register_check(self, name: str, check_func: Callable) -> None:
        """
        Register a health check function.

        Args:
            name: Name of the health check
            check_func: Async function that returns (is_healthy, details)
        """
        self.health_checks[name] = check_func

    async def check_database(self) -> tuple[bool, dict[str, Any]]:
        """
        Check database health.

        Returns:
            Tuple of (is_healthy, details)
        """
        from db.session import db_engine

        try:
            start_time = time.time()
            with db_engine.connect() as conn:
                conn.execute(text("SELECT 1"))
                response_time = (time.time() - start_time) * 1000

            return True, {
                "status": "healthy",
                "response_time_ms": round(response_time, 2),
            }
        except Exception as e:
            return False, {
                "status": "unhealthy",
                "error": str(e),
            }

    async def check_external_services(self) -> tuple[bool, dict[str, Any]]:
        """
        Check external service health.

        Returns:
            Tuple of (is_healthy, details)
        """
        services = {}
        all_healthy = True

        # Check Agno
        try:
            # Agno health is determined by successful agent creation
            services["agno"] = {
                "status": "healthy",
                "workspace_id": self.app.state.settings.AGNO_WORKSPACE_ID[:8] + "...",
            }
        except Exception as e:
            all_healthy = False
            services["agno"] = {
                "status": "unhealthy",
                "error": str(e),
            }

        return all_healthy, services

    async def get_health_status(self) -> dict[str, Any]:
        """
        Get comprehensive health status.

        Returns:
            Dict containing health status of all components
        """
        health_status = {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "uptime_seconds": time.time() - self.app.state.start_time,
            "version": self.app.state.settings.version,
            "environment": self.app.state.settings.runtime_env.value,
            "checks": {},
        }

        # Run all health checks
        for name, check_func in self.health_checks.items():
            try:
                is_healthy, details = await check_func()
                health_status["checks"][name] = details
                if not is_healthy:
                    health_status["status"] = "unhealthy"
            except Exception as e:
                health_status["checks"][name] = {
                    "status": "error",
                    "error": str(e),
                }
                health_status["status"] = "unhealthy"

        # Built-in checks
        db_healthy, db_details = await self.check_database()
        health_status["checks"]["database"] = db_details
        if not db_healthy:
            health_status["status"] = "unhealthy"

        services_healthy, services_details = await self.check_external_services()
        health_status["checks"]["external_services"] = services_details
        if not services_healthy:
            health_status["status"] = "unhealthy"

        return health_status

    async def get_metrics(self) -> dict[str, Any]:
        """
        Get application metrics.

        Returns:
            Dict containing application metrics
        """
        # Basic health check without middleware access to avoid iteration issues
        try:
            # Access middleware through the app's middleware stack
            if hasattr(self.app, "user_middleware"):
                for middleware_cls, _args, _kwargs in self.app.user_middleware:
                    if middleware_cls == MonitoringMiddleware:
                        # We can't easily get the instance, so we'll track metrics differently
                        break
        except Exception:
            pass

        metrics = {
            "timestamp": datetime.utcnow().isoformat(),
            "uptime_seconds": time.time() - self.app.state.start_time,
            "status": "metrics_available",
            "note": "Basic metrics - detailed request metrics require middleware access",
        }

        # For now, return basic metrics to avoid middleware access issues
        # TODO: Implement proper metrics collection

        return metrics


def register_monitoring_middleware(app) -> None:
    """
    Register monitoring middleware with the FastAPI app.

    Args:
        app: FastAPI application instance
    """
    # Add monitoring middleware
    app.add_middleware(MonitoringMiddleware, slow_request_threshold_ms=1000.0)

    # Create health check service
    app.state.health_check = HealthCheck(app)

    logger.info("Monitoring middleware registered")
