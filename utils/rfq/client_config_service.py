"""
Client configuration service with JSON-based versioning support.

This service handles loading client configurations and resolving the appropriate
instruction schemas based on active versions stored in JSON format.
"""

from typing import Any
from uuid import UUID

from sqlalchemy.orm import Session

from db.tables.rfq_tables import ClientConfiguration, RFQInstructionSchema
from utils.rfq.config import get_default_client_id


class ClientConfigurationService:
    """Service for loading and managing client configurations with versioned schemas."""

    @staticmethod
    def get_client_configuration(db_session: Session, client_id: str | UUID, load_schemas: bool = True) -> dict[str, Any]:
        """
        Get client configuration with resolved schemas based on active versions.

        Args:
            db_session: Database session
            client_id: Client ID to load configuration for
            load_schemas: Whether to load and resolve the actual schema content

        Returns:
            Dictionary containing client configuration and resolved schemas
        """
        # Convert client_id to UUID if it's a string
        client_uuid = UUID(client_id) if isinstance(client_id, str) else client_id

        # Get the client configuration
        config = db_session.query(ClientConfiguration).filter_by(client_id=client_uuid).first()

        if not config:
            raise ValueError(f"No configuration found for client {client_id}")

        result = {
            "config_id": str(config.config_id),
            "client_id": str(config.client_id),
            "active_versions": config.active_versions,
            "model_preferences": config.model_preferences,
            "created_at": config.created_at.isoformat(),
            "updated_at": config.updated_at.isoformat(),
        }

        if load_schemas:
            result["schemas"] = ClientConfigurationService._load_schemas_by_version(db_session, client_uuid, config.active_versions)

        return result

    @staticmethod
    def _load_schemas_by_version(db_session: Session, client_id: UUID, active_versions: dict[str, Any]) -> dict[str, Any]:
        """
        Load instruction schemas based on active versions with client preference logic.

        Rules:
        1. All agent outputs (extraction, validation, normalizer) are loaded from generic account
        2. Formatter schemas are loaded based on client's has_output_preference flag:
           - If has_output_preference = True: load from specific client
           - If has_output_preference = False: load from generic account
        """
        from db.tables.rfq_tables import RFQClient

        default_client_id = UUID(get_default_client_id())
        schemas: dict[str, Any] = {}

        # Get client information to check preference flags
        client = db_session.query(RFQClient).filter_by(client_id=client_id).first()
        has_output_preference = client.has_output_preference if client else False
        has_instruction = client.has_instruction if client else False

        # Define the schema types and their mappings
        schema_mappings = {
            "extraction": {"output_schema": "extraction_schema", "rules": "extraction_rules"},
            "validation": {"output_schema": "validation_schema", "rules": "validation_rules"},
            "normalizer": {"output_schema": "normalizer_schema", "rules": "normalizer_rules"},
            "formatter": {"output_schema": "formatter_schema", "rules": "formatter_rules", "configuration": "formatter_configuration"},
        }

        # Load schemas for each component
        for component, component_versions in active_versions.items():
            if component not in schema_mappings:
                continue

            schemas[component] = {}

            for schema_type, version in component_versions.items():
                if schema_type not in schema_mappings[component]:
                    continue

                db_schema_type = schema_mappings[component][schema_type]

                # Determine which client to load from based on component, schema type, and client preferences
                if component == "formatter" and has_output_preference:
                    # Load formatter from specific client if has_output_preference = True
                    source_client_id = client_id
                elif schema_type == "rules" and has_instruction:
                    # Load instruction/rules from specific client if has_instruction = True
                    source_client_id = client_id
                else:
                    # Load from generic account:
                    # - All output schemas (always from generic)
                    # - Rules when has_instruction = False
                    # - Formatter when has_output_preference = False
                    source_client_id = default_client_id

                # Load schema content
                schema_content = ClientConfigurationService._load_schema_content(
                    db_session, source_client_id, default_client_id, db_schema_type, version
                )

                if schema_content:
                    schemas[component][schema_type] = {
                        "id": str(schema_content.id),
                        "name": schema_content.name,
                        "type": schema_content.type,
                        "version": schema_content.version,
                        "content": schema_content.content,
                        "description": schema_content.description,
                        "is_from_default_client": str(schema_content.client_id) == str(default_client_id),
                        "loaded_from_client": str(schema_content.client_id),
                        "requested_from_client": str(source_client_id),
                        "has_output_preference": has_output_preference if component == "formatter" else None,
                        "has_instruction": has_instruction if schema_type == "rules" else None,
                    }
                else:
                    schemas[component][schema_type] = None

        return schemas

    @staticmethod
    def _load_schema_content(
        db_session: Session, client_id: UUID, default_client_id: UUID, schema_type: str, version: str
    ) -> RFQInstructionSchema | None:
        """
        Load a specific schema content with fallback logic.

        1. Try to load from client-specific schemas
        2. Fall back to default client schemas
        3. If version not found, try to get the latest active version
        """

        # First try: client-specific schema with exact version
        if client_id != default_client_id:
            schema = db_session.query(RFQInstructionSchema).filter_by(client_id=client_id, type=schema_type, version=version, is_active=True).first()

            if schema:
                return schema

        # Second try: default client schema with exact version
        schema = (
            db_session.query(RFQInstructionSchema).filter_by(client_id=default_client_id, type=schema_type, version=version, is_active=True).first()
        )

        if schema:
            return schema

        # Third try: get latest active version from default client
        schema = (
            db_session.query(RFQInstructionSchema)
            .filter_by(client_id=default_client_id, type=schema_type, is_active=True)
            .order_by(RFQInstructionSchema.created_at.desc())
            .first()
        )

        return schema

    @staticmethod
    def update_active_version(db_session: Session, client_id: str | UUID, component: str, schema_type: str, new_version: str) -> bool:
        """
        Update the active version for a specific component and schema type.

        Args:
            db_session: Database session
            client_id: Client ID
            component: Component name (extraction, validation, normalizer, formatter)
            schema_type: Schema type (output_schema, rules, configuration)
            new_version: New version to set as active

        Returns:
            True if update was successful, False otherwise
        """
        # Convert client_id to UUID if it's a string
        client_uuid = UUID(client_id) if isinstance(client_id, str) else client_id

        # Get the client configuration
        config = db_session.query(ClientConfiguration).filter_by(client_id=client_uuid).first()

        if not config:
            return False

        # Update the active versions JSON
        active_versions = config.active_versions.copy()

        if component not in active_versions:
            active_versions[component] = {}

        active_versions[component][schema_type] = new_version

        # Save the updated configuration
        config.active_versions = active_versions
        db_session.commit()

        return True

    @staticmethod
    def get_available_versions(db_session: Session, client_id: str | UUID, component: str, schema_type: str) -> list[str]:
        """
        Get all available versions for a specific component and schema type.

        Returns versions available for both the client and the default client.
        """
        # Convert client_id to UUID if it's a string
        client_uuid = UUID(client_id) if isinstance(client_id, str) else client_id

        default_client_id = UUID(get_default_client_id())

        # Map component and schema_type to database schema type
        schema_mappings = {
            "extraction": {"output_schema": "extraction_schema", "rules": "extraction_rules"},
            "validation": {"output_schema": "validation_schema", "rules": "validation_rules"},
            "normalizer": {"output_schema": "normalizer_schema", "rules": "normalizer_rules"},
            "formatter": {"output_schema": "formatter_schema", "rules": "formatter_rules", "configuration": "formatter_configuration"},
        }

        if component not in schema_mappings or schema_type not in schema_mappings[component]:
            return []

        db_schema_type = schema_mappings[component][schema_type]

        # Query for available versions
        versions = (
            db_session.query(RFQInstructionSchema.version)
            .filter(
                RFQInstructionSchema.type == db_schema_type,
                RFQInstructionSchema.is_active,
                RFQInstructionSchema.client_id.in_([client_uuid, default_client_id]),
            )
            .distinct()
            .all()
        )

        return [v[0] for v in versions]
