"""
Steel Form Dimensional Classification Criteria.

This module contains the complete dimensional criteria for classifying steel forms
based on normalized dimensions. Used by the validation agent after normalizer processing.
"""

# Complete form dimensional criteria from business rules
FORM_DIMENSIONAL_CRITERIA = {
    "Coils": {
        "qualifying_criteria": ["material_width", "material_thickness", "material_outer_diameter", "material_length"],
        "dimensional_criteria": "material_outer_diameter IS_PRESENT AND material_width > 600",
    },
    "Slit Coils": {
        "qualifying_criteria": ["material_width", "material_thickness", "material_outer_diameter", "material_length"],
        "dimensional_criteria": "material_outer_diameter IS_PRESENT AND material_width <= 600",
    },
    "Sheets": {
        "qualifying_criteria": ["material_length", "material_width", "material_thickness"],
        "dimensional_criteria": "material_thickness < 3 AND material_width IS_PRESENT",
    },
    "Plates": {
        "qualifying_criteria": ["material_length", "material_width", "material_thickness"],
        "dimensional_criteria": "material_thickness >= 3 AND material_width IS_PRESENT AND material_thickness <= 50",
    },
    "Offcuts": {
        "qualifying_criteria": ["material_length", "material_width", "material_thickness"],
        "dimensional_criteria": "material_length IS_PRESENT AND material_width IS_PRESENT AND material_thickness IS_PRESENT",
    },
    "Assorted Sheets": {
        "qualifying_criteria": ["material_length", "material_width", "material_thickness"],
        "dimensional_criteria": "material_thickness < 3 AND material_width IS_PRESENT",
    },
    "Semi-assorted Sheets": {
        "qualifying_criteria": ["material_length", "material_width", "material_thickness"],
        "dimensional_criteria": "material_thickness < 3 AND material_width IS_PRESENT",
    },
    "Heavy Plates": {
        "qualifying_criteria": ["material_length", "material_width", "material_thickness"],
        "dimensional_criteria": "material_thickness > 50 AND material_width IS_PRESENT",
    },
    "Baby Coils": {
        "qualifying_criteria": ["material_width", "material_thickness", "material_outer_diameter", "material_length"],
        "dimensional_criteria": "material_outer_diameter IS_PRESENT AND material_outer_diameter < 500",
    },
    "Flat Bars": {
        "qualifying_criteria": ["material_length", "material_width", "material_thickness"],
        "dimensional_criteria": "material_width < material_thickness AND material_length > material_thickness "
        "AND material_width IS_PRESENT AND material_thickness IS_PRESENT",
    },
    "Hexagonal Bars": {
        "qualifying_criteria": ["material_length", "material_width", "material_thickness"],
        "dimensional_criteria": "APPROX_EQUAL(material_width, material_thickness) AND material_width > 20 "
        "AND material_width <= 200 AND material_length > material_width",
    },
    "Round Bars": {
        "qualifying_criteria": ["material_length", "material_width", "material_thickness"],
        "dimensional_criteria": "APPROX_EQUAL(material_width, material_thickness) AND material_width > 20 "
        "AND material_width <= 150 AND material_length > material_width",
    },
    "Square Bars": {
        "qualifying_criteria": ["material_length", "material_width", "material_thickness"],
        "dimensional_criteria": "APPROX_EQUAL(material_width, material_thickness) AND material_width > 20 "
        "AND material_width <= 150 AND material_length > material_width",
    },
    "Wire Rods": {
        "qualifying_criteria": ["material_length", "material_width", "material_thickness"],
        "dimensional_criteria": "APPROX_EQUAL(material_width, material_thickness) AND material_width <= 10 AND material_length > material_width",
    },
    "Welded Round Tubes": {
        "qualifying_criteria": ["material_outer_diameter", "material_wall_thickness", "material_length"],
        "dimensional_criteria": "material_outer_diameter IS_PRESENT AND material_wall_thickness IS_PRESENT AND material_outer_diameter <= 500",
    },
    "Seamless Round Tubes": {
        "qualifying_criteria": ["material_outer_diameter", "material_wall_thickness", "material_length"],
        "dimensional_criteria": "material_outer_diameter IS_PRESENT AND material_wall_thickness IS_PRESENT AND material_outer_diameter <= 500",
    },
    "Round Tubes": {
        "qualifying_criteria": ["material_outer_diameter", "material_wall_thickness", "material_length"],
        "dimensional_criteria": "material_outer_diameter IS_PRESENT AND material_wall_thickness IS_PRESENT AND material_outer_diameter <= 500",
    },
    "Square Tubes": {
        "qualifying_criteria": ["material_width", "material_wall_thickness", "material_length"],
        "dimensional_criteria": "material_width IS_PRESENT AND material_wall_thickness IS_PRESENT AND material_width <= 300",
    },
    "Rectangular Tubes": {
        "qualifying_criteria": ["material_width", "material_height", "material_wall_thickness", "material_length"],
        "dimensional_criteria": "material_width IS_PRESENT AND material_height IS_PRESENT "
        "AND material_wall_thickness IS_PRESENT AND material_width <= 400 AND material_height <= 400",
    },
    "Oval Tubes": {
        "qualifying_criteria": ["material_width", "material_height", "material_wall_thickness", "material_length"],
        "dimensional_criteria": "material_width IS_PRESENT AND material_height IS_PRESENT "
        "AND material_wall_thickness IS_PRESENT AND material_width <= 300 "
        "AND material_height <= 300 AND material_width > material_height",
    },
    "Octagonal Bars": {
        "qualifying_criteria": ["material_length", "material_width", "material_thickness"],
        "dimensional_criteria": "APPROX_EQUAL(material_width, material_thickness) AND material_width > 20 "
        "AND material_width <= 200 AND material_length > material_width",
    },
    "Profiles": {
        "qualifying_criteria": ["material_length", "material_width", "material_height", "material_thickness"],
        "dimensional_criteria": "material_length IS_PRESENT AND material_width IS_PRESENT "
        "AND material_height IS_PRESENT AND material_thickness IS_PRESENT",
    },
}

# Helper functions for dimensional criteria evaluation
DIMENSIONAL_EVALUATION_FUNCTIONS = {
    "IS_PRESENT": "Checks if the dimension field exists and has a non-null value",
    "APPROX_EQUAL": "Checks if two dimensions are approximately equal (within 5% tolerance)",
    "NOT_APPROX_EQUAL": "Checks if two dimensions are NOT approximately equal",
    "MAX": "Returns the maximum of two dimension values",
}

# Form classification priority rules
FORM_CLASSIFICATION_PRIORITY = [
    "Check catalog form validation first",
    "If catalog validation fails or has low confidence, apply dimensional classification",
    "Use dimensional criteria to suggest correct form classification",
    "Prefer more specific forms over generic ones (e.g., Heavy Plates over Plates)",
    "Consider multiple possible matches and rank by specificity",
]

# Confidence scoring for dimensional validation
DIMENSIONAL_CONFIDENCE_SCORING = {
    "exact_match": 1.0,
    "dimensional_match_catalog_mismatch": 0.8,
    "partial_criteria_match": 0.6,
    "no_criteria_match": 0.3,
}
