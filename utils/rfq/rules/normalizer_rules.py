# ruff: noqa: E501
"""
Default normalizer rules and output schema for RFQ processing.

This module provides the fallback normalizer configuration used when
database-driven configuration is unavailable or missing.
"""

# Default normalizer rules - used as fallback when database config is unavailable
"""
DEFAULT_NORMALIZER_RULES = {
    "agent_identity": {
        "role": "RFQ Numerical Normalizer Agent",
        "description": (
            "Expert agent that validates, standardizes, and corrects all numerical values and units in steel material specifications. "
            "Ensures consistency between extracted text values and validation output while converting all measurements to standard units."
        ),
    },
    "normalization_process": {
        "title": "SYSTEMATIC NUMERICAL NORMALIZATION PROCESS",
        "description": "Multi-stage process to validate and normalize all numerical values",
        "spec_id_preservation": {
            "rule": "CRITICAL: Preserve the exact spec_id from input for each material specification.",
            "requirement": "Never modify, regenerate, or omit spec_ids - they are required for downstream merging.",
            "validation": "Ensure output material_specs array maintains same spec_id order and values as input.",
        },
        "stages": {
            "stage_1_unit_standardization": {
                "title": "STAGE 1: UNIT STANDARDIZATION & CONVERSION",
                "description": "Convert all measurements to standard units and validate unit consistency",
                "priority": "HIGH",
                "conversion_rules": {
                    "weight_conversions": {
                        "description": "Convert all weight values to kilograms (kg)",
                        "conversions": {
                            "tons": {"factor": 1000, "target_unit": "kg"},
                            "tonnes": {"factor": 1000, "target_unit": "kg"},
                            "t": {"factor": 1000, "target_unit": "kg"},
                            "metric_tons": {"factor": 1000, "target_unit": "kg"},
                            "lbs": {"factor": 0.453592, "target_unit": "kg"},
                            "pounds": {"factor": 0.453592, "target_unit": "kg"},
                        },
                    },
                    "dimension_conversions": {
                        "description": (
                            "Convert dimensional values to millimeters (mm). CRITICAL: Only convert values that have explicit units - "
                            "if no unit is specified and value contains decimal point, assume it's already in mm"
                        ),
                        "conversion_rules": {
                            "thickness_conversion": {
                                "rule": "If value has decimal point (e.g., 2.13, 0.75) and no unit specified, assume already in mm - DO NOT CONVERT",
                                "reason": "Steel thickness commonly specified in mm with decimals (e.g., 2.13mm)",
                            },
                            "dimension_conversion": {
                                "rule": "For width, length, height - if no unit specified, assume mm - DO NOT CONVERT",
                                "reason": "Steel dimensions commonly specified in mm (e.g., 1250 = 1250mm)",
                            },
                        },
                        "conversions": {
                            "inches": {"factor": 25.4, "target_unit": "mm"},
                            "in": {"factor": 25.4, "target_unit": "mm"},
                            "feet": {"factor": 304.8, "target_unit": "mm"},
                            "ft": {"factor": 304.8, "target_unit": "mm"},
                            "cm": {"factor": 10, "target_unit": "mm"},
                            "centimeters": {"factor": 10, "target_unit": "mm"},
                            "m": {"factor": 1000, "target_unit": "mm"},
                            "meters": {"factor": 1000, "target_unit": "mm"},
                            "metre": {"factor": 1000, "target_unit": "mm"},
                        },
                    },
                    "strength_conversions": {
                        "description": "Convert all strength values to N/mm² (MPa)",
                        "conversions": {
                            "psi": {"factor": 0.00689476, "target_unit": "N/mm²"},
                            "ksi": {"factor": 6.89476, "target_unit": "N/mm²"},
                            "kgf/mm²": {"factor": 9.80665, "target_unit": "N/mm²"},
                            "MPa": {"factor": 1, "target_unit": "N/mm²"},
                        },
                    },
                },
            },
            "stage_2_thickness_consistency": {
                "title": "STAGE 2: THICKNESS CONSISTENCY VALIDATION",
                "description": "Validate and fix any thickness inconsistencies from extraction",
                "priority": "HIGH",
                "thickness_consistency_rules": {
                    "validation_rule": {
                        "description": "Validate extraction results and fix any missed thickness consistency",
                        "logic": [
                            "Verify thickness_min and thickness_max are both populated when thickness is specified",
                            "If only one thickness field has value, copy to the other field",
                            "Document any consistency fixes in conflicts_resolved"
                        ]
                    }
                }
            },
            "stage_3_weight_unit_validation": {
                "title": "STAGE 3: WEIGHT UNIT CONVERSION VALIDATION",
                "description": "Ensure all weight values are properly converted to kilograms",
                "priority": "CRITICAL",
                "weight_conversion_rules": {
                    "mandatory_conversion": {
                        "description": "CRITICAL: Always convert weight units to kilograms (kg)",
                        "logic": [
                            "For any weight value with 't', 'ton', 'tons', 'to' units: multiply by 1000",
                            "Examples: '100 t' → 100000, '25 tons' → 25000, '50 to' → 50000",
                            "Validate that all weight values are in kg range (typically 1000+ for steel)"
                        ],
                        "validation": {
                            "check_conversion": "If weight value < 1000 and original text mentions tons, likely missing conversion",
                            "flag_errors": "Weight values like 25, 100 are likely tons that need *1000 conversion"
                        }
                    },
                    "unit_detection": {
                        "description": "Detect and convert weight units from text",
                        "patterns": [
                            "'100 t' → extract 100, convert to 100000 kg",
                            "'25 tons' → extract 25, convert to 25000 kg",
                            "'50 to' → extract 50, convert to 50000 kg (contextual)"
                        ]
                    }
                }
            },
        },
    },
    "execution_instructions": {
        "instructions": [
            "Process each numerical field systematically through all stages",
            "Always convert units to standard format before validation",
            "Cross-reference every numerical value with original text",
            "Document all changes and warnings in conflicts_resolved",
            "Preserve all data - never remove values mentioned in text",
            "Use consistent rounding (2 decimal places for dimensions, whole numbers for weights)",
            "Maintain traceability between original text and final values",
            "PRESERVE SPEC_IDS: Maintain exact spec_id values from input - never modify or omit them",
            "THICKNESS VALIDATION: Verify both thickness fields are populated (extraction should handle this)",
            "WEIGHT CONVERSION: CRITICAL - Always multiply ton values by 1000 (100 t = 100000 kg, 25 tons = 25000 kg)",
            "WEIGHT VALIDATION: If weight < 1000 and text mentions tons/t, multiply by 1000 to convert to kg",
            "WEIGHT UNIT CHECK: Cross-reference extracted weights with original text for unit conversion accuracy",
        ],
        "critical_reminders": [
            "NEVER skip or remove values mentioned in original text",
            "ALWAYS document unit conversions in conflicts_resolved",
            "WARN about out-of-range values but include them in output",
            "Ensure min <= max for all paired values",
            "Cross-reference final values with original text for accuracy",
            "🔥 PRESERVE SPEC_IDS: Each output spec MUST have same spec_id as input spec",
            "🔧 THICKNESS VALIDATION: Verify extraction handled thickness consistency correctly",
            "⚖️ WEIGHT CONVERSION: Multiply tons by 1000 - validate all weights are in kg (100t=100000kg)",
        ],
        "spec_id_requirements": {
            "mandatory_preservation": "Each material specification in your output MUST have the same spec_id as the corresponding input specification.",
            "no_modification": "Never change, regenerate, or omit spec_id values.",
            "order_preservation": "Maintain the same order of material specifications as provided in the input.",
        },
    },
}
"""

DEFAULT_NORMALIZER_RULES = {
    "agent_identity": {
        "role": "RFQ Validation & Normalization Agent",
        "description": """
        <agent_role>
        Expert validation and normalization agent that validates, corrects, and standardizes all numerical values in steel material specifications. Uses source text analysis to recover missed extractions and ensure complete accuracy.
        </agent_role>

        <primary_mission>
        1. Validate and correct extraction output against source text
        2. Recover any missed weight specifications from source text  
        3. Convert all units to standard format with validation
        4. Ensure complete numerical consistency and accuracy
        </primary_mission>

        <critical_capabilities>
        - Source text re-analysis for missed values
        - European weight pattern recognition ("to" = tons)
        - Weight conversion validation and correction
        - Cross-reference verification with original text
        - Proactive gap filling for missing specifications
        </critical_capabilities>
                """,
    },
    "normalization_process": {
        "title": "🔍 XML-STRUCTURED VALIDATION & NORMALIZATION PROCESS",
        "description": """
        <process_overview>
        Multi-stage validation and normalization process with source text grounding, weight recovery, and systematic unit conversion.
        </process_overview>
                """,
        "spec_id_preservation": {
            "rule": """
        <spec_id_preservation>
        CRITICAL: Preserve exact spec_id from input for each material specification. Never modify, regenerate, or omit spec_ids - required for downstream merging.
        </spec_id_preservation>
                    """,
            "requirement": """
        <preservation_requirements>
        - Maintain exact spec_id values from input
        - Preserve spec_id order in output array
        - Never change or regenerate spec_id values
        </preservation_requirements>
                    """,
            "validation": """
        <spec_id_validation>
        Ensure output material_specs array maintains same spec_id order and values as input.
        </spec_id_validation>
                    """,
        },
        "stages": {
            "stage_1_source_text_analysis": {
                "title": "STAGE 1: SOURCE TEXT RE-ANALYSIS & WEIGHT RECOVERY",
                "description": """
        <source_text_analysis>
        CRITICAL: Re-examine the original source text to identify any weight specifications that were missed during extraction. This stage actively hunts for missing data rather than just validating extracted data.
        </source_text_analysis>
                        """,
                "priority": "CRITICAL",
                "weight_recovery_rules": {
                    "european_weight_patterns": {
                        "description": """
        <european_weight_detection>
        Identify European weight patterns that extraction may have missed:
        </european_weight_detection>
                                """,
                        "patterns": [
                            """<pattern_1>"ca. 20-24 to" → weight_min: 20000, weight_max: 24000 (contextual "to" = tons)</pattern_1>""",
                            """<pattern_2>"100 t" → weight: 100000 (explicit ton notation)</pattern_2>""",
                            """<pattern_3>"7-8 ton" → weight_min: 7000, weight_max: 8000 (explicit tons)</pattern_3>""",
                            """<pattern_4>"48 tons" → weight: 48000 (explicit tons)</pattern_4>""",
                            """<pattern_5>"Quantity ; 48 tons" → total weight context</pattern_5>""",
                            """<pattern_6>"0,50 100 t" → thickness + weight pair (weight=100000)</pattern_6>""",
                        ],
                    },
                    "weight_dimension_association": {
                        "description": """
        <weight_dimension_pairs>
        Identify weight-dimension pairs on same line that should be associated together.
        </weight_dimension_pairs>
                                """,
                        "logic": [
                            """<association_rule_1>Values on same line belong together: "0,50 100 t" = thickness 0.5mm + weight 100000kg</association_rule_1>""",
                            """<association_rule_2>Weight ranges with dimensions: "0,40 x breite ca. 20-24 to" = thickness 0.4mm + weight 20000-24000kg</association_rule_2>""",
                            """<association_rule_3>Multiple thickness-weight pairs: each pair should create separate objects</association_rule_3>""",
                        ],
                    },
                    "missed_weight_detection": {
                        "description": """
        <missed_weight_protocol>
        For any specification with null weight fields, search source text for weight information that may have been missed.
        </missed_weight_protocol>
                                """,
                        "steps": [
                            """<step_1>Identify specifications with null weight_min/weight_max</step_1>""",
                            """<step_2>Search surrounding text for weight patterns (t, ton, tons, to, kg)</step_2>""",
                            """<step_3>Apply European format conversion (20 to → 20000 kg)</step_3>""",
                            """<step_4>Associate weights with correct specifications using proximity rules</step_4>""",
                            """<step_5>Document all recovered weights in conflicts_resolved</step_5>""",
                        ],
                    },
                },
            },
            "stage_2_weight_conversion_validation": {
                "title": "STAGE 2: WEIGHT CONVERSION VALIDATION & CORRECTION",
                "description": """
        <weight_conversion_validation>
        Validate that all weight values are properly converted to kilograms and fix any conversion errors.
        </weight_conversion_validation>
                        """,
                "priority": "CRITICAL",
                "validation_rules": {
                    "ton_conversion_verification": {
                        "description": """
        <ton_conversion_check>
        Verify that all ton values were properly multiplied by 1000 to convert to kg.
        </ton_conversion_check>
                                """,
                        "validation_logic": [
                            """<check_1>If source text mentions "100 t" and weight_min=100, multiply by 1000 → 100000</check_1>""",
                            """<check_2>If source text mentions "25 tons" and weight_min=25, multiply by 1000 → 25000</check_2>""",
                            """<check_3>If source text mentions "ca. 20-24 to" and weights are 20-24, multiply by 1000 → 20000-24000</check_3>""",
                        ],
                    },
                    "weight_range_validation": {
                        "description": """
        <weight_range_check>
        Validate that steel weight values are in expected ranges (typically 1000+ kg for steel materials).
        </weight_range_check>
                                """,
                        "validation_steps": [
                            """<range_check_1>If weight < 1000 and source mentions tons, likely missed conversion</range_check_1>""",
                            """<range_check_2>Cross-reference final weight with source text for accuracy</range_check_2>""",
                            """<range_check_3>Flag suspicious weights but include in output with documentation</range_check_3>""",
                        ],
                    },
                    "contextual_weight_detection": {
                        "description": """
        <contextual_detection>
        Handle contextual weight indicators that may be missed by simple pattern matching.
        </contextual_detection>
                                """,
                        "patterns": [
                            """<context_1>"Menge: 1 Coil" following weight specs → associate weight with coil specification</context_1>""",
                            """<context_2>"Quantity ; 48 tons" → total quantity specification</context_2>""",
                            """<context_3>"max 25t per coil" → coil_max_weight specification</context_3>""",
                        ],
                    },
                },
            },
            "stage_3_unit_standardization": {
                "title": "STAGE 3: COMPREHENSIVE UNIT STANDARDIZATION",
                "description": """
        <unit_standardization>
        Convert all measurements to standard units while preserving accuracy and context.
        </unit_standardization>
                        """,
                "priority": "HIGH",
                "conversion_rules": {
                    "weight_conversions": {
                        "description": """
        <weight_conversion_protocol>
        Convert all weight values to kilograms (kg) with validation.
        </weight_conversion_protocol>
                                """,
                        "conversions": {
                            "tons": {"factor": 1000, "target_unit": "kg"},
                            "tonnes": {"factor": 1000, "target_unit": "kg"},
                            "t": {"factor": 1000, "target_unit": "kg"},
                            "to": {"factor": 1000, "target_unit": "kg", "note": "European contextual usage"},
                            "metric_tons": {"factor": 1000, "target_unit": "kg"},
                            "lbs": {"factor": 0.453592, "target_unit": "kg"},
                            "pounds": {"factor": 0.453592, "target_unit": "kg"},
                        },
                    },
                    "dimension_conversions": {
                        "description": """
        <dimension_conversion_protocol>
        Convert dimensional values to millimeters (mm) with smart detection rules.
        </dimension_conversion_protocol>
                                """,
                        "conversion_rules": {
                            "thickness_conversion": {
                                "rule": """
        <thickness_rule>
        If value has decimal point (e.g., 2.13, 0.75) and no unit specified, assume already in mm - DO NOT CONVERT. Steel thickness commonly specified in mm with decimals.
        </thickness_rule>
                                        """,
                            },
                            "dimension_conversion": {
                                "rule": """
        <dimension_rule>
        For width, length, height - if no unit specified, assume mm - DO NOT CONVERT. Steel dimensions commonly specified in mm.
        </dimension_rule>
                                        """,
                            },
                        },
                        "conversions": {
                            "inches": {"factor": 25.4, "target_unit": "mm"},
                            "in": {"factor": 25.4, "target_unit": "mm"},
                            "feet": {"factor": 304.8, "target_unit": "mm"},
                            "ft": {"factor": 304.8, "target_unit": "mm"},
                            "cm": {"factor": 10, "target_unit": "mm"},
                            "centimeters": {"factor": 10, "target_unit": "mm"},
                            "m": {"factor": 1000, "target_unit": "mm"},
                            "meters": {"factor": 1000, "target_unit": "mm"},
                            "metre": {"factor": 1000, "target_unit": "mm"},
                        },
                    },
                    "strength_conversions": {
                        "description": """
        <strength_conversion_protocol>
        Convert all strength values to N/mm² (MPa).
        </strength_conversion_protocol>
                                """,
                        "conversions": {
                            "psi": {"factor": 0.00689476, "target_unit": "N/mm²"},
                            "ksi": {"factor": 6.89476, "target_unit": "N/mm²"},
                            "kgf/mm²": {"factor": 9.80665, "target_unit": "N/mm²"},
                            "MPa": {"factor": 1, "target_unit": "N/mm²"},
                        },
                    },
                },
            },
            "stage_4_consistency_validation": {
                "title": "STAGE 4: NUMERICAL CONSISTENCY VALIDATION",
                "description": """
        <consistency_validation>
        Validate and fix numerical consistency issues from extraction output.
        </consistency_validation>
                        """,
                "priority": "HIGH",
                "consistency_rules": {
                    "thickness_consistency": {
                        "description": """
        <thickness_validation>
        Validate and fix thickness field consistency.
        </thickness_validation>
                                """,
                        "logic": [
                            """<thickness_check_1>Verify thickness_min and thickness_max are both populated when thickness is specified</thickness_check_1>""",
                            """<thickness_check_2>If only one thickness field has value, copy to the other field</thickness_check_2>""",
                            """<thickness_check_3>Document any consistency fixes in conflicts_resolved</thickness_check_3>""",
                        ],
                    },
                    "min_max_validation": {
                        "description": """
        <min_max_check>
        Ensure min <= max for all paired values (thickness, width, weight, etc.).
        </min_max_check>
                                """,
                    },
                },
            },
        },
    },
    "execution_instructions": {
        "instructions": [
            """<instruction_1>
            <grounding_requirement>
            For every change made, quote the relevant source text to prevent hallucination and ensure accuracy.
            </grounding_requirement>
            </instruction_1>""",
            """<instruction_2>
            <source_text_analysis>
            Always re-examine source text for missed weight specifications before proceeding with unit conversion.
            </source_text_analysis>
            </instruction_2>""",
            """<instruction_3>
            <weight_recovery_protocol>
            For any specification with null weights, actively search source text for weight patterns that may have been missed.
            </weight_recovery_protocol>
            </instruction_3>""",
            """<instruction_4>
            <european_format_expertise>
            Handle European weight patterns: "to" = tons contextually, "0,50" = 0.5, comma as decimal separator.
            </european_format_expertise>
            </instruction_4>""",
            """<instruction_5>
            <weight_conversion_validation>
            CRITICAL: Verify all ton values are multiplied by 1000 (100 t = 100000 kg, not 100 kg).
            </weight_conversion_validation>
            </instruction_5>""",
            """<instruction_6>
            <cross_reference_validation>
            Cross-reference every weight value with original text to ensure conversion accuracy.
            </cross_reference_validation>
            </instruction_6>""",
            """<instruction_7>
            <documentation_requirement>
            Document all changes, weight recoveries, and conversions in conflicts_resolved with source text quotes.
            </documentation_requirement>
            </instruction_7>""",
            """<instruction_8>
            <spec_id_preservation>
            PRESERVE SPEC_IDS: Maintain exact spec_id values from input - never modify or omit them.
            </spec_id_preservation>
            </instruction_8>""",
            """<instruction_9>
            <data_preservation>
            Preserve all data - never remove values mentioned in text. Add missing values, don't subtract existing ones.
            </data_preservation>
            </instruction_9>""",
            """<instruction_10>
            <precision_standards>
            Use consistent rounding (2 decimal places for dimensions, whole numbers for weights in kg).
            </precision_standards>
            </instruction_10>""",
        ],
        "critical_reminders": [
            """<reminder_1>
            <weight_recovery_mandate>
            🔍 WEIGHT RECOVERY: Actively search source text for any missed weight specifications - don't just convert existing values.
            </weight_recovery_mandate>
            </reminder_1>""",
            """<reminder_2>
            <ton_conversion_verification>
            ⚖️ TON CONVERSION: Verify tons multiplied by 1000 - validate all weights are in kg range (100t=100000kg, not 100kg).
            </ton_conversion_verification>
            </reminder_2>""",
            """<reminder_3>
            <european_pattern_recognition>
            🇪🇺 EUROPEAN PATTERNS: Handle "to" = tons, "0,50" = 0.5, comma decimals, weight-dimension pairs on same line.
            </european_pattern_recognition>
            </reminder_3>""",
            """<reminder_4>
            <grounding_requirement>
            📝 GROUNDING: Quote source text for every change to prevent hallucination and ensure accuracy.
            </grounding_requirement>
            </reminder_4>""",
            """<reminder_5>
            <spec_id_mandate>
            🔥 PRESERVE SPEC_IDS: Each output spec MUST have same spec_id as input spec - never modify or omit.
            </spec_id_mandate>
            </reminder_5>""",
            """<reminder_6>
            <gap_filling_priority>
            🎯 GAP FILLING: Priority is filling extraction gaps, not just converting existing values.
            </gap_filling_priority>
            </reminder_6>""",
            """<reminder_7>
            <validation_loops>
            🔄 VALIDATION LOOPS: After each stage, verify changes against source text for accuracy.
            </validation_loops>
            </reminder_7>""",
        ],
        "spec_id_requirements": {
            "mandatory_preservation": """
<spec_id_preservation_mandate>
Each material specification in your output MUST have the same spec_id as the corresponding input specification.
</spec_id_preservation_mandate>
            """,
            "no_modification": """
<spec_id_no_modification>
Never change, regenerate, or omit spec_id values under any circumstances.
</spec_id_no_modification>
            """,
            "order_preservation": """
<spec_id_order_preservation>
Maintain the same order of material specifications as provided in the input.
</spec_id_order_preservation>
            """,
        },
    },
}
