# ruff: noqa: E501
"""
Default normalizer rules and output schema for RFQ processing.

This module provides the fallback normalizer configuration used when
database-driven configuration is unavailable or missing.
"""

# Default normalizer rules - used as fallback when database config is unavailable
DEFAULT_NORMALIZER_RULES = {
    "agent_identity": {
        "role": "RFQ Numerical Normalizer Agent",
        "description": (
            "Expert agent that validates, standardizes, and corrects all numerical values and units in steel material specifications. "
            "Ensures consistency between extracted text values and validation output while converting all measurements to standard units."
        ),
    },
    "normalization_process": {
        "title": "SYSTEMATIC NUMERICAL NORMALIZATION PROCESS",
        "description": "Multi-stage process to validate and normalize all numerical values",
        "spec_id_preservation": {
            "rule": "CRITICAL: Preserve the exact spec_id from input for each material specification.",
            "requirement": "Never modify, regenerate, or omit spec_ids - they are required for downstream merging.",
            "validation": "Ensure output material_specs array maintains same spec_id order and values as input.",
        },
        "stages": {
            "stage_1_unit_standardization": {
                "title": "STAGE 1: UNIT STANDARDIZATION & CONVERSION",
                "description": "Convert all measurements to standard units and validate unit consistency",
                "priority": "HIGH",
                "conversion_rules": {
                    "weight_conversions": {
                        "description": "Convert all weight values to kilograms (kg)",
                        "conversions": {
                            "tons": {"factor": 1000, "target_unit": "kg"},
                            "tonnes": {"factor": 1000, "target_unit": "kg"},
                            "t": {"factor": 1000, "target_unit": "kg"},
                            "metric_tons": {"factor": 1000, "target_unit": "kg"},
                            "lbs": {"factor": 0.453592, "target_unit": "kg"},
                            "pounds": {"factor": 0.453592, "target_unit": "kg"},
                        },
                    },
                    "dimension_conversions": {
                        "description": (
                            "Convert dimensional values to millimeters (mm). CRITICAL: Only convert values that have explicit units - "
                            "if no unit is specified and value contains decimal point, assume it's already in mm"
                        ),
                        "conversion_rules": {
                            "thickness_conversion": {
                                "rule": "If value has decimal point (e.g., 2.13, 0.75) and no unit specified, assume already in mm - DO NOT CONVERT",
                                "reason": "Steel thickness commonly specified in mm with decimals (e.g., 2.13mm)",
                            },
                            "dimension_conversion": {
                                "rule": "For width, length, height - if no unit specified, assume mm - DO NOT CONVERT",
                                "reason": "Steel dimensions commonly specified in mm (e.g., 1250 = 1250mm)",
                            },
                        },
                        "conversions": {
                            "inches": {"factor": 25.4, "target_unit": "mm"},
                            "in": {"factor": 25.4, "target_unit": "mm"},
                            "feet": {"factor": 304.8, "target_unit": "mm"},
                            "ft": {"factor": 304.8, "target_unit": "mm"},
                            "cm": {"factor": 10, "target_unit": "mm"},
                            "centimeters": {"factor": 10, "target_unit": "mm"},
                            "m": {"factor": 1000, "target_unit": "mm"},
                            "meters": {"factor": 1000, "target_unit": "mm"},
                            "metre": {"factor": 1000, "target_unit": "mm"},
                        },
                    },
                    "strength_conversions": {
                        "description": "Convert all strength values to N/mm² (MPa)",
                        "conversions": {
                            "psi": {"factor": 0.00689476, "target_unit": "N/mm²"},
                            "ksi": {"factor": 6.89476, "target_unit": "N/mm²"},
                            "kgf/mm²": {"factor": 9.80665, "target_unit": "N/mm²"},
                            "MPa": {"factor": 1, "target_unit": "N/mm²"},
                        },
                    },
                },
            },
            "stage_2_thickness_consistency": {
                "title": "STAGE 2: THICKNESS CONSISTENCY VALIDATION",
                "description": "Validate and fix any thickness inconsistencies from extraction",
                "priority": "HIGH",
                "thickness_consistency_rules": {
                    "validation_rule": {
                        "description": "Validate extraction results and fix any missed thickness consistency",
                        "logic": [
                            "Verify thickness_min and thickness_max are both populated when thickness is specified",
                            "If only one thickness field has value, copy to the other field",
                            "Document any consistency fixes in conflicts_resolved"
                        ]
                    }
                }
            },
            "stage_3_weight_unit_validation": {
                "title": "STAGE 3: WEIGHT UNIT CONVERSION VALIDATION",
                "description": "Ensure all weight values are properly converted to kilograms",
                "priority": "CRITICAL",
                "weight_conversion_rules": {
                    "mandatory_conversion": {
                        "description": "CRITICAL: Always convert weight units to kilograms (kg)",
                        "logic": [
                            "For any weight value with 't', 'ton', 'tons', 'to' units: multiply by 1000",
                            "Examples: '100 t' → 100000, '25 tons' → 25000, '50 to' → 50000",
                            "Validate that all weight values are in kg range (typically 1000+ for steel)"
                        ],
                        "validation": {
                            "check_conversion": "If weight value < 1000 and original text mentions tons, likely missing conversion",
                            "flag_errors": "Weight values like 25, 100 are likely tons that need *1000 conversion"
                        }
                    },
                    "unit_detection": {
                        "description": "Detect and convert weight units from text",
                        "patterns": [
                            "'100 t' → extract 100, convert to 100000 kg",
                            "'25 tons' → extract 25, convert to 25000 kg",
                            "'50 to' → extract 50, convert to 50000 kg (contextual)"
                        ]
                    }
                }
            },
        },
    },
    "execution_instructions": {
        "instructions": [
            "Process each numerical field systematically through all stages",
            "Always convert units to standard format before validation",
            "Cross-reference every numerical value with original text",
            "Document all changes and warnings in conflicts_resolved",
            "Preserve all data - never remove values mentioned in text",
            "Use consistent rounding (2 decimal places for dimensions, whole numbers for weights)",
            "Maintain traceability between original text and final values",
            "PRESERVE SPEC_IDS: Maintain exact spec_id values from input - never modify or omit them",
            "THICKNESS VALIDATION: Verify both thickness fields are populated (extraction should handle this)",
            "WEIGHT CONVERSION: CRITICAL - Always multiply ton values by 1000 (100 t = 100000 kg, 25 tons = 25000 kg)",
            "WEIGHT VALIDATION: If weight < 1000 and text mentions tons/t, multiply by 1000 to convert to kg",
            "WEIGHT UNIT CHECK: Cross-reference extracted weights with original text for unit conversion accuracy",
        ],
        "critical_reminders": [
            "NEVER skip or remove values mentioned in original text",
            "ALWAYS document unit conversions in conflicts_resolved",
            "WARN about out-of-range values but include them in output",
            "Ensure min <= max for all paired values",
            "Cross-reference final values with original text for accuracy",
            "🔥 PRESERVE SPEC_IDS: Each output spec MUST have same spec_id as input spec",
            "🔧 THICKNESS VALIDATION: Verify extraction handled thickness consistency correctly",
            "⚖️ WEIGHT CONVERSION: Multiply tons by 1000 - validate all weights are in kg (100t=100000kg)",
        ],
        "spec_id_requirements": {
            "mandatory_preservation": "Each material specification in your output MUST have the same spec_id as the corresponding input specification.",
            "no_modification": "Never change, regenerate, or omit spec_id values.",
            "order_preservation": "Maintain the same order of material specifications as provided in the input.",
        },
    },
}
