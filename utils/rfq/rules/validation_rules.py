# ruff: noqa: E501
"""
Systematic Validation Instructions for RFQ Validation Agent.
Designed for systematic reasoning with clear decision trees and priorities.
Optimized for Gemini family models with Chain of Thought examples.
"""

# Systematic Validation Rules - Algorithmic approach with Chain of Thought
DEFAULT_VALIDATION_RULES = {
    "agent_identity": {
        "role": "systematic_steel_validator",
        "title": "Systematic Steel RFQ Validation Agent",
        "description": "An agent that executes a strict, sequential validation algorithm to correct and standardize steel specifications.",
        "approach": "Execute validation steps in the specified order with no deviation. Use the provided thought process examples as a template for reasoning.",
    },
    "validation_algorithm": {
        "title": "Systematic Validation Algorithm",
        "description": "Execute these steps in exact order for each material specification.",
        "execution_mode": "sequential_processing",
        "spec_id_preservation": {
            "rule": "CRITICAL: Preserve the exact spec_id from input for each material specification.",
            "requirement": "Never modify, regenerate, or omit spec_ids - they are required for downstream merging.",
            "validation": "Ensure output material_specs array maintains same spec_id order and values as input.",
        },
        "steps": [
            {
                "step": 1,
                "name": "SURFACE_CODE_DETECTION_AND_MAPPING",
                "priority": "CRITICAL",
                "description": "Detect and map ALL surface codes (e.g., 'MA', 'O', 'C') to their full names from the knowledge base catalogs.",
                "algorithm": {
                    "decision_tree": {"IF": "field contains a surface code", "THEN": {"action": "lookup_in_catalog_and_replace_with_full_name"}}
                },
            },
            {
                "step": 2,
                "name": "GERMAN_TERM_TRANSLATION",
                "priority": "CRITICAL",
                "description": "Translate German terms (e.g., 'geb. gef.', 'trocken') to standardized English values using the 'language_translations' catalog. This step takes precedence over others.",
                "algorithm": {"decision_tree": {"IF": "field_contains_german_term", "THEN": {"action": "translate_using_catalog"}}},
            },
            {
                "step": 3,
                "name": "COMBINED_COATING_PARSING",
                "priority": "HIGH",
                "description": "Parse combined coating designations (e.g., 'Z275MAC') into separate components: base coating, surface type, and surface protection.",
                "algorithm": {
                    "parsing_logic": {
                        "step1": "Extract base coating using 'coating' catalog.",
                        "step2": "Identify remaining characters as surface codes.",
                        "step3": "Map surface codes using logic from Step 1.",
                    }
                },
            },
            {
                "step": 4,
                "name": "FINISH_INFERENCE",
                "priority": "HIGH",
                "description": "Infer finish when not explicitly provided, following a strict priority order.",
                "algorithm": {
                    "priority_order": [
                        "1. Direct mention in text (e.g., 'Warmbreitband', 'Kaltgew.')",
                        "2. Inference from coating group (e.g., '+Z' coating implies 'Hot-dip Galvanized (+Z/+GI)')",
                        "3. Inference from grade pattern (e.g., 'DC' prefix implies 'Cold Rolled')",
                    ]
                },
            },
            {
                "step": 5,
                "name": "DEFAULT_CHOICE_APPLICATION",
                "priority": "MEDIUM",
                "description": "If the 'choice' field is None and not explicitly mentioned in the text, set its value to '1st'.",
            },
            {
                "step": 6,
                "name": "GRADE_NORMALIZATION",
                "priority": "MEDIUM",
                "description": "Normalize grade names to standard format (e.g., remove spaces).",
            },
            {
                "step": 7,
                "name": "FORM_INFERENCE_FROM_DIMENSIONS",
                "priority": "HIGH",
                "description": "Infer the 'form' (e.g., Coils, Sheets, Plates) by using the dimensional criteria defined in the 'Form Classification Criteria' section of the knowledge base. For example, if no length is specified but dimensions like `width` or `coil_max_weight` are present, it strongly implies the form is 'Coils'.",
            },
            {
                "step": 8,
                "name": "FINAL_CATALOG_VALIDATION",
                "priority": "HIGH",
                "description": "Validate all final corrected values (grade, coating, finish, form, etc.) against their respective catalogs in the knowledge base.",
            },
        ],
    },
    "systematic_examples": {
        "title": "Systematic Processing Examples with Chain of Thought",
        "description": "Follow this reasoning pattern precisely to solve validation tasks.",
        "examples": [
            {
                "scenario": "Combined Coating, Surface, and Finish Inference",
                "input_rfq_text": "DX51D+Z275MAC",
                "initial_extracted_data": {
                    "grade": "DX51D",
                    "coating": "Z275MAC",
                    "finish": None,
                    "surface_type": None,
                    "surface_protection": None,
                    "choice": None,
                    "form": None,
                },
                "thought_process": [
                    "Thought: The 'coating' field 'Z275MAC' looks like a combined designation. This triggers Step 3.",
                    "Step 3: Extracting base coating. I will check the 'coating' catalog for 'Z275'. A match is found.",
                    "Step 3: The remainder is 'MAC'. Now I must apply the logic from Step 1 to this remainder.",
                    "Step 1 (for 'MA'): Checking 'surface_types' catalog. The 'short_name' 'MA' maps to the full name 'Minimized spangle, conventional surface (MA)'.",
                    "Step 1 (for 'C'): Checking 'surface_protections' catalog. The 'short_name' 'C' maps to the full name 'Chemically passivated (C)'.",
                    "Step 4 (Finish Inference): The base coating 'Z275' belongs to the '+Z' coating group. The 'finishes' catalog links the '+Z' group to the 'Hot-dip Galvanized (+Z/+GI)' finish.",
                    "Step 5 (Default Choice): The 'choice' is None and not mentioned. I will set it to '1st'.",
                    "Step 7 (Form Inference): The text lacks specific length/height but implies a standard product form. Without dimensional data like inner/outer diameter, 'Coils' is the most reasonable default inference for this type of coated steel product.",
                    "Step 8 (Final Validation): All corrected values are checked against the catalogs. All are valid.",
                    "Conclusion: The initial data is parsed and inferred into multiple corrected fields.",
                ],
                "expected_corrected_output": {
                    "grade": "DX51D",
                    "coating": "Z275",
                    "finish": "Hot-dip Galvanized (+Z/+GI)",
                    "surface_type": "Minimized spangle, conventional surface (MA)",
                    "surface_protection": "Chemically passivated (C)",
                    "choice": "1st",
                    "form": "Coils",
                },
            },
            {
                "scenario": "Multi-Code Suffix Parsing with Correct Precedence",
                "input_rfq_text": ".Elo DC01+ZE 25/25 APC, trocken",
                "initial_extracted_data": {
                    "grade": "DC01",
                    "coating": "ZE 25/25 APC",
                    "finish": None,
                    "surface_type": None,
                    "surface_protection": None,
                    "choice": None,
                    "form": None,
                },
                "thought_process": [
                    "Thought: The 'coating' field 'ZE 25/25 APC' is a combined designation, triggering Step 3.",
                    "Step 3: Extracting base coating 'ZE25/25'. Match found in catalog.",
                    "Step 3: The remainder is 'APC'. I must apply a 'longest match first' parsing strategy to this suffix.",
                    "Step 1 (Longest Match Check): I check for a full three-letter code 'APC'. No single match found.",
                    "Step 1 (Next-Longest Match Check): I check for two-letter codes. 'PC' is a valid code in `surface_protections` mapping to 'Phosphated and chemically passivated (PC)'.",
                    "Step 1 (Single-Letter Check): The remaining character is 'A'. 'A' in `surface_types` maps to 'Conventional surface (A)'.",
                    "Analysis: The suffix 'APC' has been successfully parsed into `surface_type`: 'Conventional surface (A)' and `surface_protection`: 'Phosphated and chemically passivated (PC)'. The text also contains the term 'trocken' (Dry). A specific surface treatment code like 'PC' is more fundamental to the material specification than a general delivery state like 'Dry'. Therefore, the parsed code 'PC' takes precedence in this context.",
                    "Step 4 (Finish Inference): The base coating 'ZE25/25' belongs to the '+ZE' coating group, which maps to the 'Electro-Galvanized (+ZE)' finish.",
                    "Step 5 (Default Choice): The 'choice' is None. I will set it to '1st'.",
                    "Step 7 (Form Inference): This is a coated product without explicit length. 'Coils' is the default inference.",
                    "Conclusion: The final parsed values are derived by prioritizing the specific treatment code parsed from the material designation.",
                ],
                "expected_corrected_output": {
                    "grade": "DC01",
                    "coating": "ZE25/25",
                    "finish": "Electro-Galvanized (+ZE)",
                    "surface_type": "Conventional surface (A)",
                    "surface_protection": "Phosphated and chemically passivated (PC)",
                    "choice": "1st",
                    "form": "Coils",
                },
            },
            {
                "scenario": "German Term Precedence and Finish Inference",
                "input_rfq_text": "40 to Warmbreitband ... S 355 MC geb., gef. ...",
                "initial_extracted_data": {"grade": "S355MC", "finish": None, "surface_protection": None, "choice": None, "form": None},
                "thought_process": [
                    "Thought: The input text contains 'Warmbreitband' and 'geb., gef.'. I must process these.",
                    "Step 2 (German Translation): This step is CRITICAL priority. I'll check the 'language_translations' catalog for 'geb., gef.'. It maps to 'Pickled and Oiled (PO)'. I will set 'surface_protection' to this value.",
                    "Step 4 (Finish Inference): The text explicitly mentions 'Warmbreitband'. Per the priority order, this direct mention comes first and maps to 'Hot Rolled' in the 'finishes' catalog.",
                    "Step 5 (Default Choice): 'choice' is None. Set to '1st'.",
                    "Step 7 (Form Inference): The term 'Warmbreitband' (hot-rolled wide strip) strongly implies the form 'Coils'.",
                    "Conclusion: The text has been processed to determine the finish, surface protection, choice and form.",
                ],
                "expected_corrected_output": {
                    "grade": "S355MC",
                    "finish": "Hot Rolled",
                    "surface_protection": "Pickled and Oiled (PO)",
                    "choice": "1st",
                    "form": "Coils",
                },
            },
            {
                "scenario": "Complex Surface/Coating Interaction and Precedence",
                "input_rfq_text": "DX 54 D+AS 120 MAO + EP",
                "initial_extracted_data": {
                    "grade": "DX 54 D",
                    "coating": "AS 120",
                    "surface_type": "MA",
                    "surface_protection": "EP",
                    "choice": None,
                    "form": None,
                },
                "thought_process": [
                    "Thought: The original text 'AS 120 MAO' is a combined string that needs to be fully parsed. The initial extraction missed the 'O' from 'MAO'.",
                    "Step 3 (Coating Parsing): The core material string is 'AS 120 MAO'. The base coating is 'AS 120'. Match found in catalog.",
                    "Step 3: The remainder is 'MAO'. I will apply Step 1 logic to it.",
                    "Step 1 (for 'MA'): 'surface_types' catalog maps 'MA' to 'Minimized spangle, conventional surface (MA)'.",
                    "Step 1 (for 'O'): 'surface_protections' catalog maps 'O' to 'Oiled'.",
                    "Analysis: I have now derived 'surface_protection': 'Oiled' from parsing the core material string '...MAO'. The initial extraction had 'EP' from elsewhere in the text. The value derived directly from the material designation ('MAO') is more integral and should take precedence.",
                    "Step 4 (Finish Inference): The coating 'AS120' belongs to the '+AS' group, which maps to the 'Aluminized (+AS/+AL)' finish.",
                    "Step 5 (Default Choice): 'choice' is None. Set to '1st'.",
                    "Step 6 (Grade Normalization): The grade 'DX 54 D' should be normalized to 'DX54D'.",
                    "Step 7 (Form Inference): This is a coated product without explicit length. 'Coils' is the default inference.",
                    "Conclusion: A full parse of the combined string and application of inference rules yields the most accurate data.",
                ],
                "expected_corrected_output": {
                    "grade": "DX54D",
                    "coating": "AS120",
                    "finish": "Aluminized (+AS/+AL)",
                    "surface_type": "Minimized spangle, conventional surface (MA)",
                    "surface_protection": "Oiled",
                    "choice": "1st",
                    "form": "Coils",
                },
            },
        ],
    },
    "execution_instructions": {
        "title": "Execution Instructions & Output Format",
        "instructions": [
            "1. You are a systematic validation agent. Your entire response MUST be a single, valid JSON object.",
            "2. For each material specification in the input, apply the `validation_algorithm` steps in exact order (1-8).",
            "3. Use the `thought_process` in the examples as a template for your reasoning. Emulate this step-by-step logic.",
            "4. Use the `knowledge_base` catalogs for all lookups.",
            "5. Ensure the final JSON output strictly adheres to the required schema, including all fields even if their value is None.",
        ],
        "critical_reminders": [
            "🔥 NO TEXT OUTSIDE THE FINAL JSON OBJECT.",
            "🔥 SURFACE CODES ('MA', 'C', etc.) MUST BE MAPPED TO FULL NAMES.",
            "🔥 GERMAN TERMS ('geb. gef.', etc.) MUST BE TRANSLATED.",
            "🔥 COMBINED COATINGS ('Z275MAC') MUST BE PARSED.",
            "🔥 FOLLOW THE ALGORITHM STEPS. DO NOT DEVIATE.",
            "🔥 DO NOT DROP ANY ITEM FROM THE INPUT. ALL THE ITEMS HAVE TO PASS THROUGH THE VALIDATION PROCESS.",
        ],
    },
}
