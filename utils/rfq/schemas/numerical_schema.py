# ruff: noqa: E501
"""
Numerical Data Schema - For Normalizer Agent processing.

Contains only numerical fields (dimensions, weights, strengths, etc.)
that require unit standardization and numerical validation.
"""

from typing import Any

# ============================================================================
# NUMERICAL DATA SCHEMA (Input to Normalizer Agent)
# ============================================================================

NUMERICAL_DATA_SCHEMA: dict[str, Any] = {
    "request_id": None,
    "client_id": None,
    "original_text": None,  # Needed for cross-referencing values
    "material_specs": [
        {
            "spec_id": None,
            "numerical_fields": {
                "thickness_min": None,
                "thickness_max": None,
                "width_min": None,
                "width_max": None,
                "length_min": None,
                "length_max": None,
                "height_min": None,
                "height_max": None,
                "inner_diameter_min": None,
                "inner_diameter_max": None,
                "outer_diameter_min": None,
                "outer_diameter_max": None,
                "weight_min": None,
                "weight_max": None,
                "coil_max_weight": None,
                "yield_strength_min": None,
                "yield_strength_max": None,
                "tensile_strength_min": None,
                "tensile_strength_max": None,
            },
            "extraction_context": {
                "confidence_scores": {},  # field -> confidence mapping
                "source_references": {},  # field -> source text mapping
                "extraction_flags": [],
                "raw_extracted_values": {},  # field -> original extracted value (before parsing)
                "assumptions_made": [],
            },
        }
    ],
    "processing_metadata": {"source_agent": "extraction", "data_type": "numerical", "split_timestamp": None, "total_specs": 0},
}

# ============================================================================
# NORMALIZER AGENT OUTPUT SCHEMA
# ============================================================================

NORMALIZER_AGENT_OUTPUT_SCHEMA: dict[str, Any] = {
    "request_id": None,
    "client_id": None,
    "material_specs": [
        {
            "spec_id": None,  # Preserved from extraction
            "normalized_numerical_fields": {
                # Standardized numerical values (units: mm, kg, N/mm²)
                "thickness_min": None,
                "thickness_max": None,
                "width_min": None,
                "width_max": None,
                "length_min": None,
                "length_max": None,
                "height_min": None,
                "height_max": None,
                "inner_diameter_min": None,
                "inner_diameter_max": None,
                "outer_diameter_min": None,
                "outer_diameter_max": None,
                "weight_min": None,
                "weight_max": None,
                "coil_max_weight": None,
                "yield_strength_min": None,
                "yield_strength_max": None,
                "tensile_strength_min": None,
                "tensile_strength_max": None,
            },
            "normalization_metadata": {
                "unit_conversions": [],  # List of conversions performed
                "consistency_fixes": [],  # Min/max fixes, field corrections
                "range_warnings": [],  # Values outside typical ranges
                "tolerance_parsing": [],  # Tolerance calculations performed
                "field_mapping_corrections": [],  # Field reassignments
                "normalization_confidence": 0.0,
                "normalization_errors": [],
                "normalization_warnings": [],
            },
            # Preserved extraction context
            "extraction_context": {
                "confidence_scores": {},
                "source_references": {},
                "extraction_flags": [],
                "raw_extracted_values": {},
                "assumptions_made": [],
            },
        }
    ],
    "normalization_summary": {
        "units_standardized": 0,
        "consistency_fixes_made": 0,
        "processing_time_ms": 0,
        "warnings_issued": [],
        "normalization_agent_version": "2.0",
        "standard_units_used": {"weight": "kg", "dimensions": "mm", "strength": "N/mm²"},
    },
}

# ============================================================================
# NUMERICAL FIELD DEFINITIONS
# ============================================================================

NUMERICAL_FIELD_DEFINITIONS = {
    "thickness_min": {
        "unit": "mm",
        "typical_range": {"min": 0.1, "max": 25},
        "validation_range": {"min": 0.01, "max": 100},
        "precision": 2,
        "paired_field": "thickness_max",
    },
    "thickness_max": {
        "unit": "mm",
        "typical_range": {"min": 0.1, "max": 25},
        "validation_range": {"min": 0.01, "max": 100},
        "precision": 2,
        "paired_field": "thickness_min",
    },
    "width_min": {
        "unit": "mm",
        "typical_range": {"min": 600, "max": 2000},
        "validation_range": {"min": 50, "max": 3500},
        "precision": 0,
        "paired_field": "width_max",
    },
    "width_max": {
        "unit": "mm",
        "typical_range": {"min": 600, "max": 2000},
        "validation_range": {"min": 50, "max": 3500},
        "precision": 0,
        "paired_field": "width_min",
    },
    "length_min": {
        "unit": "mm",
        "typical_range": {"min": 1000, "max": 12000},
        "validation_range": {"min": 100, "max": 20000},
        "precision": 0,
        "paired_field": "length_max",
    },
    "length_max": {
        "unit": "mm",
        "typical_range": {"min": 1000, "max": 12000},
        "validation_range": {"min": 100, "max": 20000},
        "precision": 0,
        "paired_field": "length_min",
    },
    "height_min": {
        "unit": "mm",
        "typical_range": {"min": 10, "max": 500},
        "validation_range": {"min": 1, "max": 1000},
        "precision": 1,
        "paired_field": "height_max",
    },
    "height_max": {
        "unit": "mm",
        "typical_range": {"min": 10, "max": 500},
        "validation_range": {"min": 1, "max": 1000},
        "precision": 1,
        "paired_field": "height_min",
    },
    "inner_diameter_min": {
        "unit": "mm",
        "typical_range": {"min": 508, "max": 610},
        "validation_range": {"min": 100, "max": 1000},
        "precision": 0,
        "paired_field": "inner_diameter_max",
    },
    "inner_diameter_max": {
        "unit": "mm",
        "typical_range": {"min": 508, "max": 610},
        "validation_range": {"min": 100, "max": 1000},
        "precision": 0,
        "paired_field": "inner_diameter_min",
    },
    "outer_diameter_min": {
        "unit": "mm",
        "typical_range": {"min": 1000, "max": 2200},
        "validation_range": {"min": 500, "max": 3000},
        "precision": 0,
        "paired_field": "outer_diameter_max",
    },
    "outer_diameter_max": {
        "unit": "mm",
        "typical_range": {"min": 1000, "max": 2200},
        "validation_range": {"min": 500, "max": 3000},
        "precision": 0,
        "paired_field": "outer_diameter_min",
    },
    "weight_min": {
        "unit": "kg",
        "typical_range": {"min": 100, "max": 50000},
        "validation_range": {"min": 0.1, "max": 100000},
        "precision": 0,
        "paired_field": "weight_max",
    },
    "weight_max": {
        "unit": "kg",
        "typical_range": {"min": 100, "max": 50000},
        "validation_range": {"min": 0.1, "max": 100000},
        "precision": 0,
        "paired_field": "weight_min",
    },
    "coil_max_weight": {
        "unit": "kg",
        "typical_range": {"min": 1000, "max": 30000},
        "validation_range": {"min": 1, "max": 50000},
        "precision": 0,
        "paired_field": None,
    },
    "yield_strength_min": {
        "unit": "N/mm²",
        "typical_range": {"min": 200, "max": 800},
        "validation_range": {"min": 100, "max": 2000},
        "precision": 0,
        "paired_field": "yield_strength_max",
    },
    "yield_strength_max": {
        "unit": "N/mm²",
        "typical_range": {"min": 200, "max": 800},
        "validation_range": {"min": 100, "max": 2000},
        "precision": 0,
        "paired_field": "yield_strength_min",
    },
    "tensile_strength_min": {
        "unit": "N/mm²",
        "typical_range": {"min": 300, "max": 1200},
        "validation_range": {"min": 200, "max": 3000},
        "precision": 0,
        "paired_field": "tensile_strength_max",
    },
    "tensile_strength_max": {
        "unit": "N/mm²",
        "typical_range": {"min": 300, "max": 1200},
        "validation_range": {"min": 200, "max": 3000},
        "precision": 0,
        "paired_field": "tensile_strength_min",
    },
}

# ============================================================================
# UNIT CONVERSION DEFINITIONS
# ============================================================================

UNIT_CONVERSIONS = {
    "weight": {
        "tons": 1000,
        "tonnes": 1000,
        "t": 1000,
        "to": 1000,
        "metric_tons": 1000,
        "lbs": 0.453592,
        "pounds": 0.453592,
        "kg": 1,  # Already in target unit
    },
    "dimensions": {
        "inches": 25.4,
        "in": 25.4,
        "feet": 304.8,
        "ft": 304.8,
        "cm": 10,
        "centimeters": 10,
        "m": 1000,
        "meters": 1000,
        "metre": 1000,
        "mm": 1,  # Already in target unit
    },
    "strength": {
        "psi": 0.00689476,
        "ksi": 6.89476,
        "kgf/mm²": 9.80665,
        "MPa": 1,
        "N/mm²": 1,  # Already in target unit
    },
}

# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================


def create_empty_numerical_data(request_id: str | None = None, client_id: str | None = None) -> dict[str, Any]:
    """Create empty numerical data structure."""
    data = NUMERICAL_DATA_SCHEMA.copy()
    if request_id:
        data["request_id"] = request_id
    if client_id:
        data["client_id"] = client_id
    data["material_specs"] = []
    return data


def create_empty_normalizer_output(request_id: str | None = None, client_id: str | None = None) -> dict[str, Any]:
    """Create empty normalizer output structure."""
    output = NORMALIZER_AGENT_OUTPUT_SCHEMA.copy()
    if request_id:
        output["request_id"] = request_id
    if client_id:
        output["client_id"] = client_id
    output["material_specs"] = []
    return output


def add_numerical_spec(
    numerical_data: dict[str, Any], spec_id: str, numerical_fields: dict[str, Any], extraction_context: dict[str, Any] | None = None
) -> dict[str, Any]:
    """Add a numerical specification to the data structure."""
    spec = {"spec_id": spec_id, "numerical_fields": numerical_fields, "extraction_context": extraction_context or {}}
    numerical_data["material_specs"].append(spec)
    numerical_data["processing_metadata"]["total_specs"] = len(numerical_data["material_specs"])
    return numerical_data


def get_numerical_field_names() -> list[str]:
    """Get list of all numerical field names."""
    return list(NUMERICAL_FIELD_DEFINITIONS.keys())


def get_field_definition(field_name: str) -> dict[str, Any]:
    """Get definition for a specific numerical field."""
    return NUMERICAL_FIELD_DEFINITIONS.get(field_name, {})


def get_unit_conversion_factor(unit: str, field_category: str) -> float:
    """Get conversion factor for a unit to standard unit."""
    conversions = UNIT_CONVERSIONS.get(field_category, {})
    return conversions.get(unit.lower(), 1.0)


def is_value_in_typical_range(field_name: str, value: float) -> bool:
    """Check if a value is within typical range for the field."""
    definition = get_field_definition(field_name)
    typical_range = definition.get("typical_range", {})
    if not typical_range:
        return True
    return typical_range.get("min", 0) <= value <= typical_range.get("max", float("inf"))


def is_value_in_validation_range(field_name: str, value: float) -> bool:
    """Check if a value is within validation range for the field."""
    definition = get_field_definition(field_name)
    validation_range = definition.get("validation_range", {})
    if not validation_range:
        return True
    return validation_range.get("min", 0) <= value <= validation_range.get("max", float("inf"))


def get_paired_field(field_name: str) -> str | None:
    """Get the paired field name (e.g., thickness_min -> thickness_max)."""
    definition = get_field_definition(field_name)
    return definition.get("paired_field")


def validate_numerical_data_structure(data: dict[str, Any]) -> list[str]:
    """Validate numerical data structure and return list of errors."""
    errors = []

    # Check required top-level fields
    required_fields = ["request_id", "material_specs", "processing_metadata"]
    for field in required_fields:
        if field not in data:
            errors.append(f"Missing required field: {field}")

    # Check material specs structure
    specs = data.get("material_specs", [])
    if not isinstance(specs, list):
        errors.append("material_specs must be a list")
    else:
        for i, spec in enumerate(specs):
            if "spec_id" not in spec:
                errors.append(f"Material spec {i} missing 'spec_id' field")
            if "numerical_fields" not in spec:
                errors.append(f"Material spec {i} missing 'numerical_fields' field")
            if "extraction_context" not in spec:
                errors.append(f"Material spec {i} missing 'extraction_context' field")

    return errors


def extract_numerical_fields_from_specification(specification: dict[str, Any]) -> dict[str, Any]:
    """Extract only numerical fields from a complete specification."""
    numerical_fields = {}
    for field_name in get_numerical_field_names():
        numerical_fields[field_name] = specification.get(field_name)
    return numerical_fields


def get_field_category_for_unit_conversion(field_name: str) -> str:
    """Get the unit conversion category for a field."""
    if "weight" in field_name:
        return "weight"
    elif "strength" in field_name:
        return "strength"
    else:
        return "dimensions"
