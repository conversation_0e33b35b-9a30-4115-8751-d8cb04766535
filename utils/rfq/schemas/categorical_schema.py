# ruff: noqa: E501
"""
Categorical Data Schema - For Validation Agent processing.

Contains only categorical fields (grades, coatings, finishes, forms, etc.)
that require catalog validation and business rule application.
"""

from typing import Any

# ============================================================================
# CATEGORICAL DATA SCHEMA (Input to Validation Agent)
# ============================================================================

CATEGORICAL_DATA_SCHEMA: dict[str, Any] = {
    "request_id": None,
    "client_id": None,
    "material_specs": [
        {
            "spec_id": None,
            "categorical_fields": {
                "grade": None,
                "coating": None,
                "finish": None,
                "form": None,
                "choice": None,
                "surface_type": None,
                "surface_protection": None,
                "certificate": [],
                "mandatory_tests": [],
            },
            "extraction_context": {
                "confidence_scores": {},  # field -> confidence mapping
                "source_references": {},  # field -> source text mapping
                "extraction_flags": [],
                "assumptions_made": [],
            },
        }
    ],
    "processing_metadata": {"source_agent": "extraction", "data_type": "categorical", "split_timestamp": None, "total_specs": 0},
}

# ============================================================================
# VALIDATION AGENT OUTPUT SCHEMA
# ============================================================================

VALIDATION_AGENT_OUTPUT_SCHEMA: dict[str, Any] = {
    "request_id": None,
    "client_id": None,
    "material_specs": [
        {
            "spec_id": None,  # Preserved from extraction
            "validated_categorical_fields": {
                # Corrected/validated categorical values
                "grade": None,
                "coating": None,
                "finish": None,
                "form": None,
                "choice": None,
                "surface_type": None,
                "surface_protection": None,
                "certificate": [],
                "mandatory_tests": [],
            },
            "validation_metadata": {
                "corrections_made": [],  # List of field corrections
                "catalog_validations": {},  # field -> validation status
                "inference_applied": [],  # List of inferences made
                "validation_confidence": 0.0,
                "german_translations": [],  # German terms translated
                "surface_codes_parsed": [],  # Combined codes parsed (e.g., Z275MAC)
                "validation_errors": [],
                "validation_warnings": [],
            },
            # Preserved extraction context
            "extraction_context": {"confidence_scores": {}, "source_references": {}, "extraction_flags": [], "assumptions_made": []},
        }
    ],
    "validation_summary": {
        "total_corrections": 0,
        "validation_confidence": 0.0,
        "processing_time_ms": 0,
        "catalog_issues": [],
        "validation_agent_version": "2.0",
        "catalogs_checked": [],
    },
}

# ============================================================================
# CATEGORICAL FIELD DEFINITIONS
# ============================================================================

CATEGORICAL_FIELD_DEFINITIONS = {
    "grade": {
        "validation_type": "catalog_lookup",
        "catalog_name": "steel_grades",
        "required": False,
        "inference_rules": ["infer_from_text_patterns"],
        "normalization_rules": ["remove_spaces", "uppercase"],
    },
    "coating": {
        "validation_type": "catalog_lookup",
        "catalog_name": "coatings",
        "required": False,
        "inference_rules": ["parse_combined_codes"],
        "normalization_rules": ["standardize_format"],
    },
    "finish": {
        "validation_type": "catalog_lookup",
        "catalog_name": "finishes",
        "required": False,
        "inference_rules": ["infer_from_coating", "infer_from_grade"],
        "normalization_rules": ["standardize_terminology"],
    },
    "form": {
        "validation_type": "catalog_lookup",
        "catalog_name": "forms",
        "required": False,
        "inference_rules": ["infer_from_dimensions"],
        "normalization_rules": ["standardize_terminology"],
    },
    "choice": {
        "validation_type": "catalog_lookup",
        "catalog_name": "choices",
        "required": False,
        "inference_rules": ["default_to_first"],
        "normalization_rules": ["standardize_terminology"],
    },
    "surface_type": {
        "validation_type": "catalog_lookup",
        "catalog_name": "surface_types",
        "required": False,
        "inference_rules": ["parse_from_coating_suffix"],
        "normalization_rules": ["expand_abbreviations"],
    },
    "surface_protection": {
        "validation_type": "catalog_lookup",
        "catalog_name": "surface_protections",
        "required": False,
        "inference_rules": ["parse_from_coating_suffix", "translate_german"],
        "normalization_rules": ["expand_abbreviations"],
    },
    "certificate": {
        "validation_type": "array_validation",
        "catalog_name": "certificates",
        "required": False,
        "inference_rules": [],
        "normalization_rules": ["standardize_certificate_names"],
    },
    "mandatory_tests": {
        "validation_type": "array_validation",
        "catalog_name": "tests",
        "required": False,
        "inference_rules": [],
        "normalization_rules": ["standardize_test_names"],
    },
}

# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================


def create_empty_categorical_data(request_id: str | None = None, client_id: str | None = None) -> dict[str, Any]:
    """Create empty categorical data structure."""
    data: dict[str, Any] = CATEGORICAL_DATA_SCHEMA.copy()
    if request_id:
        data["request_id"] = request_id
    if client_id:
        data["client_id"] = client_id
    data["material_specs"] = []
    return data


def create_empty_validation_output(request_id: str | None = None, client_id: str | None = None) -> dict[str, Any]:
    """Create empty validation output structure."""
    output: dict[str, Any] = VALIDATION_AGENT_OUTPUT_SCHEMA.copy()
    if request_id:
        output["request_id"] = request_id
    if client_id:
        output["client_id"] = client_id
    output["material_specs"] = []
    return output


def add_categorical_spec(
    categorical_data: dict[str, Any], spec_id: str, categorical_fields: dict[str, Any], extraction_context: dict[str, Any] | None = None
) -> dict[str, Any]:
    """Add a categorical specification to the data structure."""
    spec = {"spec_id": spec_id, "categorical_fields": categorical_fields, "extraction_context": extraction_context or {}}
    categorical_data["material_specs"].append(spec)
    categorical_data["processing_metadata"]["total_specs"] = len(categorical_data["material_specs"])
    return categorical_data


def get_categorical_field_names() -> list[str]:
    """Get list of all categorical field names."""
    return list(CATEGORICAL_FIELD_DEFINITIONS.keys())


def validate_categorical_data_structure(data: dict[str, Any]) -> list[str]:
    """Validate categorical data structure and return list of errors."""
    errors = []

    # Check required top-level fields
    required_fields = ["request_id", "material_specs", "processing_metadata"]
    for field in required_fields:
        if field not in data:
            errors.append(f"Missing required field: {field}")

    # Check material specs structure
    specs = data.get("material_specs", [])
    if not isinstance(specs, list):
        errors.append("material_specs must be a list")
    else:
        for i, spec in enumerate(specs):
            if "spec_id" not in spec:
                errors.append(f"Material spec {i} missing 'spec_id' field")
            if "categorical_fields" not in spec:
                errors.append(f"Material spec {i} missing 'categorical_fields' field")
            if "extraction_context" not in spec:
                errors.append(f"Material spec {i} missing 'extraction_context' field")

    return errors


def extract_categorical_fields_from_specification(specification: dict[str, Any]) -> dict[str, Any]:
    """Extract only categorical fields from a complete specification."""
    categorical_fields = {}
    for field_name in get_categorical_field_names():
        categorical_fields[field_name] = specification.get(field_name, None if field_name not in ["certificate", "mandatory_tests"] else [])
    return categorical_fields
