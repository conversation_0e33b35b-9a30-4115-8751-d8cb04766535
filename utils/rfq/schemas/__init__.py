"""
Modular RFQ Schema Package

This package contains the new modular, domain-specific schemas for the RFQ processing pipeline.
Each agent handles only their specialized area of expertise.
"""

# Import schemas only (utilities should be imported directly from domain_splitter)
from .categorical_schema import CATEGORICAL_DATA_SCHEMA, VALIDATION_AGENT_OUTPUT_SCHEMA
from .extraction_schema import EXTRACTION_AGENT_OUTPUT_SCHEMA
from .formatter_schema import CLIENT_CONFIGURATION_SCHEMA, DEFAULT_FORMATTER_OUTPUT_SCHEMA
from .merged_schema import MERGED_OUTPUT_SCHEMA
from .numerical_schema import NORMALIZER_AGENT_OUTPUT_SCHEMA, NUMERICAL_DATA_SCHEMA

__all__ = [
    # Schemas only - utilities should be imported directly from domain_splitter
    "EXTRACTION_AGENT_OUTPUT_SCHEMA",
    "CATEGORICAL_DATA_SCHEMA",
    "VALIDATION_AGENT_OUTPUT_SCHEMA",
    "NUMERICAL_DATA_SCHEMA",
    "NORMALIZER_AGENT_OUTPUT_SCHEMA",
    "MERGED_OUTPUT_SCHEMA",
    "CLIENT_CONFIGURATION_SCHEMA",
    "DEFAULT_FORMATTER_OUTPUT_SCHEMA",
]
