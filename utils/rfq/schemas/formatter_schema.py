# ruff: noqa: E501
"""
Formatter Schema - Client-customizable output formats.

This module defines various output templates for the formatter agent
to produce client-specific results based on their configuration.
"""

from typing import Any

# ============================================================================
# FORMATTER OUTPUT TEMPLATES
# ============================================================================

DEFAULT_FORMATTER_OUTPUT_SCHEMA: dict[str, str | dict[str, Any]] = {
    "version": "2.0",
    "minimal": {
        "request_id": None,
        "client_id": None,
        "materials": [
            {
                "grade": None,
                "coating": None,
                "finish": None,
                "form": None,
                "choice": None,
                "surface_type": None,
                "surface_protection": None,
                "thickness_min": None,
                "thickness_max": None,
                "thickness_unit": "mm",
                "width_min": None,
                "width_max": None,
                "width_unit": "mm",
                "length_min": None,
                "length_max": None,
                "length_unit": "mm",
                "weight_min": None,
                "weight_max": None,
                "weight_unit": "kg",
                "coil_max_weight": None,
                "yield_strength_min": None,
                "yield_strength_max": None,
                "certificates": [],
                "mandatory_tests": [],
            }
        ],
    },
    "standard": {
        "request_id": None,
        "client_id": None,
        "specifications": [
            {
                "material_properties": {
                    "grade": None,
                    "coating": None,
                    "finish": None,
                    "form": None,
                    "choice": None,
                    "surface_type": None,
                    "surface_protection": None,
                },
                "dimensions": {
                    "thickness_min": None,
                    "thickness_max": None,
                    "thickness_unit": "mm",
                    "width_min": None,
                    "width_max": None,
                    "width_unit": "mm",
                    "length_min": None,
                    "length_max": None,
                    "length_unit": "mm",
                    "inner_diameter_min": None,
                    "inner_diameter_max": None,
                    "outer_diameter_min": None,
                    "outer_diameter_max": None,
                    "diameter_unit": "mm",
                },
                "weight_info": {"weight_min": None, "weight_max": None, "weight_unit": "kg", "coil_max_weight": None, "coil_weight_unit": "kg"},
                "mechanical_properties": {
                    "yield_strength_min": None,
                    "yield_strength_max": None,
                    "yield_strength_unit": "N/mm²",
                    "tensile_strength_min": None,
                    "tensile_strength_max": None,
                    "tensile_strength_unit": "N/mm²",
                    "elongation_min": None,
                    "elongation_max": None,
                    "elongation_unit": "%",
                },
                "requirements": {"certificates": [], "mandatory_tests": []},
            }
        ],
        "processing_quality": {"confidence_score": 0.0, "corrections_applied": 0, "warnings": []},
    },
    "api": {
        "request_id": None,
        "status": "success",
        "data": {
            "material_specifications": [
                {
                    "material_properties": {
                        "grade": None,
                        "coating": None,
                        "finish": None,
                        "form": None,
                        "choice": None,
                        "surface_type": None,
                        "surface_protection": None,
                    },
                    "dimensions": {
                        "thickness_min": None,
                        "thickness_max": None,
                        "thickness_unit": "mm",
                        "width_min": None,
                        "width_max": None,
                        "width_unit": "mm",
                        "length_min": None,
                        "length_max": None,
                        "length_unit": "mm",
                        "inner_diameter_min": None,
                        "inner_diameter_max": None,
                        "outer_diameter_min": None,
                        "outer_diameter_max": None,
                        "diameter_unit": "mm",
                    },
                    "weight_info": {"weight_min": None, "weight_max": None, "weight_unit": "kg", "coil_max_weight": None, "coil_weight_unit": "kg"},
                    "mechanical_properties": {
                        "yield_strength_min": None,
                        "yield_strength_max": None,
                        "yield_strength_unit": "N/mm²",
                        "tensile_strength_min": None,
                        "tensile_strength_max": None,
                    },
                    "requirements": {"certificates": [], "mandatory_tests": []},
                }
            ],
            "confidence_score": 0.0,
            "processing_time_ms": 0,
        },
        "metadata": {"specifications_count": 0, "corrections_applied": 0, "warnings": []},
    },
}

# ============================================================================
# CLIENT CONFIGURATION SCHEMA
# ============================================================================

CLIENT_CONFIGURATION_SCHEMA = {
    "client_id": None,
    "output_preferences": {
        "format_type": "api",  # minimal, standard, detailed, api, csv_friendly
        "include_metadata": False,
        "include_confidence_scores": False,
        "include_processing_details": False,
        "include_warnings": False,
        "output_langauge": "en",  # en, de, fr, es, etc.
    },
    "value_formatting": {
        "None_value_display": "None",  # How to display None/empty values
        "use_german_floating": False,  # use comma for floating point
    },
}

# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================
