# ruff: noqa: E501
"""
Extraction Agent Schema - Entry point for RFQ processing pipeline.

The extraction agent outputs complete material specifications that will be
split into categorical and numerical data for specialized processing.
"""

from typing import Any

# ============================================================================
# EXTRACTION AGENT OUTPUT SCHEMA
# ============================================================================

EXTRACTION_AGENT_OUTPUT_SCHEMA: dict[str, Any] = {
    "request_id": None,
    "client_id": None,
    "original_text": None,
    "processing_timestamp": None,
    "material_specs": [
        {
            "spec_id": None,  # Will be generated post-extraction
            "specification": {
                # ========== CATEGORICAL FIELDS (for Validation Agent) ==========
                "grade": None,
                "coating": None,
                "finish": None,
                "form": None,
                "choice": None,
                "surface_type": None,
                "surface_protection": None,
                "certificate": [],
                "mandatory_tests": [],
                # ========== NUMERICAL FIELDS (for Normalizer Agent) ==========
                "thickness_min": None,
                "thickness_max": None,
                "width_min": None,
                "width_max": None,
                "length_min": None,
                "length_max": None,
                "height_min": None,
                "height_max": None,
                "inner_diameter_min": None,
                "inner_diameter_max": None,
                "outer_diameter_min": None,
                "outer_diameter_max": None,
                "weight_min": None,
                "weight_max": None,
                "coil_max_weight": None,
                "yield_strength_min": None,
                "yield_strength_max": None,
                "tensile_strength_min": None,
                "tensile_strength_max": None,
            },
            "extraction_metadata": {
                "overall_confidence": 0.0,
                "source_references": {},  # field -> source text mapping
                "extraction_flags": [],
                "assumptions_made": [],
                "field_confidence": {},  # field -> confidence score mapping
            },
        }
    ],
    "extraction_summary": {
        "total_specs_extracted": 0,
        "overall_confidence": 0.0,
        "processing_time_ms": 0,
        "language_detected": "English",
        "extraction_agent_version": "2.0",
    },
}

# Field definitions for dynamic schema generation
EXTRACTION_FIELD_DEFINITIONS = {
    # Categorical fields
    "grade": {
        "type": "string",
        "required": False,
        "description": "Steel grade like DCXX, combination of alphabets and digits",
        "domain": "categorical",
    },
    "coating": {
        "type": "string",
        "required": False,
        "description": "Coating type applied to material (e.g., Z275, ZE50/50)",
        "domain": "categorical",
    },
    "finish": {"type": "string", "required": False, "description": "Surface finish of the material", "domain": "categorical"},
    "form": {"type": "string", "required": False, "description": "The form of steel (e.g., Coils, Sheets, Plates, Bars)", "domain": "categorical"},
    "choice": {"type": "string", "required": False, "description": "The choice of steel (e.g., Prime, 1st, 2nd, 3rd, 4th)", "domain": "categorical"},
    "surface_type": {
        "type": "string",
        "required": False,
        "description": "Surface type of steel (e.g., MA for Minimized spangle)",
        "domain": "categorical",
    },
    "surface_protection": {
        "type": "string",
        "required": False,
        "description": "Surface protection type (e.g., C for Chemically passivated)",
        "domain": "categorical",
    },
    "certificate": {
        "type": "array",
        "items": {"type": "string"},
        "required": False,
        "description": "List of required certificates",
        "domain": "categorical",
    },
    "mandatory_tests": {
        "type": "array",
        "items": {"type": "string"},
        "required": False,
        "description": "List of required tests or analyses",
        "domain": "categorical",
    },
    # Numerical fields
    "thickness_min": {"type": "number", "required": False, "description": "Minimum thickness value in mm", "domain": "numerical"},
    "thickness_max": {"type": "number", "required": False, "description": "Maximum thickness value in mm", "domain": "numerical"},
    "width_min": {"type": "number", "required": False, "description": "Minimum width value in mm", "domain": "numerical"},
    "width_max": {"type": "number", "required": False, "description": "Maximum width value in mm", "domain": "numerical"},
    "length_min": {"type": "number", "required": False, "description": "Minimum length value in mm", "domain": "numerical"},
    "length_max": {"type": "number", "required": False, "description": "Maximum length value in mm", "domain": "numerical"},
    "height_min": {"type": "number", "required": False, "description": "Minimum height value in mm", "domain": "numerical"},
    "height_max": {"type": "number", "required": False, "description": "Maximum height value in mm", "domain": "numerical"},
    "inner_diameter_min": {"type": "number", "required": False, "description": "Minimum inner diameter value in mm", "domain": "numerical"},
    "inner_diameter_max": {"type": "number", "required": False, "description": "Maximum inner diameter value in mm", "domain": "numerical"},
    "outer_diameter_min": {"type": "number", "required": False, "description": "Minimum outer diameter value in mm", "domain": "numerical"},
    "outer_diameter_max": {"type": "number", "required": False, "description": "Maximum outer diameter value in mm", "domain": "numerical"},
    "weight_min": {"type": "number", "required": False, "description": "Minimum weight value in kg", "domain": "numerical"},
    "weight_max": {"type": "number", "required": False, "description": "Maximum weight value in kg", "domain": "numerical"},
    "coil_max_weight": {"type": "number", "required": False, "description": "Maximum weight per coil in kg", "domain": "numerical"},
    "yield_strength_min": {"type": "number", "required": False, "description": "Minimum yield strength value in N/mm²", "domain": "numerical"},
    "yield_strength_max": {"type": "number", "required": False, "description": "Maximum yield strength value in N/mm²", "domain": "numerical"},
    "tensile_strength_min": {"type": "number", "required": False, "description": "Minimum tensile strength value in N/mm²", "domain": "numerical"},
    "tensile_strength_max": {"type": "number", "required": False, "description": "Maximum tensile strength value in N/mm²", "domain": "numerical"},
}


def get_categorical_fields() -> list[str]:
    """Get list of categorical field names."""
    return [field for field, def_ in EXTRACTION_FIELD_DEFINITIONS.items() if def_["domain"] == "categorical"]


def get_numerical_fields() -> list[str]:
    """Get list of numerical field names."""
    return [field for field, def_ in EXTRACTION_FIELD_DEFINITIONS.items() if def_["domain"] == "numerical"]


def create_empty_extraction_output(request_id: str | None = None, client_id: str | None = None) -> dict[str, Any]:
    """Create an empty extraction output following the schema."""
    output: dict[str, Any] = EXTRACTION_AGENT_OUTPUT_SCHEMA.copy()
    if request_id:
        output["request_id"] = request_id
    if client_id:
        output["client_id"] = client_id
    output["material_specs"] = []
    return output


def add_material_spec_to_extraction(extraction_output: dict[str, Any], spec_data: dict[str, Any], spec_id: str | None = None) -> dict[str, Any]:
    """Add a material specification to extraction output."""
    spec_template = EXTRACTION_AGENT_OUTPUT_SCHEMA["material_specs"]
    if isinstance(spec_template, list) and len(spec_template) > 0:
        spec = spec_template[0].copy()
    else:
        spec = {"spec_id": None, "specification": {}, "extraction_metadata": {}}
    if spec_id:
        spec["spec_id"] = spec_id

    # Update specification fields
    spec["specification"].update(spec_data.get("specification", {}))

    # Update metadata
    spec["extraction_metadata"].update(spec_data.get("extraction_metadata", {}))

    extraction_output["material_specs"].append(spec)
    extraction_output["extraction_summary"]["total_specs_extracted"] = len(extraction_output["material_specs"])

    return extraction_output
