# ruff: noqa: E501
"""
Merged Schema - Combined output from validation and normalization agents.

This schema represents the complete material specification with both
validated categorical data and normalized numerical data.
"""

from typing import Any

# ============================================================================
# MERGED OUTPUT SCHEMA (Result of combining validation + normalization)
# ============================================================================

MERGED_OUTPUT_SCHEMA: dict[str, Any] = {
    "request_id": None,
    "client_id": None,
    "processing_timestamp": None,
    "material_specs": [
        {
            "spec_id": None,
            "specification": {
                # ========== VALIDATED CATEGORICAL FIELDS ==========
                "grade": None,
                "coating": None,
                "finish": None,
                "form": None,
                "choice": None,
                "surface_type": None,
                "surface_protection": None,
                "certificate": [],
                "mandatory_tests": [],
                # ========== NORMALIZED NUMERICAL FIELDS ==========
                "thickness_min": None,  # In mm, normalized
                "thickness_max": None,
                "width_min": None,  # In mm, normalized
                "width_max": None,
                "length_min": None,  # In mm, normalized
                "length_max": None,
                "height_min": None,  # In mm, normalized
                "height_max": None,
                "inner_diameter_min": None,  # In mm, normalized
                "inner_diameter_max": None,
                "outer_diameter_min": None,  # In mm, normalized
                "outer_diameter_max": None,
                "weight_min": None,  # In kg, normalized
                "weight_max": None,
                "coil_max_weight": None,  # In kg, normalized
                "yield_strength_min": None,  # In N/mm², normalized
                "yield_strength_max": None,
                "tensile_strength_min": None,  # In N/mm², normalized
                "tensile_strength_max": None,
            },
            "processing_metadata": {
                "extraction": {
                    "overall_confidence": 0.0,
                    "source_references": {},
                    "extraction_flags": [],
                    "assumptions_made": [],
                    "field_confidence": {},
                },
                "validation": {
                    "corrections_made": [],
                    "catalog_validations": {},
                    "inference_applied": [],
                    "validation_confidence": 0.0,
                    "german_translations": [],
                    "surface_codes_parsed": [],
                    "validation_errors": [],
                    "validation_warnings": [],
                },
                "normalization": {
                    "unit_conversions": [],
                    "consistency_fixes": [],
                    "range_warnings": [],
                    "tolerance_parsing": [],
                    "field_mapping_corrections": [],
                    "normalization_confidence": 0.0,
                    "normalization_errors": [],
                    "normalization_warnings": [],
                },
            },
            "quality_metrics": {
                "overall_confidence": 0.0,
                "data_completeness": 0.0,  # Percentage of fields with values
                "validation_score": 0.0,
                "normalization_score": 0.0,
            },
        }
    ],
    "processing_summary": {
        "extraction_summary": {},
        "validation_summary": {},
        "normalization_summary": {},
        "overall_confidence": 0.0,
        "total_processing_time_ms": 0,
        "total_specifications_processed": 0,
        "pipeline_errors": [],
        "pipeline_warnings": [],
        "processing_status": "completed",
        "agents_completed": [],
    },
}

# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================


def create_empty_merged_output(request_id: str | None = None, client_id: str | None = None) -> dict[str, Any]:
    """Create empty merged output structure."""
    output = MERGED_OUTPUT_SCHEMA.copy()
    if request_id:
        output["request_id"] = request_id
    if client_id:
        output["client_id"] = client_id
    output["material_specs"] = []
    return output


def add_merged_spec(
    merged_output: dict[str, Any], spec_id: str, specification: dict[str, Any], processing_metadata: dict[str, Any]
) -> dict[str, Any]:
    """Add a merged specification to the output."""

    # Calculate quality metrics
    total_fields = len(specification)
    filled_fields = sum(1 for v in specification.values() if v is not None and v != [])
    data_completeness = filled_fields / total_fields if total_fields > 0 else 0

    validation_confidence = processing_metadata.get("validation", {}).get("validation_confidence", 0.0)
    normalization_confidence = processing_metadata.get("normalization", {}).get("normalization_confidence", 0.0)
    overall_confidence = (validation_confidence + normalization_confidence) / 2

    spec = {
        "spec_id": spec_id,
        "specification": specification,
        "processing_metadata": processing_metadata,
        "quality_metrics": {
            "overall_confidence": overall_confidence,
            "data_completeness": data_completeness,
            "validation_score": validation_confidence,
            "normalization_score": normalization_confidence,
        },
    }

    merged_output["material_specs"].append(spec)
    merged_output["processing_summary"]["total_specifications_processed"] = len(merged_output["material_specs"])

    return merged_output


def calculate_merged_quality_score(merged_output: dict[str, Any]) -> float:
    """Calculate overall quality score for merged output."""
    specs = merged_output.get("material_specs", [])
    if not specs:
        return 0.0

    quality_scores = [spec.get("quality_metrics", {}).get("overall_confidence", 0.0) for spec in specs]

    return sum(quality_scores) / len(quality_scores)


def get_merged_field_statistics(merged_output: dict[str, Any]) -> dict[str, Any]:
    """Get statistics about field completeness in merged output."""
    specs = merged_output.get("material_specs", [])
    if not specs:
        return {"total_specs": 0}

    # Count field completeness across all specs
    field_counts = {}
    total_specs = len(specs)

    for spec in specs:
        specification = spec.get("specification", {})
        for field, value in specification.items():
            if field not in field_counts:
                field_counts[field] = 0
            if value is not None and value != []:
                field_counts[field] += 1

    # Calculate percentages
    field_completeness = {field: (count / total_specs) * 100 for field, count in field_counts.items()}

    # Overall completeness
    total_possible_fields = len(field_counts) * total_specs
    total_filled_fields = sum(field_counts.values())
    overall_completeness = (total_filled_fields / total_possible_fields) * 100 if total_possible_fields > 0 else 0

    return {
        "total_specs": total_specs,
        "field_completeness_percentages": field_completeness,
        "overall_completeness_percentage": overall_completeness,
        "most_complete_fields": sorted(field_completeness.items(), key=lambda x: x[1], reverse=True)[:5],
        "least_complete_fields": sorted(field_completeness.items(), key=lambda x: x[1])[:5],
    }


def validate_merged_output_structure(merged_output: dict[str, Any]) -> list[str]:
    """Validate merged output structure."""
    errors = []

    # Check required top-level fields
    required_fields = ["request_id", "material_specs", "processing_summary"]
    for field in required_fields:
        if field not in merged_output:
            errors.append(f"Missing required field: {field}")

    # Check material specs structure
    specs = merged_output.get("material_specs", [])
    if not isinstance(specs, list):
        errors.append("material_specs must be a list")
    else:
        for i, spec in enumerate(specs):
            if "spec_id" not in spec:
                errors.append(f"Material spec {i} missing 'spec_id' field")
            if "specification" not in spec:
                errors.append(f"Material spec {i} missing 'specification' field")
            if "processing_metadata" not in spec:
                errors.append(f"Material spec {i} missing 'processing_metadata' field")
            if "quality_metrics" not in spec:
                errors.append(f"Material spec {i} missing 'quality_metrics' field")

    return errors


def extract_specification_only(merged_output: dict[str, Any]) -> dict[str, Any]:
    """Extract only the core specifications without metadata."""
    result: dict[str, Any] = {"request_id": merged_output.get("request_id"), "client_id": merged_output.get("client_id"), "specifications": []}

    specs = merged_output.get("material_specs", [])
    if isinstance(specs, list):
        for spec in specs:
            if isinstance(spec, dict):
                result["specifications"].append({"spec_id": spec.get("spec_id"), "specification": spec.get("specification", {})})

    return result
