"""
Version parsing utilities for agent-specific semantic versioning.

Supports agent-prefixed semantic versions like:
- e.1.0.0 (extractor agent version 1.0.0)
- v.1.2.1 (validator agent version 1.2.1)
- n.2.0.0 (normalizer agent version 2.0.0)
- f.1.1.0 (formatter agent version 1.1.0)
"""

import re
from dataclasses import dataclass


@dataclass
class AgentVersionParser:
    """Parser for agent-specific semantic versions."""

    agent_prefix: str
    major: int
    minor: int
    patch: int

    def __str__(self) -> str:
        """Return the full version string with agent prefix."""
        return f"{self.agent_prefix}.{self.major}.{self.minor}.{self.patch}"

    def __eq__(self, other) -> bool:
        """Check if two versions are equal."""
        if not isinstance(other, AgentVersionParser):
            return False
        return self.agent_prefix == other.agent_prefix and self.major == other.major and self.minor == other.minor and self.patch == other.patch

    def __lt__(self, other) -> bool:
        """Check if this version is less than another."""
        if not isinstance(other, AgentVersionParser):
            return NotImplemented
        if self.agent_prefix != other.agent_prefix:
            return self.agent_prefix < other.agent_prefix
        return (self.major, self.minor, self.patch) < (other.major, other.minor, other.patch)

    def __le__(self, other) -> bool:
        """Check if this version is less than or equal to another."""
        return self < other or self == other

    def __gt__(self, other) -> bool:
        """Check if this version is greater than another."""
        return not self <= other

    def __ge__(self, other) -> bool:
        """Check if this version is greater than or equal to another."""
        return not self < other

    def is_compatible_with(self, other) -> bool:
        """Check if this version is compatible with another (same agent, same major version)."""
        if not isinstance(other, AgentVersionParser):
            return False
        return self.agent_prefix == other.agent_prefix and self.major == other.major

    def semantic_version(self) -> str:
        """Return just the semantic version part without agent prefix."""
        return f"{self.major}.{self.minor}.{self.patch}"

    def next_patch(self) -> "AgentVersionParser":
        """Return the next patch version."""
        return AgentVersionParser(agent_prefix=self.agent_prefix, major=self.major, minor=self.minor, patch=self.patch + 1)

    def next_minor(self) -> "AgentVersionParser":
        """Return the next minor version (resets patch to 0)."""
        return AgentVersionParser(agent_prefix=self.agent_prefix, major=self.major, minor=self.minor + 1, patch=0)

    def next_major(self) -> "AgentVersionParser":
        """Return the next major version (resets minor and patch to 0)."""
        return AgentVersionParser(agent_prefix=self.agent_prefix, major=self.major + 1, minor=0, patch=0)


# Agent prefix mappings
AGENT_PREFIXES = {"e": "extractor", "v": "validator", "n": "normalizer", "f": "formatter"}

REVERSE_AGENT_PREFIXES = {"extractor": "e", "validator": "v", "normalizer": "n", "formatter": "f"}


def parse_agent_version(version_str: str) -> AgentVersionParser | None:
    """
    Parse an agent-specific version string.

    Args:
        version_str: Version string like "e.1.0.0", "v.1.2.1", etc.

    Returns:
        AgentVersionParser object or None if parsing fails

    Examples:
        >>> parse_agent_version("e.1.0.0")
        AgentVersionParser(agent_prefix='e', major=1, minor=0, patch=0)

        >>> parse_agent_version("v.2.1.3")
        AgentVersionParser(agent_prefix='v', major=2, minor=1, patch=3)
    """
    if not version_str:
        return None

    # Pattern: agent_prefix.major.minor.patch
    pattern = r"^([evnf])\.([0-9]+)\.([0-9]+)\.([0-9]+)$"
    match = re.match(pattern, version_str.strip())

    if not match:
        return None

    agent_prefix, major_str, minor_str, patch_str = match.groups()

    try:
        major = int(major_str)
        minor = int(minor_str)
        patch = int(patch_str)

        # Validate agent prefix
        if agent_prefix not in AGENT_PREFIXES:
            return None

        return AgentVersionParser(agent_prefix=agent_prefix, major=major, minor=minor, patch=patch)
    except (ValueError, TypeError):
        return None


def create_agent_version(agent_type: str, major: int = 1, minor: int = 0, patch: int = 0) -> AgentVersionParser | None:
    """
    Create an agent version from agent type and semantic version components.

    Args:
        agent_type: Agent type ('extractor', 'validator', 'normalizer', 'formatter')
        major: Major version number
        minor: Minor version number
        patch: Patch version number

    Returns:
        AgentVersionParser object or None if invalid agent type

    Examples:
        >>> create_agent_version("extractor", 1, 0, 0)
        AgentVersionParser(agent_prefix='e', major=1, minor=0, patch=0)

        >>> create_agent_version("validator", 2, 1, 3)
        AgentVersionParser(agent_prefix='v', major=2, minor=1, patch=3)
    """
    if agent_type not in REVERSE_AGENT_PREFIXES:
        return None

    prefix = REVERSE_AGENT_PREFIXES[agent_type]

    return AgentVersionParser(agent_prefix=prefix, major=max(0, major), minor=max(0, minor), patch=max(0, patch))


def get_agent_type_from_prefix(prefix: str) -> str | None:
    """
    Get the full agent type name from prefix.

    Args:
        prefix: Agent prefix ('e', 'v', 'n', 'f')

    Returns:
        Full agent type name or None if invalid prefix
    """
    return AGENT_PREFIXES.get(prefix)


def get_prefix_from_agent_type(agent_type: str) -> str | None:
    """
    Get the agent prefix from full agent type name.

    Args:
        agent_type: Full agent type ('extractor', 'validator', 'normalizer', 'formatter')

    Returns:
        Agent prefix or None if invalid agent type
    """
    return REVERSE_AGENT_PREFIXES.get(agent_type)


def is_valid_agent_version(version_str: str) -> bool:
    """
    Check if a version string is a valid agent version.

    Args:
        version_str: Version string to validate

    Returns:
        True if valid, False otherwise
    """
    return parse_agent_version(version_str) is not None
