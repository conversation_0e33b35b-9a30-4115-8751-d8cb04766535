# ruff: noqa: E501
"""
Domain Splitter - Utility to split extraction output into categorical and numerical data.

This module handles the splitting of extraction agent output into domain-specific
data streams for parallel processing by validation and normalizer agents.
"""

import uuid
from datetime import UTC, datetime
from typing import Any

from utils.log import get_app_logger

from .schemas.categorical_schema import (
    add_categorical_spec,
    create_empty_categorical_data,
    extract_categorical_fields_from_specification,
)
from .schemas.numerical_schema import add_numerical_spec, create_empty_numerical_data, extract_numerical_fields_from_specification


def split_extraction_output(extraction_output: dict[str, Any]) -> tuple[dict[str, Any], dict[str, Any]]:
    """
    Split extraction output into categorical and numerical data streams.

    Args:
        extraction_output: Output from extraction agent following EXTRACTION_AGENT_OUTPUT_SCHEMA

    Returns:
        Tuple of (categorical_data, numerical_data) for respective agents
    """
    # Extract metadata
    request_id = extraction_output.get("request_id")
    client_id = extraction_output.get("client_id")
    original_text = extraction_output.get("original_text")

    # Create empty structures
    categorical_data = create_empty_categorical_data(request_id, client_id)
    numerical_data = create_empty_numerical_data(request_id, client_id)

    # Set original text for normalizer (needed for cross-referencing)
    numerical_data["original_text"] = original_text

    # Set split timestamp
    split_timestamp = datetime.now(UTC).isoformat()
    categorical_data["processing_metadata"]["split_timestamp"] = split_timestamp
    numerical_data["processing_metadata"]["split_timestamp"] = split_timestamp

    # Process each material specification
    for spec in extraction_output.get("material_specs", []):
        spec_id = spec.get("spec_id")
        specification = spec.get("specification", {})
        extraction_metadata = spec.get("extraction_metadata", {})

        # Extract categorical fields
        categorical_fields = extract_categorical_fields_from_specification(specification)
        categorical_context = {
            "confidence_scores": {field: extraction_metadata.get("field_confidence", {}).get(field, 0.0) for field in categorical_fields},
            "source_references": {field: extraction_metadata.get("source_references", {}).get(field, "") for field in categorical_fields},
            "extraction_flags": extraction_metadata.get("extraction_flags", []),
            "assumptions_made": extraction_metadata.get("assumptions_made", []),
        }

        # Extract numerical fields
        numerical_fields = extract_numerical_fields_from_specification(specification)

        numerical_context = {
            "confidence_scores": {field: extraction_metadata.get("field_confidence", {}).get(field, 0.0) for field in numerical_fields},
            "source_references": {field: extraction_metadata.get("source_references", {}).get(field, "") for field in numerical_fields},
            "extraction_flags": extraction_metadata.get("extraction_flags", []),
            "raw_extracted_values": {
                field: specification.get(field)  # Store original values
                for field in numerical_fields
                if specification.get(field) is not None
            },
            "assumptions_made": extraction_metadata.get("assumptions_made", []),
        }

        # Add to respective data structures
        add_categorical_spec(categorical_data, spec_id, categorical_fields, categorical_context)

        # Always add to numerical data, even if no numerical fields
        # This ensures spec_id consistency between validation and normalization
        add_numerical_spec(numerical_data, spec_id, numerical_fields, numerical_context)

    return categorical_data, numerical_data


def merge_agent_outputs(
    categorical_output: dict[str, Any], numerical_output: dict[str, Any], extraction_summary: dict[str, Any] | None = None
) -> dict[str, Any]:
    """
    Merge validated categorical and normalized numerical outputs into unified structure.

    Args:
        categorical_output: Output from validation agent
        numerical_output: Output from normalizer agent
        extraction_summary: Original extraction summary (optional)

    Returns:
        Merged output following MERGED_OUTPUT_SCHEMA
    """

    logger = get_app_logger()

    # Extract metadata
    request_id = categorical_output.get("request_id") or numerical_output.get("request_id")
    client_id = categorical_output.get("client_id") or numerical_output.get("client_id")

    # Create empty merged structure
    merged: dict[str, Any] = {"request_id": request_id, "client_id": client_id, "material_specs": [], "processing_summary": {}}

    # Get specs from both outputs and validate spec_id presence
    cat_specs = categorical_output.get("corrected_rfq_data", {}).get("material_specs", [])

    # Try multiple locations for numerical specs based on actual normalizer agent output
    num_specs = numerical_output.get("material_specs", [])
    if not num_specs and "corrected_rfq_data" in numerical_output:
        num_specs = numerical_output["corrected_rfq_data"].get("material_specs", [])

    logger.debug(f"Found {len(num_specs)} numerical specs from normalizer output")

    logger.info(f"Merging {len(cat_specs)} categorical specs with {len(num_specs)} numerical specs")

    # Debug: Log the structure of the numerical output to understand the format
    if len(num_specs) == 0:
        logger.warning("🔍 No numerical specs found! Debugging normalizer output structure:")
        logger.warning(f"🔍 Numerical output keys: {list(numerical_output.keys()) if isinstance(numerical_output, dict) else 'Not a dict'}")
        if isinstance(numerical_output, dict) and "corrected_rfq_data" in numerical_output:
            corrected_data = numerical_output["corrected_rfq_data"]
            logger.warning(f"🔍 corrected_rfq_data keys: {list(corrected_data.keys()) if isinstance(corrected_data, dict) else 'Not a dict'}")
            if isinstance(corrected_data, dict) and "material_specs" in corrected_data:
                logger.warning(f"🔍 corrected_rfq_data.material_specs length: {len(corrected_data['material_specs'])}")
                if corrected_data["material_specs"]:
                    sample_spec = corrected_data["material_specs"][0]
                    logger.warning(f"🔍 Sample spec keys: {list(sample_spec.keys()) if isinstance(sample_spec, dict) else 'Not a dict'}")

        # Also check if there's a different structure
        for key in ["normalized_specs", "normalized_data", "normalization_results"]:
            if key in numerical_output:
                logger.warning(f"🔍 Found alternative key '{key}' in numerical output")

    # Validate spec_ids in both outputs
    cat_spec_ids = [spec.get("spec_id") for spec in cat_specs if isinstance(spec, dict)]
    num_spec_ids = [spec.get("spec_id") for spec in num_specs if isinstance(spec, dict)]

    missing_cat_ids = [i for i, spec_id in enumerate(cat_spec_ids) if not spec_id]
    missing_num_ids = [i for i, spec_id in enumerate(num_spec_ids) if not spec_id]

    if missing_cat_ids:
        logger.warning(f"⚠️ Categorical specs missing spec_ids at indices: {missing_cat_ids}")
    if missing_num_ids:
        logger.warning(f"⚠️ Numerical specs missing spec_ids at indices: {missing_num_ids}")

    logger.debug(f"Categorical spec_ids: {cat_spec_ids}")
    logger.debug(f"Numerical spec_ids: {num_spec_ids}")

    # Create optimized lookup for numerical specs using spec_id as primary key
    numerical_specs_by_id = {}  # spec_id -> spec
    numerical_specs_by_index = {}  # index -> spec

    for i, spec in enumerate(num_specs):
        if isinstance(spec, dict):
            spec_id = spec.get("spec_id")
            # Always store by index for fallback
            numerical_specs_by_index[i] = spec
            # Store by spec_id if available for primary matching
            if spec_id and spec_id is not None:
                numerical_specs_by_id[spec_id] = spec

    logger.debug(f"Created numerical spec lookup: {len(numerical_specs_by_id)} by spec_id, {len(numerical_specs_by_index)} by index")

    # Merge material specifications using spec_id-first approach
    merged_count = 0
    spec_id_matches = 0
    index_fallbacks = 0

    if isinstance(cat_specs, list):
        for i, cat_spec in enumerate(cat_specs):
            if isinstance(cat_spec, dict):
                spec_id = cat_spec.get("spec_id")

                # PRIORITY 1: Match by spec_id (this is the primary method)
                num_spec = None
                match_method = "none"

                if spec_id and spec_id in numerical_specs_by_id:
                    num_spec = numerical_specs_by_id[spec_id]
                    match_method = "spec_id"
                    spec_id_matches += 1
                    logger.debug(f"✅ Matched spec {i} by spec_id: {spec_id}")

                # PRIORITY 2: Fallback to index matching
                if num_spec is None and i in numerical_specs_by_index:
                    num_spec = numerical_specs_by_index[i]
                    match_method = "index"
                    index_fallbacks += 1
                    logger.debug(f"⚠️ Matched spec {i} by index fallback (spec_id: {spec_id})")

                # PRIORITY 3: No match found
                if num_spec is None:
                    logger.warning(f"❌ No numerical spec found for categorical spec {i} (spec_id: {spec_id})")
                    num_spec = {}  # Empty spec for partial merge

                # Combine categorical and numerical fields
                merged_specification = {}

                # Extract fields from categorical spec - use actual field names returned by agents
                cat_fields = cat_spec.get("categorical_fields", {})
                if not cat_fields:
                    # Fallback 1: try validated_categorical_fields (legacy format)
                    cat_fields = cat_spec.get("validated_categorical_fields", {})
                    if not cat_fields and "corrected_rfq_data" in categorical_output:
                        # Fallback 2: try to get fields from corrected_rfq_data or other structures
                        corrected_data = categorical_output["corrected_rfq_data"]
                        if "material_specs" in corrected_data and i < len(corrected_data["material_specs"]):
                            spec_data = corrected_data["material_specs"][i]
                            cat_fields = spec_data.get("specification", {})

                merged_specification.update(cat_fields)

                # Extract fields from numerical spec - use actual field names returned by agents
                num_fields = {}

                # Try to get normalized numerical fields from the normalizer output
                if num_spec.get("normalized_numerical_fields"):
                    # This is the expected structure from NORMALIZER_AGENT_OUTPUT_SCHEMA
                    num_fields = num_spec["normalized_numerical_fields"]
                    logger.debug(f"Found normalized_numerical_fields for spec {i}: {list(num_fields.keys())}")
                elif num_spec.get("numerical_fields"):
                    # Fallback for alternative structure
                    num_fields = num_spec["numerical_fields"]
                    logger.debug(f"Found numerical_fields for spec {i}: {list(num_fields.keys())}")
                elif "corrected_rfq_data" in numerical_output:
                    # Fallback: try to get fields from corrected_rfq_data structure
                    corrected_data = numerical_output["corrected_rfq_data"]
                    if "material_specs" in corrected_data and i < len(corrected_data["material_specs"]):
                        spec_data = corrected_data["material_specs"][i]
                        num_fields = spec_data.get("specification", {})
                        logger.debug(f"Found numerical fields from corrected_rfq_data for spec {i}: {list(num_fields.keys())}")

                # Only add non-null numerical fields to preserve null values in schema
                for field_name, field_value in num_fields.items():
                    merged_specification[field_name] = field_value

                logger.debug(f"Merged specification for spec {i} has {len(merged_specification)} fields: {list(merged_specification.keys())}")

                # Add debug logging for available metadata fields
                logger.info(f"🔍 Available fields in cat_spec {i}: {list(cat_spec.keys()) if isinstance(cat_spec, dict) else 'Not a dict'}")
                logger.info(f"🔍 Available fields in num_spec {i}: {list(num_spec.keys()) if isinstance(num_spec, dict) else 'Not a dict'}")

                # Debug agent-level structures (only once to avoid spam)
                if i == 0:
                    logger.info(
                        f"🔍 Agent-level categorical_output keys: {list(categorical_output.keys()) if isinstance(categorical_output, dict) else 'Not a dict'}"
                    )
                    logger.info(
                        f"🔍 Agent-level numerical_output keys: {list(numerical_output.keys()) if isinstance(numerical_output, dict) else 'Not a dict'}"
                    )

                # Combine processing metadata with robust field extraction
                processing_metadata: dict[str, Any] = {"extraction": {}, "validation": {}, "normalization": {}}

                # Extract extraction metadata (try multiple possible field names)
                extraction_meta = (
                    cat_spec.get("extraction_context", {})
                    or cat_spec.get("extraction_metadata", {})
                    or num_spec.get("extraction_context", {})
                    or num_spec.get("extraction_metadata", {})
                )
                if extraction_meta:
                    processing_metadata["extraction"] = extraction_meta

                # Extract validation metadata - look in the categorical output at agent level
                validation_meta = {}

                # Try to get validation metadata from the agent output structure
                if isinstance(categorical_output, dict):
                    # Look for validation summary or metadata at the agent output level
                    agent_validation_meta = (
                        categorical_output.get("validation_summary", {})
                        or categorical_output.get("validation_metadata", {})
                        or categorical_output.get("corrected_rfq_data", {}).get("validation_summary", {})
                    )
                    if agent_validation_meta:
                        validation_meta = agent_validation_meta

                    # Also try spec-level validation metadata
                    spec_validation_meta = (
                        cat_spec.get("validation_metadata", {}) or cat_spec.get("validation_context", {}) or cat_spec.get("validation_info", {})
                    )
                    if spec_validation_meta:
                        validation_meta.update(spec_validation_meta)

                if validation_meta:
                    processing_metadata["validation"] = validation_meta

                # Extract normalization metadata - look in the numerical output at agent level
                normalization_meta = {}

                # Try to get normalization metadata from the agent output structure
                if isinstance(numerical_output, dict):
                    # Look for normalization summary or metadata at the agent output level
                    agent_normalization_meta = (
                        numerical_output.get("normalization_summary", {})
                        or numerical_output.get("normalization_metadata", {})
                        or numerical_output.get("corrected_rfq_data", {}).get("normalization_summary", {})
                    )
                    if agent_normalization_meta:
                        normalization_meta = agent_normalization_meta

                    # Also try spec-level normalization metadata
                    spec_normalization_meta = (
                        num_spec.get("normalization_metadata", {})
                        or num_spec.get("normalization_context", {})
                        or num_spec.get("normalization_info", {})
                    )
                    if spec_normalization_meta:
                        normalization_meta.update(spec_normalization_meta)

                if normalization_meta:
                    processing_metadata["normalization"] = normalization_meta

                # Add debug logging for metadata extraction
                logger.info(f"📊 Extracted metadata for spec {i}:")
                logger.info(f"📊   Extraction: {len(processing_metadata['extraction'])} fields - {list(processing_metadata['extraction'].keys())}")
                logger.info(f"📊   Validation: {len(processing_metadata['validation'])} fields - {list(processing_metadata['validation'].keys())}")
                logger.info(
                    f"📊   Normalization: {len(processing_metadata['normalization'])} fields - {list(processing_metadata['normalization'].keys())}"
                )

                # Ensure spec_id is set
                final_spec_id = spec_id if spec_id else f"merged_spec_{i}"

                merged_spec = {"spec_id": final_spec_id, "specification": merged_specification, "processing_metadata": processing_metadata}

                material_specs = merged.get("material_specs")
                if isinstance(material_specs, list):
                    material_specs.append(merged_spec)
                    merged_count += 1
                    logger.debug(f"Added merged spec {i} with {len(merged_specification)} fields (match: {match_method})")

    # Log merge statistics
    logger.info(f"Successfully merged {merged_count} specifications")
    logger.info(f"Match statistics: {spec_id_matches} by spec_id, {index_fallbacks} by index fallback")

    if spec_id_matches == len(cat_specs) and spec_id_matches > 0:
        logger.info("✅ Perfect spec_id matching achieved!")
    elif index_fallbacks > 0:
        logger.warning(f"⚠️ {index_fallbacks} specs required index fallback matching")

    # Merge processing summaries
    extraction_summary = extraction_summary or {}
    validation_summary = categorical_output.get("validation_summary", {})
    normalization_summary = numerical_output.get("normalization_summary", {})

    # Calculate overall confidence
    validation_confidence = validation_summary.get("validation_confidence", 0.0)
    normalization_confidence = 0.0
    if numerical_output.get("material_specs"):
        # Average normalization confidence across specs
        norm_confidences = [
            spec.get("normalization_metadata", {}).get("normalization_confidence", 0.0) for spec in numerical_output.get("material_specs", [])
        ]
        if norm_confidences:
            normalization_confidence = sum(norm_confidences) / len(norm_confidences)

    overall_confidence = (validation_confidence + normalization_confidence) / 2

    # Calculate total processing time
    total_processing_time = (
        extraction_summary.get("processing_time_ms", 0)
        + validation_summary.get("processing_time_ms", 0)
        + normalization_summary.get("processing_time_ms", 0)
    )

    merged["processing_summary"] = {
        "extraction_summary": extraction_summary,
        "validation_summary": validation_summary,
        "normalization_summary": normalization_summary,
        "overall_confidence": overall_confidence,
        "total_processing_time_ms": total_processing_time,
        "total_specifications_processed": len(merged["material_specs"]),
        "pipeline_errors": (validation_summary.get("catalog_issues", []) + normalization_summary.get("warnings_issued", [])),
        "pipeline_warnings": [],
        "processing_status": "completed",
        "agents_completed": ["extraction", "validation", "normalization"],
    }

    return merged


def validate_extraction_output(extraction_output: dict[str, Any]) -> list[str]:
    """Validate that extraction output can be split properly."""
    errors = []

    # Check required fields
    required_fields = ["request_id", "material_specs"]
    for field in required_fields:
        if field not in extraction_output:
            errors.append(f"Missing required field: {field}")

    # Check material specs structure
    specs = extraction_output.get("material_specs", [])
    if not isinstance(specs, list):
        errors.append("material_specs must be a list")
    else:
        for i, spec in enumerate(specs):
            if "spec_id" not in spec:
                errors.append(f"Material spec {i} missing 'spec_id' field")
            if "specification" not in spec:
                errors.append(f"Material spec {i} missing 'specification' field")
            if "extraction_metadata" not in spec:
                errors.append(f"Material spec {i} missing 'extraction_metadata' field")

    return errors


def validate_merge_inputs(categorical_output: dict[str, Any], numerical_output: dict[str, Any]) -> list[str]:
    """Validate that agent outputs can be merged properly."""
    from utils.log import get_app_logger

    logger = get_app_logger()

    errors = []

    # Check categorical output structure
    if "material_specs" not in categorical_output:
        # Check for alternative structures
        if "corrected_rfq_data" in categorical_output:
            corrected_data = categorical_output["corrected_rfq_data"]
            if "material_specs" not in corrected_data:
                errors.append("Categorical output missing 'material_specs' field in both root and corrected_rfq_data")
        else:
            errors.append("Categorical output missing 'material_specs' field")

    # Check numerical output structure
    if "material_specs" not in numerical_output:
        # Check for alternative structures
        if "corrected_rfq_data" in numerical_output:
            corrected_data = numerical_output["corrected_rfq_data"]
            if "material_specs" not in corrected_data:
                errors.append("Numerical output missing 'material_specs' field in both root and corrected_rfq_data")
        else:
            errors.append("Numerical output missing 'material_specs' field")

    # Get spec lists for comparison
    cat_specs = categorical_output.get("material_specs", [])
    if not cat_specs and "corrected_rfq_data" in categorical_output:
        cat_specs = categorical_output["corrected_rfq_data"].get("material_specs", [])

    num_specs = numerical_output.get("material_specs", [])
    if not num_specs and "corrected_rfq_data" in numerical_output:
        num_specs = numerical_output["corrected_rfq_data"].get("material_specs", [])

    # Check spec counts
    if len(cat_specs) != len(num_specs):
        logger.warning(f"Spec count mismatch: categorical={len(cat_specs)}, numerical={len(num_specs)}")
        # This is a warning, not an error - we can handle it with fallback matching

    # Check spec_id alignment (with tolerance for None values)
    cat_spec_ids = [spec.get("spec_id") for spec in cat_specs if isinstance(spec, dict)]
    num_spec_ids = [spec.get("spec_id") for spec in num_specs if isinstance(spec, dict)]

    if cat_spec_ids and num_spec_ids and set(cat_spec_ids) != set(num_spec_ids):
        logger.warning(f"Spec ID mismatch: categorical={cat_spec_ids}, numerical={num_spec_ids}")
        # This is a warning, not an error - merge function handles this with fallback

    return errors


def create_spec_id(prefix: str = "spec") -> str:
    """Generate a unique specification ID."""
    return f"{prefix}_{str(uuid.uuid4())[:8]}"


def get_split_statistics(extraction_output: dict[str, Any]) -> dict[str, Any]:
    """Get statistics about the splitting operation."""
    material_specs = extraction_output.get("material_specs", [])
    if not isinstance(material_specs, list):
        material_specs = []

    total_specs = len(material_specs)
    total_categorical_fields = 0
    total_numerical_fields = 0
    specs_with_categorical = 0
    specs_with_numerical = 0

    for spec in material_specs:
        if isinstance(spec, dict):
            specification = spec.get("specification", {})
        else:
            continue

        # Count categorical fields
        categorical_fields = extract_categorical_fields_from_specification(specification)
        categorical_count = sum(1 for v in categorical_fields.values() if v is not None and v != [])
        total_categorical_fields += categorical_count
        if categorical_count > 0:
            specs_with_categorical += 1

        # Count numerical fields
        numerical_fields = extract_numerical_fields_from_specification(specification)
        numerical_count = sum(1 for v in numerical_fields.values() if v is not None)
        total_numerical_fields += numerical_count
        if numerical_count > 0:
            specs_with_numerical += 1

    return {
        "total_specs": total_specs,
        "total_categorical_fields": total_categorical_fields,
        "total_numerical_fields": total_numerical_fields,
        "specs_with_categorical": specs_with_categorical,
        "specs_with_numerical": specs_with_numerical,
        "categorical_coverage": specs_with_categorical / total_specs if total_specs > 0 else 0,
        "numerical_coverage": specs_with_numerical / total_specs if total_specs > 0 else 0,
    }
