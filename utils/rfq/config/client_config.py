"""Client configuration utilities."""

import os

from api.settings import get_settings


def get_default_client_id() -> str:
    """
    Get the default client ID for generic operations.

    This function provides a centralized way to access the default client ID
    that's used throughout the application when no specific client is provided.

    Returns:
        str: The default client ID UUID

    Priority:
        1. Environment variable DEFAULT_CLIENT_ID
        2. Settings configuration DEFAULT_CLIENT_ID
        3. Hardcoded fallback (should not be reached)
    """
    # First try environment variable (highest priority)
    env_client_id = os.getenv("DEFAULT_CLIENT_ID")
    if env_client_id:
        return env_client_id

    # Then try settings configuration
    try:
        settings = get_settings()
        return settings.DEFAULT_CLIENT_ID
    except Exception:
        # Fallback to hardcoded value (should not be reached in normal operation)
        return "00000000-0000-0000-0000-000000000000"


def get_client_id_or_default(client_id: str | None) -> str:
    """
    Get the provided client ID or fall back to the default.

    Args:
        client_id: The client ID to use, or None to use default

    Returns:
        str: The client ID to use (either provided or default)
    """
    return client_id or get_default_client_id()
