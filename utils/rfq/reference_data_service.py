"""
Utility functions for querying reference data with Generic Template support.
This handles the pattern of querying both Generic Template and client-specific data.
"""

from typing import Any, cast
from uuid import UUID

from sqlalchemy import and_, or_
from sqlalchemy.orm import Session

from db.tables.rfq_tables import GradeCoatingCompatibility, ReferenceData
from utils.rfq.config import get_default_client_id

# Generic User Template client ID for global reference data
GENERIC_CLIENT_ID = get_default_client_id()


class ReferenceDataService:
    """Service for querying reference data with Generic Template support."""

    @staticmethod
    def get_reference_data(
        db_session: Session,
        client_id: str | UUID | None,
        catalog_type_name: str,
        code: str | None = None,
        include_global: bool = True,
    ) -> list[dict[str, Any]]:
        """
        Get reference data for a client, optionally including global data.

        Args:
            db_session: Database session
            client_id: Client ID (if None, only returns global data)
            catalog_type_name: Name of the catalog type (e.g., 'grades', 'coatings')
            code: Optional specific code to filter by
            include_global: Whether to include global (Generic Template) data

        Returns:
            List of reference data entries
        """
        from db.tables.rfq_tables import CatalogType

        # Build the query
        query = (
            db_session.query(ReferenceData)
            .join(CatalogType, ReferenceData.catalog_type_id == CatalogType.catalog_type_id)
            .filter(CatalogType.name == catalog_type_name, ReferenceData.is_active)
        )

        # Add code filter if specified
        if code:
            query = query.filter(ReferenceData.code == code)

        # Build client_id filter
        client_filters = []

        # Always include Generic Template data if include_global is True
        if include_global:
            client_filters.append(ReferenceData.client_id == UUID(GENERIC_CLIENT_ID))

        # Include client-specific data if client_id is provided
        if client_id:
            client_uuid = UUID(client_id) if isinstance(client_id, str) else client_id
            client_filters.append(ReferenceData.client_id == client_uuid)

        # Apply client filter
        if client_filters:
            query = query.filter(or_(*client_filters))

        # Execute query and convert to dict
        results = query.all()
        results_list: list[dict[str, Any]] = []
        for result in results:
            results_list.append(
                {
                    "reference_id": str(result.reference_id),
                    "client_id": str(result.client_id) if result.client_id else None,
                    "code": result.code,
                    "name": result.name,
                    "description": result.description,
                    "properties": result.properties or {},
                    "is_global": str(result.client_id) == GENERIC_CLIENT_ID,
                    "created_at": result.created_at.isoformat(),
                    "updated_at": result.updated_at.isoformat(),
                }
            )
        return results_list

    @staticmethod
    def get_grades(
        db_session: Session,
        client_id: str | UUID | None = None,
        include_global: bool = True,
    ) -> list[dict[str, Any]]:
        """Get all steel grades for a client."""
        return ReferenceDataService.get_reference_data(db_session, client_id, "grade", include_global=include_global)

    @staticmethod
    def get_coatings(
        db_session: Session,
        client_id: str | UUID | None = None,
        include_global: bool = True,
    ) -> list[dict[str, Any]]:
        """Get all coatings for a client."""
        return ReferenceDataService.get_reference_data(db_session, client_id, "coating", include_global=include_global)

    @staticmethod
    def get_forms(
        db_session: Session,
        client_id: str | UUID | None = None,
        include_global: bool = True,
    ) -> list[dict[str, Any]]:
        """Get all material forms for a client."""
        return ReferenceDataService.get_reference_data(db_session, client_id, "form", include_global=include_global)

    @staticmethod
    def get_finishes(
        db_session: Session,
        client_id: str | UUID | None = None,
        include_global: bool = True,
    ) -> list[dict[str, Any]]:
        """Get all surface finishes for a client."""
        return ReferenceDataService.get_reference_data(db_session, client_id, "finish", include_global=include_global)

    @staticmethod
    def get_certificates(
        db_session: Session,
        client_id: str | UUID | None = None,
        include_global: bool = True,
    ) -> list[dict[str, Any]]:
        """Get all certificates for a client."""
        return ReferenceDataService.get_reference_data(db_session, client_id, "certificate", include_global=include_global)

    @staticmethod
    def is_grade_coating_compatible(
        db_session: Session,
        grade_code: str,
        coating_code: str,
        client_id: str | UUID | None = None,
        include_global: bool = True,
    ) -> bool:
        """
        Check if a grade and coating are compatible.

        Args:
            db_session: Database session
            grade_code: Steel grade code
            coating_code: Coating code
            client_id: Client ID
            include_global: Whether to check global compatibility

        Returns:
            True if compatible, False otherwise
        """
        # Build client filter
        client_filters = []

        # Include Generic Template compatibility if include_global is True
        if include_global:
            client_filters.append(GradeCoatingCompatibility.client_id == UUID(GENERIC_CLIENT_ID))

        # Include client-specific compatibility if client_id is provided
        if client_id:
            client_uuid = UUID(client_id) if isinstance(client_id, str) else client_id
            client_filters.append(GradeCoatingCompatibility.client_id == client_uuid)

        if not client_filters:
            return False

        # Query for compatibility
        compatibility = (
            db_session.query(GradeCoatingCompatibility)
            .filter(
                and_(
                    GradeCoatingCompatibility.grade_code == grade_code,
                    GradeCoatingCompatibility.coating_code == coating_code,
                    or_(*client_filters),
                )
            )
            .first()
        )

        return compatibility is not None

    @staticmethod
    def get_compatible_coatings(
        db_session: Session,
        grade_code: str,
        client_id: str | UUID | None = None,
        include_global: bool = True,
    ) -> list[str]:
        """
        Get all coatings compatible with a specific grade.

        Args:
            db_session: Database session
            grade_code: Steel grade code
            client_id: Client ID
            include_global: Whether to include global compatibility

        Returns:
            List of compatible coating codes
        """
        # Build client filter
        client_filters = []

        if include_global:
            client_filters.append(GradeCoatingCompatibility.client_id == UUID(GENERIC_CLIENT_ID))

        if client_id:
            client_uuid = UUID(client_id) if isinstance(client_id, str) else client_id
            client_filters.append(GradeCoatingCompatibility.client_id == client_uuid)

        if not client_filters:
            return []

        # Query for compatible coatings
        compatibilities = (
            db_session.query(GradeCoatingCompatibility.coating_code)
            .filter(
                and_(
                    GradeCoatingCompatibility.grade_code == grade_code,
                    or_(*client_filters),
                )
            )
            .distinct()
            .all()
        )

        return [comp.coating_code for comp in compatibilities]

    @staticmethod
    def validate_material_specification(
        db_session: Session,
        grade_code: str,
        coating_code: str | None = None,
        form_code: str | None = None,
        client_id: str | UUID | None = None,
    ) -> dict[str, Any]:
        """
        Validate a material specification against reference data.

        Args:
            db_session: Database session
            grade_code: Steel grade code
            coating_code: Coating code (optional)
            form_code: Material form code (optional)
            client_id: Client ID

        Returns:
            Validation result with details
        """
        result = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "grade_found": False,
            "coating_found": False,
            "form_found": False,
            "grade_coating_compatible": False,
        }

        # Validate grade
        grades = ReferenceDataService.get_reference_data(db_session, client_id, "grade", code=grade_code)
        if grades:
            cast(dict[str, Any], result)["grade_found"] = True
        else:
            cast(dict[str, Any], result)["valid"] = False
            cast(list[str], result["errors"]).append(f"Grade '{grade_code}' not found in reference data")

        # Validate coating if provided
        if coating_code:
            coatings = ReferenceDataService.get_reference_data(db_session, client_id, "coating", code=coating_code)
            if coatings:
                cast(dict[str, Any], result)["coating_found"] = True

                # Check grade-coating compatibility
                if result["grade_found"]:
                    compatible = ReferenceDataService.is_grade_coating_compatible(db_session, grade_code, coating_code, client_id)
                    cast(dict[str, Any], result)["grade_coating_compatible"] = compatible
                    if not compatible:
                        cast(list[str], result["warnings"]).append(f"Grade '{grade_code}' and coating '{coating_code}' compatibility not verified")
            else:
                cast(dict[str, Any], result)["valid"] = False
                cast(list[str], result["errors"]).append(f"Coating '{coating_code}' not found in reference data")

        # Validate form if provided
        if form_code:
            forms = ReferenceDataService.get_reference_data(db_session, client_id, "form", code=form_code)
            if forms:
                cast(dict[str, Any], result)["form_found"] = True
            else:
                cast(list[str], result["warnings"]).append(f"Form '{form_code}' not found in reference data")

        return result


# Convenience functions for backward compatibility
def get_client_grades(db_session: Session, client_id: str | UUID) -> list[dict[str, Any]]:
    """Backward compatibility function."""
    return ReferenceDataService.get_grades(db_session, client_id)


def get_client_coatings(db_session: Session, client_id: str | UUID) -> list[dict[str, Any]]:
    """Backward compatibility function."""
    return ReferenceDataService.get_coatings(db_session, client_id)


def validate_grade_coating_compatibility(db_session: Session, grade_code: str, coating_code: str, client_id: str | UUID) -> bool:
    """Backward compatibility function."""
    return ReferenceDataService.is_grade_coating_compatible(db_session, grade_code, coating_code, client_id)
