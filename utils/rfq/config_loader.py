"""
Configuration loader utility that demonstrates the new JSON versioning system.

This module provides convenient functions to load client configurations
with the new versioning system, ensuring that schemas are loaded from
the generic client (default client UUID) as specified.
"""

from typing import Any
from uuid import UUID

from sqlalchemy.orm import Session

from utils.rfq.client_config_service import ClientConfigurationService
from utils.rfq.config import get_default_client_id


def load_client_config_with_schemas(db_session: Session, client_id: str | UUID, components: list[str] | None = None) -> dict[str, Any]:
    """
    Load client configuration with schemas, ensuring generic schemas are loaded
    from the default client UUID.

    Args:
        db_session: Database session
        client_id: Client ID to load configuration for
        components: Optional list of components to load (extraction, validation, normalizer, formatter)
                   If None, loads all components

    Returns:
        Dictionary containing client configuration and resolved schemas
    """
    # Load the full configuration
    config = ClientConfigurationService.get_client_configuration(db_session, client_id, load_schemas=True)

    # Filter components if specified
    if components:
        filtered_schemas = {}
        for component in components:
            if component in config["schemas"]:
                filtered_schemas[component] = config["schemas"][component]
        config["schemas"] = filtered_schemas

    return config


def get_active_schema_content(db_session: Session, client_id: str | UUID, component: str, schema_type: str) -> dict[str, Any] | None:
    """
    Get the content of a specific active schema.

    Args:
        db_session: Database session
        client_id: Client ID
        component: Component name (extraction, validation, normalizer, formatter)
        schema_type: Schema type (output_schema, rules, configuration)

    Returns:
        Schema content dictionary or None if not found
    """
    config = ClientConfigurationService.get_client_configuration(db_session, client_id, load_schemas=True)

    if component in config["schemas"] and schema_type in config["schemas"][component]:
        schema_info = config["schemas"][component][schema_type]
        if schema_info:
            return schema_info["content"]

    return None


def get_formatter_configuration(db_session: Session, client_id: str | UUID) -> dict[str, Any] | None:
    """
    Get the active formatter configuration for a client.

    This is a convenience function that specifically loads the formatter
    configuration from the generic client (default client UUID).

    Args:
        db_session: Database session
        client_id: Client ID

    Returns:
        Formatter configuration content or None if not found
    """
    return get_active_schema_content(db_session, client_id, "formatter", "configuration")


def get_extraction_rules(db_session: Session, client_id: str | UUID) -> dict[str, Any] | None:
    """
    Get the active extraction rules for a client.

    Args:
        db_session: Database session
        client_id: Client ID

    Returns:
        Extraction rules content or None if not found
    """
    return get_active_schema_content(db_session, client_id, "extraction", "rules")


def get_validation_schema(db_session: Session, client_id: str | UUID) -> dict[str, Any] | None:
    """
    Get the active validation output schema for a client.

    Args:
        db_session: Database session
        client_id: Client ID

    Returns:
        Validation output schema content or None if not found
    """
    return get_active_schema_content(db_session, client_id, "validation", "output_schema")


def get_normalizer_rules(db_session: Session, client_id: str | UUID) -> dict[str, Any] | None:
    """
    Get the active normalizer rules for a client.

    Args:
        db_session: Database session
        client_id: Client ID

    Returns:
        Normalizer rules content or None if not found
    """
    return get_active_schema_content(db_session, client_id, "normalizer", "rules")


def update_component_version(db_session: Session, client_id: str | UUID, component: str, schema_type: str, new_version: str) -> bool:
    """
    Update the active version for a specific component and schema type.

    Args:
        db_session: Database session
        client_id: Client ID
        component: Component name (extraction, validation, normalizer, formatter)
        schema_type: Schema type (output_schema, rules, configuration)
        new_version: New version to set as active

    Returns:
        True if update was successful, False otherwise
    """
    return ClientConfigurationService.update_active_version(db_session, client_id, component, schema_type, new_version)


def get_all_component_versions(db_session: Session, client_id: str | UUID) -> dict[str, dict[str, list[str]]]:
    """
    Get all available versions for all components and schema types.

    Args:
        db_session: Database session
        client_id: Client ID

    Returns:
        Nested dictionary with structure:
        {
            "extraction": {"output_schema": ["1.0.0", "1.1.0"], "rules": ["1.0.0"]},
            "validation": {"output_schema": ["1.0.0"], "rules": ["1.0.0"]},
            ...
        }
    """
    components = ["extraction", "validation", "normalizer", "formatter"]
    schema_types = {
        "extraction": ["output_schema", "rules"],
        "validation": ["output_schema", "rules"],
        "normalizer": ["output_schema", "rules"],
        "formatter": ["output_schema", "rules", "configuration"],
    }

    result: dict[str, dict[str, list[str]]] = {}
    for component in components:
        result[component] = {}
        for schema_type in schema_types[component]:
            versions = ClientConfigurationService.get_available_versions(db_session, client_id, component, schema_type)
            result[component][schema_type] = versions

    return result


def ensure_default_client_schemas_loaded(db_session: Session, client_id: str | UUID) -> dict[str, Any]:
    """
    Load schemas with proper client preference logic.

    This function loads schemas according to the new rules:
    1. All agent outputs (extraction, validation, normalizer) from generic account
    2. Formatter schemas based on client's has_output_preference flag:
       - If has_output_preference = True: load from specific client
       - If has_output_preference = False: load from generic account

    Args:
        db_session: Database session
        client_id: Client ID to load configuration for

    Returns:
        Configuration with schemas loaded according to client preference rules
    """
    # Load configuration using the client preference logic
    config = ClientConfigurationService.get_client_configuration(db_session, client_id, load_schemas=True)

    # Add summary information about loading behavior
    config["schema_loading_summary"] = {
        "agent_outputs_source": "generic_account",
        "formatter_source": "client_specific" if _client_has_output_preference(db_session, client_id) else "generic_account",
        "instructions_source": "client_specific" if _client_has_instruction(db_session, client_id) else "generic_account",
        "generic_client_id": get_default_client_id(),
    }

    return config


def _client_has_output_preference(db_session: Session, client_id: str | UUID) -> bool:
    """Helper function to check if client has output preference enabled."""
    from uuid import UUID

    from db.tables.rfq_tables import RFQClient

    client_uuid = UUID(client_id) if isinstance(client_id, str) else client_id

    client = db_session.query(RFQClient).filter_by(client_id=client_uuid).first()
    return client.has_output_preference if client else False


def _client_has_instruction(db_session: Session, client_id: str | UUID) -> bool:
    """Helper function to check if client has instruction enabled."""
    from db.tables.rfq_tables import RFQClient

    client_uuid = UUID(client_id) if isinstance(client_id, str) else client_id

    client = db_session.query(RFQClient).filter_by(client_id=client_uuid).first()
    return client.has_instruction if client else False
