"""
Standardized Logging Utilities for Vanilla Steel AI.

This module provides consistent, performance-optimized logging utilities
with environment-aware configuration and structured logging patterns.

Features:
- Performance-optimized logging (standard logging in production, Rich in dev)
- Consistent log levels and formatting
- Structured logging utilities for common operations
- Environment-aware configuration
- Lazy string formatting for better performance
"""

import logging
import os
import sys
from typing import Any

from rich.logging import Rich<PERSON><PERSON><PERSON>


def get_logger(logger_name: str, level: str | None = None) -> logging.Logger:
    """
    Get a configured logger with optimized formatting for performance.

    Args:
        logger_name: Name of the logger
        level: Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)

    Returns:
        logging.Logger: Configured logger instance

    Performance Notes:
        - Uses standard logging in production for better performance
        - Rich formatting only in development
        - Optimized log levels to reduce overhead
    """
    # Determine log level from environment or parameter
    if level is None:
        runtime_env = os.getenv("RUNTIME_ENV", "dev")
        # More conservative defaults for better performance
        level = os.getenv("LOG_LEVEL", "WARNING").upper() if runtime_env == "prd" else os.getenv("LOG_LEVEL", "INFO").upper()

    # Performance optimization: Use Rich only in development
    runtime_env = os.getenv("RUNTIME_ENV", "dev")

    _logger = logging.getLogger(logger_name)

    # Clear existing handlers to avoid duplicates
    _logger.handlers.clear()

    if runtime_env == "prd":
        # Production: Use standard logging for better performance
        handler = logging.StreamHandler(sys.stdout)
        formatter = logging.Formatter(
            fmt="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S",
        )
        handler.setFormatter(formatter)
    else:
        # Development: Use Rich formatting for better UX
        handler = RichHandler(
            show_time=True,
            rich_tracebacks=True,
            show_path=True,
            tracebacks_show_locals=False,
        )
        formatter = logging.Formatter(
            fmt="%(message)s",
            datefmt="[%X]",
        )
        handler.setFormatter(formatter)

    _logger.addHandler(handler)
    _logger.setLevel(getattr(logging, level, logging.INFO))
    _logger.propagate = False

    return _logger


def configure_root_logging() -> None:
    """
    Configure root logging for the application.

    This sets up global logging configuration including:
    - Log levels based on environment
    - Output formatting
    - Error handling
    """
    runtime_env = os.getenv("RUNTIME_ENV", "dev")
    log_level = os.getenv("LOG_LEVEL", "INFO" if runtime_env == "prd" else "DEBUG")

    # Configure root logger
    logging.basicConfig(
        level=getattr(logging, log_level.upper(), logging.INFO),
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[logging.StreamHandler(sys.stdout)],
    )

    # Reduce noise from third-party libraries
    logging.getLogger("uvicorn").setLevel(logging.WARNING)
    logging.getLogger("fastapi").setLevel(logging.WARNING)
    logging.getLogger("sqlalchemy").setLevel(logging.WARNING)
    logging.getLogger("openai").setLevel(logging.WARNING)
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("httpcore").setLevel(logging.WARNING)
    logging.getLogger("tools.rfq.database_tools").setLevel(logging.WARNING)

    # In test environment, reduce all logging
    if os.getenv("TESTING") == "True":
        logging.getLogger().setLevel(logging.ERROR)


# Initialize root logging
configure_root_logging()


def get_app_logger() -> logging.Logger:
    """Get the main application logger instance."""
    return get_logger("vanilla-steel-ai")


# Performance-optimized logging utilities
def log_api_request(method: str, path: str, client_ip: str = "unknown") -> None:
    """
    Log an API request in a structured format.

    Args:
        method: HTTP method
        path: Request path
        client_ip: Client IP address
    """
    logger = get_app_logger()
    # Use lazy formatting for better performance
    logger.info("API Request: %s %s from %s", method, path, client_ip)


def log_api_response(method: str, path: str, status_code: int, duration_ms: float) -> None:
    """
    Log an API response in a structured format.

    Args:
        method: HTTP method
        path: Request path
        status_code: HTTP status code
        duration_ms: Request duration in milliseconds
    """
    logger = get_app_logger()
    # Use lazy formatting for better performance
    logger.info("API Response: %s %s -> %d (%.2fms)", method, path, status_code, duration_ms)


def log_database_operation(operation: str, table: str, duration_ms: float, success: bool = True) -> None:
    """
    Log a database operation in a structured format.

    Args:
        operation: Database operation (SELECT, INSERT, UPDATE, DELETE)
        table: Database table name
        duration_ms: Operation duration in milliseconds
        success: Whether the operation was successful
    """
    status = "SUCCESS" if success else "FAILED"
    logger = get_app_logger()
    # Use lazy formatting for better performance
    logger.info("DB Operation: %s on %s -> %s (%.2fms)", operation, table, status, duration_ms)


def log_error_with_context(error: Exception, context: dict | None = None) -> None:
    """
    Log an error with additional context information.

    Args:
        error: Exception that occurred
        context: Additional context information
    """
    logger = get_app_logger()
    if context:
        context_items = [f"{k}={v}" for k, v in context.items()]
        context_str = f" Context: {', '.join(context_items)}"
        logger.error("Error: %s: %s%s", type(error).__name__, str(error), context_str)
    else:
        logger.error("Error: %s: %s", type(error).__name__, str(error))


# Standardized logging patterns for common operations
def log_agent_initialization(agent_type: str, model_id: str, user_id: str | None, duration_ms: float) -> None:
    """Log agent initialization with consistent format."""
    logger = get_app_logger()
    logger.info("Agent initialized: %s (model=%s, user=%s, duration=%.2fms)", agent_type, model_id, user_id or "none", duration_ms)


def log_processing_phase(phase: str, duration_s: float, status: str = "completed") -> None:
    """Log processing phase completion with consistent format."""
    logger = get_app_logger()
    logger.info("Phase %s: %s (%.2fs)", phase, status, duration_s)


def log_performance_metrics(operation: str, metrics: dict[str, Any]) -> None:
    """Log performance metrics with consistent format."""
    logger = get_app_logger()
    metrics_str = ", ".join(f"{k}={v}" for k, v in metrics.items())
    logger.info("Performance [%s]: %s", operation, metrics_str)


def log_cache_operation(operation: str, key: str, hit: bool = True) -> None:
    """Log cache operations with consistent format."""
    logger = get_app_logger()
    status = "HIT" if hit else "MISS"
    logger.debug("Cache %s: %s -> %s", operation, key, status)
