"""
Advanced caching system for improved performance.
"""

import asyncio
import hashlib
import pickle
import threading
import time
from collections import OrderedDict
from collections.abc import Callable
from dataclasses import dataclass, field
from functools import wraps
from typing import Any

from utils.log import get_app_logger

logger = get_app_logger()


@dataclass
class CacheEntry:
    """Individual cache entry with metadata."""

    value: Any
    created_at: float = field(default_factory=time.time)
    last_accessed: float = field(default_factory=time.time)
    access_count: int = 0
    size_bytes: int = 0
    tags: set[str] = field(default_factory=set)


class CacheManager:
    """
    Advanced caching system with multiple eviction policies and performance monitoring.

    Features:
    - Multiple eviction policies (LRU, TTL, size-based)
    - Tag-based cache invalidation
    - Memory usage monitoring
    - Cache hit/miss statistics
    - Async-safe operations
    - Configurable cache sizes per namespace
    """

    def __init__(
        self,
        max_size: int = 1000,
        max_memory_mb: float = 100.0,
        default_ttl: float = 3600.0,  # 1 hour default TTL
        cleanup_interval: float = 300.0,  # 5 minutes
    ):
        """
        Initialize cache manager.

        Args:
            max_size: Maximum number of cache entries
            max_memory_mb: Maximum memory usage in MB
            default_ttl: Default time-to-live in seconds
            cleanup_interval: Interval for cleanup operations
        """
        self.max_size = max_size
        self.max_memory_bytes = max_memory_mb * 1024 * 1024
        self.default_ttl = default_ttl
        self.cleanup_interval = cleanup_interval

        # Cache storage organized by namespace
        self.caches: dict[str, OrderedDict[str, CacheEntry]] = {}
        self.locks: dict[str, threading.RLock] = {}

        # Statistics
        self.stats = {
            "hits": 0,
            "misses": 0,
            "evictions": 0,
            "total_size_bytes": 0,
            "total_entries": 0,
        }

        # Cleanup task
        self._cleanup_task: asyncio.Task | None = None
        self._running = False

    def _get_namespace_cache(self, namespace: str) -> OrderedDict[str, CacheEntry]:
        """Get or create cache for namespace."""
        if namespace not in self.caches:
            self.caches[namespace] = OrderedDict()
            self.locks[namespace] = threading.RLock()
        return self.caches[namespace]

    def _get_namespace_lock(self, namespace: str) -> threading.RLock:
        """Get lock for namespace."""
        if namespace not in self.locks:
            self.locks[namespace] = threading.RLock()
        return self.locks[namespace]

    def _calculate_size(self, value: Any) -> int:
        """Calculate approximate size of value in bytes."""
        try:
            return len(pickle.dumps(value, protocol=pickle.HIGHEST_PROTOCOL))
        except Exception:
            # Fallback for non-serializable objects
            return len(str(value).encode("utf-8"))

    def _make_key(self, key: str | tuple) -> str:
        """Create cache key from various input types."""
        if isinstance(key, str):
            return key
        elif isinstance(key, tuple):
            # Hash tuples for consistent keys
            key_str = str(key)
            return hashlib.md5(key_str.encode()).hexdigest()
        else:
            return str(key)

    def _is_expired(self, entry: CacheEntry, ttl: float | None = None) -> bool:
        """Check if cache entry is expired."""
        if ttl is None:
            ttl = self.default_ttl

        if ttl <= 0:  # No expiration
            return False

        return (time.time() - entry.created_at) > ttl

    def _evict_lru(self, namespace: str, count: int = 1) -> int:
        """Evict least recently used entries."""
        cache = self._get_namespace_cache(namespace)
        evicted = 0

        # Sort by last_accessed time
        sorted_items = sorted(cache.items(), key=lambda x: x[1].last_accessed)

        for key, entry in sorted_items[:count]:
            if key in cache:
                self.stats["total_size_bytes"] -= entry.size_bytes
                del cache[key]
                evicted += 1
                self.stats["evictions"] += 1

        return evicted

    def _cleanup_expired(self, namespace: str) -> int:
        """Remove expired entries from namespace."""
        cache = self._get_namespace_cache(namespace)
        expired_keys = []

        for key, entry in cache.items():
            if self._is_expired(entry):
                expired_keys.append(key)

        for key in expired_keys:
            entry = cache[key]
            self.stats["total_size_bytes"] -= entry.size_bytes
            del cache[key]
            self.stats["evictions"] += 1

        return len(expired_keys)

    def _enforce_size_limits(self, namespace: str) -> None:
        """Enforce cache size and memory limits."""
        cache = self._get_namespace_cache(namespace)

        # Check entry count limit
        while len(cache) > self.max_size:
            self._evict_lru(namespace, 1)

        # Check memory limit
        while self.stats["total_size_bytes"] > self.max_memory_bytes:
            if not self._evict_lru(namespace, 1):
                break  # No more entries to evict

    def get(self, key: str | tuple, namespace: str = "default", default: Any = None) -> Any:
        """
        Get value from cache.

        Args:
            key: Cache key
            namespace: Cache namespace
            default: Default value if not found

        Returns:
            Cached value or default
        """
        cache_key = self._make_key(key)
        cache = self._get_namespace_cache(namespace)
        lock = self._get_namespace_lock(namespace)

        with lock:
            if cache_key in cache:
                entry = cache[cache_key]

                # Check if expired
                if self._is_expired(entry):
                    self.stats["total_size_bytes"] -= entry.size_bytes
                    del cache[cache_key]
                    self.stats["misses"] += 1
                    return default

                # Update access stats
                entry.last_accessed = time.time()
                entry.access_count += 1

                # Move to end (most recently used)
                cache.move_to_end(cache_key)

                self.stats["hits"] += 1
                return entry.value
            else:
                self.stats["misses"] += 1
                return default

    def set(
        self,
        key: str | tuple,
        value: Any,
        namespace: str = "default",
        ttl: float | None = None,
        tags: set[str] | None = None,
    ) -> None:
        """
        Set value in cache.

        Args:
            key: Cache key
            value: Value to cache
            namespace: Cache namespace
            ttl: Time-to-live override
            tags: Tags for invalidation
        """
        cache_key = self._make_key(key)
        cache = self._get_namespace_cache(namespace)
        lock = self._get_namespace_lock(namespace)

        with lock:
            # Calculate size
            size_bytes = self._calculate_size(value)

            # Remove old entry if exists
            if cache_key in cache:
                old_entry = cache[cache_key]
                self.stats["total_size_bytes"] -= old_entry.size_bytes

            # Create new entry
            entry = CacheEntry(
                value=value,
                size_bytes=size_bytes,
                tags=tags or set(),
            )

            # Add to cache
            cache[cache_key] = entry
            self.stats["total_size_bytes"] += size_bytes

            # Enforce limits
            self._enforce_size_limits(namespace)

            # Update total entries count
            self.stats["total_entries"] = sum(len(c) for c in self.caches.values())

    def delete(self, key: str | tuple, namespace: str = "default") -> bool:
        """
        Delete entry from cache.

        Args:
            key: Cache key
            namespace: Cache namespace

        Returns:
            True if entry was deleted
        """
        cache_key = self._make_key(key)
        cache = self._get_namespace_cache(namespace)
        lock = self._get_namespace_lock(namespace)

        with lock:
            if cache_key in cache:
                entry = cache[cache_key]
                self.stats["total_size_bytes"] -= entry.size_bytes
                del cache[cache_key]
                self.stats["total_entries"] -= 1
                return True
            return False

    def get_stats(self, namespace: str | None = None) -> dict[str, Any]:
        """
        Get cache statistics.

        Args:
            namespace: Specific namespace stats or all if None

        Returns:
            Dictionary with cache statistics
        """
        total_hits = self.stats["hits"]
        total_misses = self.stats["misses"]
        total_requests = total_hits + total_misses

        hit_rate = (total_hits / total_requests * 100) if total_requests > 0 else 0

        base_stats = {
            "hit_rate": f"{hit_rate:.2f}%",
            "total_requests": total_requests,
            "hits": total_hits,
            "misses": total_misses,
            "evictions": self.stats["evictions"],
            "total_entries": self.stats["total_entries"],
            "total_size_mb": f"{self.stats['total_size_bytes'] / (1024 * 1024):.2f}",
            "memory_utilization": f"{(self.stats['total_size_bytes'] / self.max_memory_bytes * 100):.1f}%",
        }

        if namespace:
            if namespace in self.caches:
                cache = self.caches[namespace]
                namespace_stats = {
                    "namespace": namespace,
                    "entries": len(cache),
                    "size_bytes": sum(entry.size_bytes for entry in cache.values()),
                }
                base_stats.update(namespace_stats)
            else:
                base_stats["namespace"] = f"{namespace} (not found)"
        else:
            # Add per-namespace breakdown
            namespaces = {}
            for ns, cache in self.caches.items():
                namespaces[ns] = {
                    "entries": len(cache),
                    "size_bytes": sum(entry.size_bytes for entry in cache.values()),
                }
            base_stats["namespaces"] = namespaces

        return base_stats


def cached(
    ttl: float = 3600.0,
    namespace: str = "default",
    tags: set[str] | None = None,
    key_func: Callable | None = None,
):
    """Decorator for caching function results."""

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            # Generate cache key
            cache_key = key_func(*args, **kwargs) if key_func else (func.__name__, args, tuple(sorted(kwargs.items())))

            # Try to get from cache
            result = cache_manager.get(cache_key, namespace)
            if result is not None:
                return result

            # Execute function and cache result
            result = await func(*args, **kwargs)
            cache_manager.set(cache_key, result, namespace, ttl, tags)

            return result

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            # Generate cache key
            cache_key = key_func(*args, **kwargs) if key_func else (func.__name__, args, tuple(sorted(kwargs.items())))

            # Try to get from cache
            result = cache_manager.get(cache_key, namespace)
            if result is not None:
                return result

            # Execute function and cache result
            result = func(*args, **kwargs)
            cache_manager.set(cache_key, result, namespace, ttl, tags)

            return result

        # Return appropriate wrapper
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator


# Global cache manager instance
cache_manager = CacheManager()
