"""
Performance metrics collection and monitoring.
"""

import asyncio
import threading
import time
from collections.abc import Callable
from contextlib import asynccontextmanager
from dataclasses import dataclass, field
from functools import wraps
from typing import Any

import psutil

from utils.log import get_app_logger

logger = get_app_logger()


@dataclass
class PerformanceMetric:
    """Individual performance metric data."""

    name: str
    value: float
    unit: str
    timestamp: float = field(default_factory=time.time)
    metadata: dict[str, Any] = field(default_factory=dict)


@dataclass
class AgentPerformance:
    """Performance data for a specific agent."""

    agent_type: str
    execution_time: float
    memory_usage: float
    token_count: int
    success: bool
    error_message: str | None = None
    timestamp: float = field(default_factory=time.time)


class MetricsCollector:
    """
    Collects and analyzes performance metrics for RFQ processing.

    Features:
    - Real-time performance monitoring
    - Agent-specific metrics tracking
    - System resource monitoring
    - Automatic metric aggregation
    - Performance alerts and thresholds
    """

    def __init__(self, max_metrics: int = 10000):
        """
        Initialize metrics collector.

        Args:
            max_metrics: Maximum number of metrics to store in memory
        """
        self.max_metrics = max_metrics
        self.metrics: list[PerformanceMetric] = []
        self.agent_metrics: list[AgentPerformance] = []
        self.lock = threading.RLock()

        # Performance thresholds
        self.thresholds = {
            "agent_execution_time": 30.0,  # seconds
            "memory_usage_mb": 1000.0,  # MB
            "token_rate": 1000.0,  # tokens/second
            "error_rate": 0.05,  # 5%
        }

        # Aggregated statistics
        self.stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "average_execution_time": 0.0,
            "average_memory_usage": 0.0,
            "peak_memory_usage": 0.0,
            "total_tokens_processed": 0,
        }

        # System metrics
        self.process = psutil.Process()
        self.start_time = time.time()

    def record_metric(self, name: str, value: float, unit: str, **metadata) -> None:
        """
        Record a performance metric.

        Args:
            name: Metric name
            value: Metric value
            unit: Unit of measurement
            **metadata: Additional metadata
        """
        with self.lock:
            metric = PerformanceMetric(name=name, value=value, unit=unit, metadata=metadata)

            self.metrics.append(metric)

            # Trim metrics if we exceed max_metrics
            if len(self.metrics) > self.max_metrics:
                self.metrics = self.metrics[-self.max_metrics :]

            # Check thresholds
            self._check_threshold(name, value)

    def record_agent_performance(
        self, agent_type: str, execution_time: float, token_count: int, success: bool, error_message: str | None = None, **metadata
    ) -> None:
        """
        Record agent-specific performance data.

        Args:
            agent_type: Type of agent (extraction, validation, etc.)
            execution_time: Execution time in seconds
            token_count: Number of tokens processed
            success: Whether the operation was successful
            error_message: Error message if failed
            **metadata: Additional metadata
        """
        # Get current memory usage
        memory_info = self.process.memory_info()
        memory_usage_mb = memory_info.rss / 1024 / 1024

        with self.lock:
            agent_perf = AgentPerformance(
                agent_type=agent_type,
                execution_time=execution_time,
                memory_usage=memory_usage_mb,
                token_count=token_count,
                success=success,
                error_message=error_message,
            )

            self.agent_metrics.append(agent_perf)

            # Update aggregated statistics
            self._update_stats(agent_perf)

            # Trim agent metrics if needed
            if len(self.agent_metrics) > self.max_metrics:
                self.agent_metrics = self.agent_metrics[-self.max_metrics :]

            # Record individual metrics
            self.record_metric(f"{agent_type}_execution_time", execution_time, "seconds")
            self.record_metric(f"{agent_type}_memory_usage", memory_usage_mb, "MB")
            self.record_metric(f"{agent_type}_token_count", token_count, "tokens")

    def _update_stats(self, agent_perf: AgentPerformance) -> None:
        """Update aggregated statistics."""
        self.stats["total_requests"] += 1

        if agent_perf.success:
            self.stats["successful_requests"] += 1
        else:
            self.stats["failed_requests"] += 1

        # Update averages
        total_requests = self.stats["total_requests"]

        # Execution time average
        current_avg_time = self.stats["average_execution_time"]
        self.stats["average_execution_time"] = (current_avg_time * (total_requests - 1) + agent_perf.execution_time) / total_requests

        # Memory usage average
        current_avg_memory = self.stats["average_memory_usage"]
        self.stats["average_memory_usage"] = (current_avg_memory * (total_requests - 1) + agent_perf.memory_usage) / total_requests

        # Peak memory usage
        if agent_perf.memory_usage > self.stats["peak_memory_usage"]:
            self.stats["peak_memory_usage"] = agent_perf.memory_usage

        # Total tokens
        self.stats["total_tokens_processed"] += agent_perf.token_count

    def _check_threshold(self, metric_name: str, value: float) -> None:
        """Check if metric exceeds threshold and log warning."""
        threshold_key = None

        if "execution_time" in metric_name:
            threshold_key = "agent_execution_time"
        elif "memory_usage" in metric_name:
            threshold_key = "memory_usage_mb"

        if threshold_key and threshold_key in self.thresholds:
            threshold = self.thresholds[threshold_key]
            if value > threshold:
                logger.warning(f"Performance threshold exceeded: {metric_name}={value:.2f} > {threshold}")

    def get_system_metrics(self) -> dict[str, Any]:
        """Get current system performance metrics."""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)

            # Memory info
            memory_info = self.process.memory_info()
            system_memory = psutil.virtual_memory()

            # Disk I/O
            disk_io = psutil.disk_io_counters()

            # Network I/O
            network_io = psutil.net_io_counters()

            return {
                "cpu_percent": cpu_percent,
                "memory_usage_mb": memory_info.rss / 1024 / 1024,
                "memory_percent": system_memory.percent,
                "disk_read_mb": disk_io.read_bytes / 1024 / 1024 if disk_io else 0,
                "disk_write_mb": disk_io.write_bytes / 1024 / 1024 if disk_io else 0,
                "network_sent_mb": network_io.bytes_sent / 1024 / 1024 if network_io else 0,
                "network_recv_mb": network_io.bytes_recv / 1024 / 1024 if network_io else 0,
                "uptime_seconds": time.time() - self.start_time,
            }
        except Exception as e:
            logger.error(f"Error getting system metrics: {e}")
            return {"error": str(e)}

    def get_agent_statistics(self, agent_type: str | None = None) -> dict[str, Any]:
        """Get statistics for specific agent type or all agents."""
        with self.lock:
            agent_metrics = [m for m in self.agent_metrics if m.agent_type == agent_type] if agent_type else self.agent_metrics

            if not agent_metrics:
                return {"message": f"No metrics found for agent: {agent_type or 'any'}"}

            # Calculate statistics
            execution_times = [m.execution_time for m in agent_metrics]
            memory_usages = [m.memory_usage for m in agent_metrics]
            token_counts = [m.token_count for m in agent_metrics]
            success_count = sum(1 for m in agent_metrics if m.success)

            return {
                "agent_type": agent_type or "all",
                "total_requests": len(agent_metrics),
                "success_rate": f"{(success_count / len(agent_metrics)) * 100:.2f}%",
                "avg_execution_time": f"{sum(execution_times) / len(execution_times):.3f}s",
                "max_execution_time": f"{max(execution_times):.3f}s",
                "min_execution_time": f"{min(execution_times):.3f}s",
                "avg_memory_usage": f"{sum(memory_usages) / len(memory_usages):.2f}MB",
                "max_memory_usage": f"{max(memory_usages):.2f}MB",
                "total_tokens": sum(token_counts),
                "avg_tokens_per_request": f"{sum(token_counts) / len(token_counts):.0f}",
            }

    def get_performance_summary(self) -> dict[str, Any]:
        """Get comprehensive performance summary."""
        with self.lock:
            system_metrics = self.get_system_metrics()

            # Calculate error rate
            total_requests = self.stats["total_requests"]
            error_rate = (self.stats["failed_requests"] / total_requests) * 100 if total_requests > 0 else 0

            # Calculate token processing rate
            uptime = time.time() - self.start_time
            token_rate = self.stats["total_tokens_processed"] / uptime if uptime > 0 else 0

            return {
                "overall_stats": {
                    "total_requests": total_requests,
                    "success_rate": f"{(self.stats['successful_requests'] / total_requests * 100):.2f}%" if total_requests > 0 else "0%",
                    "error_rate": f"{error_rate:.2f}%",
                    "avg_execution_time": f"{self.stats['average_execution_time']:.3f}s",
                    "avg_memory_usage": f"{self.stats['average_memory_usage']:.2f}MB",
                    "peak_memory_usage": f"{self.stats['peak_memory_usage']:.2f}MB",
                    "token_processing_rate": f"{token_rate:.1f} tokens/sec",
                    "uptime": f"{uptime:.1f}s",
                },
                "system_metrics": system_metrics,
                "agent_breakdown": {
                    agent_type: self.get_agent_statistics(agent_type) for agent_type in set(m.agent_type for m in self.agent_metrics)
                },
                "thresholds": self.thresholds,
            }

    def reset_metrics(self) -> None:
        """Reset all collected metrics."""
        with self.lock:
            self.metrics.clear()
            self.agent_metrics.clear()
            self.stats = {
                "total_requests": 0,
                "successful_requests": 0,
                "failed_requests": 0,
                "average_execution_time": 0.0,
                "average_memory_usage": 0.0,
                "peak_memory_usage": 0.0,
                "total_tokens_processed": 0,
            }
            self.start_time = time.time()
            logger.info("Performance metrics reset")


def performance_monitor(
    agent_type: str,
    track_tokens: bool = True,
    track_memory: bool = True,
):
    """Decorator for automatic performance monitoring of agent functions."""

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()

            try:
                result = await func(*args, **kwargs)

                # Extract token count from result if possible
                token_count = 0
                if track_tokens and isinstance(result, dict):
                    # Try to extract token usage from common response formats
                    if "usage" in result and "total_tokens" in result["usage"]:
                        token_count = result["usage"]["total_tokens"]
                    elif "token_count" in result:
                        token_count = result["token_count"]

                execution_time = time.time() - start_time

                # Record performance
                metrics_collector.record_agent_performance(
                    agent_type=agent_type,
                    execution_time=execution_time,
                    token_count=token_count,
                    success=True,
                )

                return result

            except Exception as e:
                execution_time = time.time() - start_time

                # Record failed performance
                metrics_collector.record_agent_performance(
                    agent_type=agent_type,
                    execution_time=execution_time,
                    token_count=0,
                    success=False,
                    error_message=str(e),
                )

                raise

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()

            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time

                # Record performance
                metrics_collector.record_agent_performance(
                    agent_type=agent_type,
                    execution_time=execution_time,
                    token_count=0,  # Sync functions typically don't process tokens
                    success=True,
                )

                return result

            except Exception as e:
                execution_time = time.time() - start_time

                # Record failed performance
                metrics_collector.record_agent_performance(
                    agent_type=agent_type,
                    execution_time=execution_time,
                    token_count=0,
                    success=False,
                    error_message=str(e),
                )

                raise

        # Return appropriate wrapper based on function type
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator


@asynccontextmanager
async def performance_context(operation_name: str):
    """Context manager for measuring performance of code blocks."""
    start_time = time.time()
    start_memory = psutil.Process().memory_info().rss / 1024 / 1024

    try:
        yield

        execution_time = time.time() - start_time
        end_memory = psutil.Process().memory_info().rss / 1024 / 1024
        memory_delta = end_memory - start_memory

        metrics_collector.record_metric(f"{operation_name}_execution_time", execution_time, "seconds")
        metrics_collector.record_metric(f"{operation_name}_memory_delta", memory_delta, "MB")

        logger.debug(f"{operation_name} completed in {execution_time:.3f}s, memory delta: {memory_delta:.2f}MB")

    except Exception as e:
        execution_time = time.time() - start_time
        metrics_collector.record_metric(f"{operation_name}_execution_time", execution_time, "seconds", error=str(e))
        raise


# Global metrics collector instance
metrics_collector = MetricsCollector()
