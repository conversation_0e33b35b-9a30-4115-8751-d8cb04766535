"""
Optimized RFQ Database Tools with async connection pooling and performance improvements.
"""

import json
from typing import Any

from utils.exceptions import ClientConfigurationError, DatabaseError
from utils.log import get_app_logger
from utils.performance import Async<PERSON><PERSON><PERSON><PERSON><PERSON>, CacheManager, cached, get_db_pool, performance_monitor
from utils.rfq.config import get_default_client_id

logger = get_app_logger()


class OptimizedRFQDatabaseTools:
    """
    High-performance RFQ database tools with async connection pooling.

    Features:
    - Async database connection pooling
    - Query result caching
    - Performance monitoring
    - Batch operations
    - Connection retry logic
    """

    def __init__(self, db_url: str):
        """
        Initialize optimized database tools.

        Args:
            db_url: Database connection URL
        """
        self.db_url = db_url
        self._pool: AsyncDatabasePool | None = None
        self.cache = CacheManager(
            max_size=500,
            max_memory_mb=50.0,
            default_ttl=300.0,  # 5 minutes for database queries
        )

    async def _get_pool(self) -> AsyncDatabasePool:
        """Get or create database pool."""
        if self._pool is None:
            self._pool = await get_db_pool(
                self.db_url,
                min_connections=5,
                max_connections=20,
                command_timeout=30.0,
                connection_timeout=10.0,
            )
        return self._pool

    @performance_monitor("database_query")
    @cached(ttl=300.0, namespace="client_config")
    async def get_conditional_client_configuration_async(self, client_id: str) -> dict[str, Any]:
        """
        Async version of get_conditional_client_configuration with caching.

        Args:
            client_id: Client ID to get configuration for

        Returns:
            Client configuration dictionary
        """
        try:
            pool = await self._get_pool()

            # Get client configuration with flags
            client_config, has_kb, has_instruction, has_output_preference = await self._get_client_configuration_with_flags_async(client_id)

            # Get generic configuration as fallback
            generic_config, _, _, _ = await self._get_client_configuration_with_flags_async(get_default_client_id())

            if not generic_config:
                raise ClientConfigurationError("Generic configuration not found in database")

            # Start with generic configuration
            merged_config = generic_config.copy()

            # Override with client-specific configurations if available
            if client_config:
                # Always use client-specific model preferences if available
                if client_config.get("model_preferences"):
                    merged_config["model_preferences"] = client_config["model_preferences"]

                # Always use client-specific normalization rules if available
                if client_config.get("normalization_rules"):
                    merged_config["normalization_rules"] = client_config["normalization_rules"]

                # Use client-specific instructions if has_instruction flag is True
                if has_instruction:
                    if client_config.get("extraction_rules"):
                        merged_config["extraction_rules"] = client_config["extraction_rules"]
                    if client_config.get("validation_rules"):
                        merged_config["validation_rules"] = client_config["validation_rules"]

                # Use client-specific output schemas if has_output_preference flag is True
                if has_output_preference:
                    if client_config.get("formatter_output_schema"):
                        merged_config["formatter_output_schema"] = client_config["formatter_output_schema"]

            # Add client flags to configuration
            merged_config["_client_flags"] = {
                "has_kb": has_kb,
                "has_instruction": has_instruction,
                "has_output_preference": has_output_preference,
                "client_id": str(client_id),
            }

            return merged_config

        except Exception as e:
            logger.error(f"Error in get_conditional_client_configuration_async: {e}")
            raise ClientConfigurationError(f"Failed to load conditional configuration for client {client_id}") from e

    async def _get_client_configuration_with_flags_async(self, client_id: str) -> tuple[dict[str, Any] | None, bool, bool, bool]:
        """Async version of get_client_configuration_with_flags."""
        try:
            pool = await self._get_pool()

            # Get client flags
            flags_query = """
            SELECT has_kb, has_instruction, has_output_preference
            FROM rfq_clients
            WHERE client_id = $1
            """

            flags_result = await pool.execute_query(flags_query, client_id, fetch_method="fetchone")

            if not flags_result:
                logger.warning(f"No client found: {client_id}")
                return None, False, False, False

            has_kb = bool(flags_result[0]) if flags_result[0] is not None else False
            has_instruction = bool(flags_result[1]) if flags_result[1] is not None else False
            has_output_preference = bool(flags_result[2]) if flags_result[2] is not None else False

            # Get configuration using versioned schema system
            config = await self._get_client_configuration_async(client_id)

            return config, has_kb, has_instruction, has_output_preference

        except Exception as e:
            logger.error(f"Error in _get_client_configuration_with_flags_async: {e}")
            raise DatabaseError(f"Failed to get client configuration with flags: {e}") from e

    @cached(ttl=300.0, namespace="client_config_detailed")
    async def _get_client_configuration_async(self, client_id: str) -> dict[str, Any] | None:
        """Async version of get_client_configuration."""
        try:
            pool = await self._get_pool()

            # Load all schemas and rules from versioned table
            config = {}

            schema_types = [
                "extraction_schema",
                "validation_schema",
                "normalizer_schema",
                "extraction_rules",
                "validation_rules",
                "normalizer_rules",
                "formatter_schema",
            ]

            # Batch load all schema types
            for schema_type in schema_types:
                schema_content = await self._get_versioned_schema_content_async(client_id, schema_type)

                # Map schema types to config keys
                config_key_map = {
                    "extraction_schema": "extraction_output_schema",
                    "validation_schema": "validation_output_schema",
                    "normalizer_schema": "normalizer_output_schema",
                    "formatter_schema": "formatter_output_schema",
                    "extraction_rules": "extraction_rules",
                    "validation_rules": "validation_rules",
                    "normalizer_rules": "normalizer_rules",
                }

                config_key = config_key_map.get(schema_type, schema_type)
                config[config_key] = schema_content

            # Load model preferences
            model_prefs_query = """
            SELECT model_preferences
            FROM rfq_client_configurations
            WHERE client_id = $1
            ORDER BY updated_at DESC
            LIMIT 1
            """

            result = await pool.execute_query(model_prefs_query, client_id, fetch_method="fetchval")

            if result:
                config["model_preferences"] = self._safe_json_parse(result, "model_preferences", client_id)

            return config if any(config.values()) else None

        except Exception as e:
            logger.error(f"Error in _get_client_configuration_async: {e}")
            raise DatabaseError(f"Failed to get client configuration: {e}") from e

    @cached(ttl=600.0, namespace="versioned_schema")  # 10 minutes for schema content
    async def _get_versioned_schema_content_async(self, client_id: str, schema_type: str, version: str | None = None) -> dict[str, Any] | None:
        """Async version of get_versioned_schema_content."""
        try:
            pool = await self._get_pool()

            if version:
                # Get specific version
                query = """
                SELECT content
                FROM rfq_instruction_schemas
                WHERE client_id = $1 AND type = $2 AND version = $3 AND is_active = true
                ORDER BY updated_at DESC
                LIMIT 1
                """
                result = await pool.execute_query(query, client_id, schema_type, version, fetch_method="fetchval")
            else:
                # Get latest version for this schema type
                query = """
                SELECT content
                FROM rfq_instruction_schemas
                WHERE client_id = $1 AND type = $2 AND is_active = true
                ORDER BY version DESC, updated_at DESC
                LIMIT 1
                """
                result = await pool.execute_query(query, client_id, schema_type, fetch_method="fetchval")

            if result:
                if isinstance(result, dict):
                    return result
                elif isinstance(result, str):
                    return self._safe_json_parse(result, schema_type, client_id)
                else:
                    logger.warning(f"Unexpected content type for {schema_type}: {type(result)}")
                    return None
            else:
                logger.debug(f"No {schema_type} found for client {client_id} version {version}")
                return None

        except Exception as e:
            logger.error(f"Error loading versioned schema {schema_type} for client {client_id}: {e}")
            return None

    @performance_monitor("database_query")
    @cached(ttl=300.0, namespace="agent_model_prefs")
    async def get_agent_model_preferences_async(self, client_id: str, agent_type: str) -> dict[str, Any]:
        """
        Async version of get_agent_model_preferences with caching.

        Args:
            client_id: Client ID
            agent_type: Agent type (extraction, validation, normalization, formatting)

        Returns:
            Model preferences dictionary
        """
        valid_agent_types = ["extraction", "validation", "normalizer", "formatter"]
        if agent_type not in valid_agent_types:
            raise ValueError(f"Invalid agent_type '{agent_type}'. Must be one of: {valid_agent_types}")

        try:
            # Default preferences based on agent type
            default_preferences = {
                "model_id": "google/gemini-flash-1.5",
                "temperature": 0.0 if agent_type in ["extraction", "normalizer", "formatter"] else 0.1,
                "max_tokens": {
                    "extraction": 6144,
                    "validation": 8192,
                    "normalizer": 4096,
                    "formatter": 4096,
                }.get(agent_type, 6144),
                "source": "default",
            }

            # Get client configuration with flags
            client_config, has_kb, has_instruction, has_output_preference = await self._get_client_configuration_with_flags_async(client_id)

            if not client_config or not client_config.get("model_preferences"):
                logger.info(f"Using default model preferences for {agent_type} agent (client: {client_id})")
                return default_preferences

            model_prefs = client_config["model_preferences"]
            agent_models = model_prefs.get("agent_models", {})
            model_id = agent_models.get(agent_type)

            if not model_id:
                model_id = model_prefs.get("default_model")
            if not model_id:
                model_id = default_preferences["model_id"]

            performance_settings = model_prefs.get("performance_settings", {})
            agent_settings = performance_settings.get(agent_type, {})

            final_preferences = {
                "model_id": model_id,
                "temperature": agent_settings.get("temperature", default_preferences["temperature"]),
                "max_tokens": agent_settings.get("max_tokens", default_preferences["max_tokens"]),
                "source": "client_specific" if model_id != default_preferences["model_id"] else "default",
                "client_id": str(client_id),
                "agent_type": agent_type,
            }

            return final_preferences

        except Exception as e:
            logger.error(f"Error loading model preferences for {agent_type} agent: {e}")
            return default_preferences

    @performance_monitor("database_batch_query")
    async def batch_get_client_configurations(self, client_ids: list[str]) -> dict[str, dict[str, Any]]:
        """
        Batch load configurations for multiple clients.

        Args:
            client_ids: List of client IDs

        Returns:
            Dictionary mapping client_id to configuration
        """
        try:
            pool = await self._get_pool()

            # Use batch query for better performance
            placeholders = ",".join(f"${i + 1}" for i in range(len(client_ids)))
            query = """
            SELECT client_id, has_kb, has_instruction, has_output_preference
            FROM rfq_clients
            WHERE client_id = ANY($1::uuid[])
            """

            results = await pool.execute_query(query, client_ids, fetch_method="fetchall")

            configurations = {}
            for row in results:
                client_id = str(row[0])
                has_kb = bool(row[1]) if row[1] is not None else False
                has_instruction = bool(row[2]) if row[2] is not None else False
                has_output_preference = bool(row[3]) if row[3] is not None else False

                # For now, store flags - full config loading would require more queries
                configurations[client_id] = {
                    "_client_flags": {
                        "has_kb": has_kb,
                        "has_instruction": has_instruction,
                        "has_output_preference": has_output_preference,
                        "client_id": client_id,
                    }
                }

            return configurations

        except Exception as e:
            logger.error(f"Error in batch_get_client_configurations: {e}")
            return {}

    def _safe_json_parse(self, json_str: Any, field_name: str, client_id: str = "unknown") -> dict[str, Any] | list[Any] | None:
        """Safely parse JSON string with comprehensive error handling."""
        if json_str is None:
            return None

        if isinstance(json_str, dict | list):
            return json_str

        if isinstance(json_str, str):
            if not json_str.strip():
                return None

            try:
                return json.loads(json_str)
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse JSON for {field_name} (client {client_id}): {e}")
                return None
        else:
            logger.warning(f"Unexpected type for {field_name} (client {client_id}): {type(json_str)}")
            return None

    async def get_performance_metrics(self) -> dict[str, Any]:
        """
        Get database performance metrics.

        Returns:
            Dictionary with performance metrics
        """
        metrics = {}

        # Pool metrics
        if self._pool:
            pool_status = await self._pool.get_pool_status()
            pool_metrics = self._pool.get_performance_metrics()
            metrics["database_pool"] = {**pool_status, **pool_metrics}

        # Cache metrics
        cache_stats = self.cache.get_stats()
        metrics["cache"] = cache_stats

        return metrics

    async def warm_cache(self, client_ids: list[str]) -> None:
        """
        Warm up the cache with configurations for specified clients.

        Args:
            client_ids: List of client IDs to warm cache for
        """
        logger.info(f"Warming cache for {len(client_ids)} clients")

        # Batch load configurations
        await self.batch_get_client_configurations(client_ids)

        # Load individual configurations for caching
        for client_id in client_ids:
            try:
                await self.get_conditional_client_configuration_async(client_id)
                await self.get_agent_model_preferences_async(client_id, "extraction")
                await self.get_agent_model_preferences_async(client_id, "validation")
                await self.get_agent_model_preferences_async(client_id, "normalization")
                await self.get_agent_model_preferences_async(client_id, "formatting")
            except Exception as e:
                logger.warning(f"Failed to warm cache for client {client_id}: {e}")

        logger.info("Cache warming completed")

    async def close(self) -> None:
        """Close database connections and cleanup resources."""
        if self._pool:
            await self._pool.close()
            self._pool = None

        logger.info("Optimized database tools closed")


# Global optimized database tools instance
_global_optimized_db_tools: OptimizedRFQDatabaseTools | None = None


async def get_optimized_db_tools(db_url: str = None) -> OptimizedRFQDatabaseTools:
    """
    Get or create global optimized database tools instance.

    Args:
        db_url: Database URL

    Returns:
        OptimizedRFQDatabaseTools instance
    """
    global _global_optimized_db_tools

    if _global_optimized_db_tools is None:
        if not db_url:
            from db.session import db_url as default_db_url

            db_url = default_db_url

        _global_optimized_db_tools = OptimizedRFQDatabaseTools(db_url)

    return _global_optimized_db_tools
