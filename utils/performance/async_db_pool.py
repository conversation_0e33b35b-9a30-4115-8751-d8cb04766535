"""
Async database connection pool for improved performance.
"""

import asyncio
import time
from collections.abc import AsyncGenerator
from contextlib import asynccontextmanager
from typing import Any

import asyncpg
from asyncpg import Pool

from utils.exceptions import DatabaseConnectionError, DatabaseError
from utils.log import get_app_logger

logger = get_app_logger()


class AsyncDatabasePool:
    """
    High-performance async database connection pool.

    Features:
    - Connection pooling with configurable min/max connections
    - Automatic connection health checks
    - Connection retry logic with exponential backoff
    - Connection metrics and monitoring
    - Graceful shutdown handling
    """

    def __init__(
        self,
        db_url: str,
        min_connections: int = 5,
        max_connections: int = 20,
        max_retries: int = 3,
        retry_delay: float = 1.0,
        command_timeout: float = 30.0,
        connection_timeout: float = 10.0,
    ):
        """
        Initialize async database pool.

        Args:
            db_url: Database URL
            min_connections: Minimum number of connections to maintain
            max_connections: Maximum number of connections in pool
            max_retries: Maximum retry attempts for failed operations
            retry_delay: Initial delay between retries (exponential backoff)
            command_timeout: Timeout for individual commands
            connection_timeout: Timeout for establishing connections
        """
        # Convert SQLAlchemy URL format to asyncpg format if needed
        if db_url.startswith("postgresql+psycopg://"):
            db_url = db_url.replace("postgresql+psycopg://", "postgresql://")

        self.db_url = db_url
        self.min_connections = min_connections
        self.max_connections = max_connections
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.command_timeout = command_timeout
        self.connection_timeout = connection_timeout

        self.pool: Pool | None = None
        self._initialized = False
        self._lock = asyncio.Lock()

        # Metrics
        self.stats = {
            "total_queries": 0,
            "successful_queries": 0,
            "failed_queries": 0,
            "retry_count": 0,
            "average_query_time": 0.0,
            "pool_hits": 0,
            "pool_misses": 0,
        }

    async def initialize(self) -> None:
        """
        Initialize the connection pool.

        Raises:
            DatabaseConnectionError: If pool initialization fails
        """
        if self._initialized:
            return

        async with self._lock:
            if self._initialized:
                return

            try:
                logger.info(f"Initializing async database pool (min={self.min_connections}, max={self.max_connections})")

                self.pool = await asyncpg.create_pool(
                    self.db_url,
                    min_size=self.min_connections,
                    max_size=self.max_connections,
                    command_timeout=self.command_timeout,
                    server_settings={
                        "application_name": "vanilla_steel_ai",
                        "jit": "off",  # Disable JIT for faster startup
                    },
                    init=self._init_connection,
                )

                # Test the pool with a simple query
                async with self.pool.acquire() as conn:
                    await conn.fetchval("SELECT 1")

                self._initialized = True
                logger.info("✅ Database pool initialized successfully")

            except Exception as e:
                logger.error(f"Failed to initialize database pool: {e}")
                raise DatabaseConnectionError(f"Failed to initialize database pool: {e}") from e

    async def _init_connection(self, conn: asyncpg.Connection) -> None:
        """
        Initialize a new connection with optimal settings.

        Args:
            conn: The new connection to initialize
        """
        # Set optimal connection parameters
        await conn.execute("SET statement_timeout = '30s'")
        await conn.execute("SET lock_timeout = '10s'")
        await conn.execute("SET idle_in_transaction_session_timeout = '5min'")

        # Enable connection-level optimizations
        await conn.execute("SET synchronous_commit = 'off'")  # For non-critical operations
        await conn.execute("SET random_page_cost = 1.1")  # Assume SSD storage

    @asynccontextmanager
    async def acquire_connection(self) -> AsyncGenerator[asyncpg.Connection, None]:
        """
        Acquire a connection from the pool with automatic cleanup.

        Yields:
            Database connection

        Raises:
            DatabaseConnectionError: If connection cannot be acquired
        """
        if not self._initialized:
            await self.initialize()

        if not self.pool:
            raise DatabaseConnectionError("Database pool not initialized")

        start_time = time.time()
        connection = None

        try:
            connection = await asyncio.wait_for(self.pool.acquire(), timeout=self.connection_timeout)

            self.stats["pool_hits"] += 1

            # Test connection health
            await connection.fetchval("SELECT 1")

            yield connection

        except TimeoutError:
            self.stats["pool_misses"] += 1
            logger.error("Timeout acquiring database connection")
            raise DatabaseConnectionError("Timeout acquiring database connection") from None
        except Exception as e:
            self.stats["pool_misses"] += 1
            logger.error(f"Error acquiring database connection: {e}")
            raise DatabaseConnectionError(f"Error acquiring database connection: {e}") from e
        finally:
            if connection:
                try:
                    await self.pool.release(connection)
                except Exception as e:
                    logger.warning(f"Error releasing connection: {e}")

            # Update timing metrics
            query_time = time.time() - start_time
            self._update_timing_stats(query_time)

    async def execute_query(
        self,
        query: str,
        *args,
        fetch_method: str = "fetchval",
        retry: bool = True,
    ) -> Any:
        """
        Execute a query with automatic retry and performance monitoring.

        Args:
            query: SQL query to execute
            *args: Query parameters
            fetch_method: Method to use (fetchval, fetchone, fetchall)
            retry: Whether to retry on failure

        Returns:
            Query result

        Raises:
            DatabaseError: If query execution fails
        """
        last_exception = None

        for attempt in range(self.max_retries if retry else 1):
            try:
                async with self.acquire_connection() as conn:
                    self.stats["total_queries"] += 1

                    # Execute query based on fetch method
                    if fetch_method == "fetchval":
                        result = await conn.fetchval(query, *args)
                    elif fetch_method == "fetchone":
                        result = await conn.fetchrow(query, *args)
                    elif fetch_method == "fetchall":
                        result = await conn.fetch(query, *args)
                    elif fetch_method == "execute":
                        result = await conn.execute(query, *args)
                    else:
                        raise ValueError(f"Unsupported fetch method: {fetch_method}")

                    self.stats["successful_queries"] += 1
                    return result

            except Exception as e:
                last_exception = e
                self.stats["failed_queries"] += 1

                if attempt < self.max_retries - 1:
                    self.stats["retry_count"] += 1
                    delay = self.retry_delay * (2**attempt)  # Exponential backoff
                    logger.warning(f"Query failed (attempt {attempt + 1}/{self.max_retries}), retrying in {delay}s: {e}")
                    await asyncio.sleep(delay)
                else:
                    logger.error(f"Query failed after {self.max_retries} attempts: {e}")

        raise DatabaseError(f"Query execution failed: {last_exception}")

    def _update_timing_stats(self, query_time: float) -> None:
        """Update average query time statistics."""
        if self.stats["successful_queries"] > 0:
            current_avg = self.stats["average_query_time"]
            total_queries = self.stats["successful_queries"]
            self.stats["average_query_time"] = (current_avg * (total_queries - 1) + query_time) / total_queries

    async def get_pool_status(self) -> dict[str, Any]:
        """
        Get current pool status and statistics.

        Returns:
            Dictionary with pool status information
        """
        if not self.pool:
            return {"status": "not_initialized"}

        return {
            "status": "active" if self._initialized else "initializing",
            "pool_size": self.pool.get_size(),
            "pool_min_size": self.pool.get_min_size(),
            "pool_max_size": self.pool.get_max_size(),
            "pool_idle_size": self.pool.get_idle_size(),
            "stats": self.stats.copy(),
        }

    async def close(self) -> None:
        """
        Close the connection pool gracefully.
        """
        if self.pool:
            logger.info("Closing database connection pool...")
            await self.pool.close()
            self.pool = None
            self._initialized = False
            logger.info("Database connection pool closed")

    def get_performance_metrics(self) -> dict[str, Any]:
        """
        Get detailed performance metrics.

        Returns:
            Dictionary with performance metrics
        """
        total_queries = self.stats["total_queries"]
        if total_queries == 0:
            return {"status": "no_queries_executed"}

        success_rate = (self.stats["successful_queries"] / total_queries) * 100
        retry_rate = (self.stats["retry_count"] / total_queries) * 100
        pool_hit_rate = (
            self.stats["pool_hits"] / (self.stats["pool_hits"] + self.stats["pool_misses"]) * 100
            if (self.stats["pool_hits"] + self.stats["pool_misses"]) > 0
            else 0
        )

        return {
            "total_queries": total_queries,
            "success_rate": f"{success_rate:.2f}%",
            "retry_rate": f"{retry_rate:.2f}%",
            "pool_hit_rate": f"{pool_hit_rate:.2f}%",
            "average_query_time": f"{self.stats['average_query_time'] * 1000:.2f}ms",
            "failed_queries": self.stats["failed_queries"],
        }


# Global pool instance
_global_pool: AsyncDatabasePool | None = None
_pool_lock = asyncio.Lock()


async def get_db_pool(db_url: str | None = None, **kwargs) -> AsyncDatabasePool:
    """
    Get or create the global database pool instance.

    Args:
        db_url: Database URL (uses default if not provided)
        **kwargs: Additional pool configuration options

    Returns:
        Global AsyncDatabasePool instance
    """
    global _global_pool

    async with _pool_lock:
        if _global_pool is None:
            if not db_url:
                # Import here to avoid circular imports
                from db.session import db_url as default_db_url

                db_url = default_db_url

            _global_pool = AsyncDatabasePool(db_url, **kwargs)
            await _global_pool.initialize()

        return _global_pool


async def close_global_pool():
    """Close the global database pool."""
    global _global_pool

    if _global_pool:
        await _global_pool.close()
        _global_pool = None
