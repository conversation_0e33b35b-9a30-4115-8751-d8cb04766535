"""
Connection manager for handling multiple external service connections.
"""

import asyncio
import time
from typing import Any

from aiohttp import Client<PERSON>ession, ClientTimeout, TCPConnector

from utils.exceptions import ExternalServiceError
from utils.log import get_app_logger

logger = get_app_logger()


class ConnectionManager:
    """
    Manages HTTP connections and external service integrations.

    Features:
    - HTTP connection pooling
    - Automatic retry with exponential backoff
    - Circuit breaker pattern
    - Request/response metrics
    - Timeout management
    """

    def __init__(
        self,
        max_connections: int = 100,
        max_connections_per_host: int = 30,
        connection_timeout: float = 10.0,
        read_timeout: float = 30.0,
        max_retries: int = 3,
        retry_delay: float = 1.0,
    ):
        """
        Initialize connection manager.

        Args:
            max_connections: Maximum total connections
            max_connections_per_host: Maximum connections per host
            connection_timeout: Connection establishment timeout
            read_timeout: Read timeout for requests
            max_retries: Maximum retry attempts
            retry_delay: Base delay for exponential backoff
        """
        self.max_connections = max_connections
        self.max_connections_per_host = max_connections_per_host
        self.connection_timeout = connection_timeout
        self.read_timeout = read_timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay

        self._session: ClientSession | None = None
        self._lock = asyncio.Lock()

        # Circuit breaker state
        self._circuit_breakers: dict[str, dict[str, Any]] = {}

        # Metrics
        self.stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "retry_count": 0,
            "circuit_breaker_trips": 0,
            "average_response_time": 0.0,
        }

    async def _get_session(self) -> ClientSession:
        """
        Get or create HTTP session with optimized settings.

        Returns:
            aiohttp ClientSession
        """
        if self._session is None or self._session.closed:
            async with self._lock:
                if self._session is None or self._session.closed:
                    # Create TCP connector with connection pooling
                    connector = TCPConnector(
                        limit=self.max_connections,
                        limit_per_host=self.max_connections_per_host,
                        ttl_dns_cache=300,  # DNS cache TTL
                        use_dns_cache=True,
                        keepalive_timeout=30,
                        enable_cleanup_closed=True,
                    )

                    # Create session with timeouts
                    timeout = ClientTimeout(
                        total=self.connection_timeout + self.read_timeout,
                        connect=self.connection_timeout,
                        sock_read=self.read_timeout,
                    )

                    self._session = ClientSession(
                        connector=connector,
                        timeout=timeout,
                        headers={
                            "User-Agent": "VanillaSteelAI/1.0",
                            "Accept": "application/json",
                            "Connection": "keep-alive",
                        },
                    )

                    logger.info("HTTP session initialized with connection pooling")

        return self._session

    def _get_circuit_breaker_key(self, url: str) -> str:
        """Extract host from URL for circuit breaker key."""
        from urllib.parse import urlparse

        parsed = urlparse(url)
        return f"{parsed.scheme}://{parsed.netloc}"

    def _is_circuit_open(self, service_key: str) -> bool:
        """
        Check if circuit breaker is open for a service.

        Args:
            service_key: Service identifier

        Returns:
            True if circuit is open (should reject requests)
        """
        if service_key not in self._circuit_breakers:
            return False

        breaker = self._circuit_breakers[service_key]
        current_time = time.time()

        # Check if circuit should be reset
        if current_time - breaker["last_failure"] > breaker["reset_timeout"]:
            breaker["failure_count"] = 0
            breaker["state"] = "closed"
            return False

        # Circuit is open if failure count exceeds threshold
        return breaker["failure_count"] >= breaker["failure_threshold"]

    def _record_failure(self, service_key: str) -> None:
        """Record a failure for circuit breaker."""
        if service_key not in self._circuit_breakers:
            self._circuit_breakers[service_key] = {
                "failure_count": 0,
                "failure_threshold": 5,
                "reset_timeout": 60,  # 1 minute
                "state": "closed",
                "last_failure": 0,
            }

        breaker = self._circuit_breakers[service_key]
        breaker["failure_count"] += 1
        breaker["last_failure"] = time.time()

        if breaker["failure_count"] >= breaker["failure_threshold"]:
            breaker["state"] = "open"
            self.stats["circuit_breaker_trips"] += 1
            logger.warning(f"Circuit breaker opened for {service_key}")

    def _record_success(self, service_key: str) -> None:
        """Record a success for circuit breaker."""
        if service_key in self._circuit_breakers:
            self._circuit_breakers[service_key]["failure_count"] = 0
            self._circuit_breakers[service_key]["state"] = "closed"

    async def make_request(self, method: str, url: str, retry: bool = True, **kwargs) -> dict[str, Any]:
        """
        Make HTTP request with retry logic and circuit breaker.

        Args:
            method: HTTP method (GET, POST, etc.)
            url: Request URL
            retry: Whether to retry on failure
            **kwargs: Additional request parameters

        Returns:
            Response data

        Raises:
            ExternalServiceError: If request fails after retries
        """
        service_key = self._get_circuit_breaker_key(url)

        # Check circuit breaker
        if self._is_circuit_open(service_key):
            raise ExternalServiceError(f"Circuit breaker open for {service_key}")

        start_time = time.time()
        last_exception = None

        for attempt in range(self.max_retries if retry else 1):
            try:
                self.stats["total_requests"] += 1

                session = await self._get_session()

                async with session.request(method, url, **kwargs) as response:
                    # Check for HTTP errors
                    if response.status >= 400:
                        error_text = await response.text()
                        raise ExternalServiceError(
                            f"HTTP {response.status}: {error_text}",
                            details={
                                "status_code": response.status,
                                "url": url,
                                "method": method,
                            },
                        )

                    # Parse response
                    if response.content_type == "application/json":
                        result = await response.json()
                    else:
                        result = {"content": await response.text()}

                    # Record success
                    self._record_success(service_key)
                    self.stats["successful_requests"] += 1

                    # Update timing stats
                    response_time = time.time() - start_time
                    self._update_timing_stats(response_time)

                    return result

            except Exception as e:
                last_exception = e
                self.stats["failed_requests"] += 1
                self._record_failure(service_key)

                if attempt < self.max_retries - 1:
                    self.stats["retry_count"] += 1
                    delay = self.retry_delay * (2**attempt)  # Exponential backoff
                    logger.warning(f"Request failed (attempt {attempt + 1}/{self.max_retries}), retrying in {delay}s: {e}")
                    await asyncio.sleep(delay)
                else:
                    logger.error(f"Request failed after {self.max_retries} attempts: {e}")

        raise ExternalServiceError(f"Request failed: {last_exception}")

    def _update_timing_stats(self, response_time: float) -> None:
        """Update average response time statistics."""
        if self.stats["successful_requests"] > 0:
            current_avg = self.stats["average_response_time"]
            total_requests = self.stats["successful_requests"]
            self.stats["average_response_time"] = (current_avg * (total_requests - 1) + response_time) / total_requests

    async def get_openrouter_completion(self, model_id: str, messages: list, api_key: str, **kwargs) -> dict[str, Any]:
        """
        Optimized OpenRouter API call with connection pooling.

        Args:
            model_id: Model identifier
            messages: Chat messages
            api_key: OpenRouter API key
            **kwargs: Additional OpenRouter parameters

        Returns:
            OpenRouter API response
        """
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://vanilla-steel-ai.com",
            "X-Title": "Steel RFQ Processor",
        }

        payload = {"model": model_id, "messages": messages, **kwargs}

        return await self.make_request("POST", "https://openrouter.ai/api/v1/chat/completions", headers=headers, json=payload)

    async def close(self) -> None:
        """Close the HTTP session."""
        if self._session and not self._session.closed:
            await self._session.close()
            logger.info("HTTP session closed")

    def get_performance_metrics(self) -> dict[str, Any]:
        """
        Get connection manager performance metrics.

        Returns:
            Dictionary with performance metrics
        """
        total_requests = self.stats["total_requests"]
        if total_requests == 0:
            return {"status": "no_requests_made"}

        success_rate = (self.stats["successful_requests"] / total_requests) * 100
        retry_rate = (self.stats["retry_count"] / total_requests) * 100

        return {
            "total_requests": total_requests,
            "success_rate": f"{success_rate:.2f}%",
            "retry_rate": f"{retry_rate:.2f}%",
            "circuit_breaker_trips": self.stats["circuit_breaker_trips"],
            "average_response_time": f"{self.stats['average_response_time'] * 1000:.2f}ms",
            "active_circuit_breakers": len([k for k, v in self._circuit_breakers.items() if v["state"] == "open"]),
        }


# Global connection manager instance
_global_connection_manager: ConnectionManager | None = None
_manager_lock = asyncio.Lock()


async def get_connection_manager(**kwargs) -> ConnectionManager:
    """
    Get or create the global connection manager instance.

    Args:
        **kwargs: Connection manager configuration options

    Returns:
        Global ConnectionManager instance
    """
    global _global_connection_manager

    async with _manager_lock:
        if _global_connection_manager is None:
            _global_connection_manager = ConnectionManager(**kwargs)

        return _global_connection_manager


async def close_global_connection_manager():
    """Close the global connection manager."""
    global _global_connection_manager

    if _global_connection_manager:
        await _global_connection_manager.close()
        _global_connection_manager = None
