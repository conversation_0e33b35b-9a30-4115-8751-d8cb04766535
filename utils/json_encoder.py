import json
from datetime import date, datetime
from decimal import Decimal
from uuid import UUID


class UUIDEncoder(json.JSONEncoder):
    """Custom JSON encoder that handles UUIDs, datetime, and other common types."""

    def default(self, obj):
        if isinstance(obj, UUID):
            return str(obj)
        elif isinstance(obj, datetime | date):
            return obj.isoformat()
        elif isinstance(obj, Decimal):
            return float(obj)
        return super().default(obj)


def json_dumps(obj, **kwargs):
    """JSON dumps with UUID support."""
    return json.dumps(obj, cls=UUIDEncoder, **kwargs)


def safe_json_serialize(obj):
    """Safely serialize an object to JSON, converting UUIDs to strings."""
    if isinstance(obj, dict):
        return {key: safe_json_serialize(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [safe_json_serialize(item) for item in obj]
    elif isinstance(obj, UUID):
        return str(obj)
    elif isinstance(obj, datetime | date):
        return obj.isoformat()
    elif isinstance(obj, Decimal):
        return float(obj)
    else:
        return obj


def convert_uuids_to_strings(data):
    """Convert all UUID objects in a data structure to strings.

    Args:
        data: Any data structure containing potential UUID objects

    Returns:
        The same data structure with UUIDs converted to strings
    """
    return safe_json_serialize(data)
