default_language_version:
  python: python3.12

repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: check-json
      - id: check-merge-conflict
      - id: end-of-file-fixer
        exclude: '^(\.local/.*|\.venv/.*|site-packages/.*)'
      - id: trailing-whitespace
        exclude: '^(\.local/.*|\.venv/.*|site-packages/.*)'
      - id: check-merge-conflict
      - id: check-docstring-first
        exclude: '^(\.local/.*|\.venv/.*|site-packages/.*|.*__pycache__/.*)'
        files: '^(agents|api|db|models|utils|workflows)/.*\.py$'
      - id: check-yaml
        args: [--allow-multiple-documents]
      - id: check-added-large-files
        args: [--maxkb=4096]
      - id: no-commit-to-branch
        args: ['--branch', 'main', '--branch', 'staging']

  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: "v0.9.10"
    hooks:
      - id: ruff
        name: ruff
        args: ["--fix", "--exit-non-zero-on-fix"]
        exclude: '^(\.local/.*|\.venv/.*|site-packages/.*)'
      - id: ruff-format
        name: ruff-format
        exclude: '^(\.local/.*|\.venv/.*|site-packages/.*)'

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.15.0
    hooks:
      - id: mypy
        name: mypy
        additional_dependencies: ['pydantic']
        exclude: '^(\.local/.*|\.venv/.*|site-packages/.*|tests/.*)'
        files: '^(agents|api|db|models|utils|workflows)/.*\.py$'
        args: [--no-error-summary]

  - repo: https://github.com/rhysd/actionlint
    rev: v1.7.7
    hooks:
      - id: actionlint
        name: actionlint

  - repo: https://github.com/commitizen-tools/commitizen
    rev: v4.6.0
    hooks:
      - id: commitizen
        stages: [commit-msg]
