#!/usr/bin/env python3
"""
Quick verification of import availability for vanilla-steel-ai
"""


def test_imports():
    """Test if the problematic imports work"""
    print("Testing Import Availability")
    print("=" * 40)

    # Test psutil
    try:
        import psutil

        print("psutil: Available")
        print(f"   Version: {psutil.__version__}")
    except ImportError as e:
        print(f"psutil: Failed - {e}")

    # Test aiohttp
    try:
        import aiohttp

        print("aiohttp: Available")
        print(f"   Version: {aiohttp.__version__}")

        # Test specific imports from connection_manager.py
        try:
            import aiohttp

            print("   aiohttp imports work")
        except ImportError as e:
            print(f"   aiohttp import failed: {e}")
    except ImportError as e:
        print(f"aiohttp: Failed - {e}")

    # Test asyncpg
    try:
        import asyncpg

        print("asyncpg: Available")
        print(f"   Version: {asyncpg.__version__}")

        # Test specific imports from async_db_pool.py
        try:
            from asyncpg import Pool

            print(f"   Pool import works: {Pool}")
        except ImportError as e:
            print(f"   Pool import failed: {e}")
    except ImportError as e:
        print(f"asyncpg: Failed - {e}")


if __name__ == "__main__":
    test_imports()
