#!/bin/bash

# Simple fix for mypy dependency issues
set -e

echo "🔧 Fixing mypy dependency issues..."

# Check if we're in the right directory
if [ ! -f "pyproject.toml" ]; then
    echo "❌ Error: pyproject.toml not found. Please run this script from the project root."
    exit 1
fi

echo "📦 Updating dependencies..."

# Update uv lock file and sync dependencies
uv lock
uv sync --dev

echo "📝 Installing type stubs..."

# Install type stubs explicitly (only ones that exist)
uv run python -m pip install types-psutil types-aiofiles

# Let mypy install any missing types
uv run mypy --install-types --non-interactive || true

echo "🧪 Testing mypy fix..."

# Test the specific files
if uv run mypy utils/performance/metrics_collector.py utils/performance/connection_manager.py utils/performance/async_db_pool.py; then
    echo "✅ MyPy issues fixed!"
else
    echo "❌ Some issues remain. Try running: uv run mypy --install-types"
fi
