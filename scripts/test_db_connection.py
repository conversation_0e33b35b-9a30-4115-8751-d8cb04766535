#!/usr/bin/env python3
"""
Test script to verify database connection configuration
"""

import os
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def test_cloud_sql_connection():
    """Test Cloud SQL Unix socket connection configuration"""

    # Set environment variables for Cloud SQL
    os.environ.update(
        {
            "RUNTIME_ENV": "stg",
            "DB_HOST": "/cloudsql/vs-data-439613:europe-west1:vs-ai-staging",
            "DB_PORT": "5432",
            "DB_USER": "vs-ai-staging-admin",
            "DB_PASS": "C4D8itbu7*sAxxGdikL4ijHdLVTTstz",  # Replace with actual password
            "DB_DATABASE": "postgres_stg",
            "POSTGRES_INIT_SCHEMA": "public",
        }
    )

    try:
        from db.settings import db_settings

        print("🔧 Testing database configuration...")
        print(f"Host: {db_settings.db_host}")
        print(f"Port: {db_settings.db_port}")
        print(f"Database: {db_settings.db_database}")
        print(f"Is Unix Socket: {db_settings.is_unix_socket()}")

        # Test URL generation
        db_url = db_settings.get_db_url()
        print("✅ Database URL generated successfully")
        print(f"URL format: {db_url.split('://')[0]}://<user>@/{db_settings.db_database}?host={db_settings.db_host}")

        # Test SSL config
        ssl_config = db_settings.get_ssl_config()
        print(f"SSL Config: {ssl_config}")

        # Test validation
        errors = db_settings.validate_environment()
        if errors:
            print(f"❌ Validation errors: {errors}")
        else:
            print("✅ Environment validation passed")

        return True

    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False


def test_tcp_connection():
    """Test TCP connection configuration"""

    # Set environment variables for TCP connection
    os.environ.update(
        {
            "RUNTIME_ENV": "dev",
            "DB_HOST": "localhost",  # or your actual IP
            "DB_PORT": "5432",
            "DB_USER": "vs-ai-staging-admin",
            "DB_PASS": "your-password-here",  # Replace with actual password
            "DB_DATABASE": "postgres_stg",
            "POSTGRES_INIT_SCHEMA": "postgres_stg",
        }
    )

    try:
        from db.settings import db_settings

        print("\n🔧 Testing TCP database configuration...")
        print(f"Host: {db_settings.db_host}")
        print(f"Port: {db_settings.db_port}")
        print(f"Database: {db_settings.db_database}")
        print(f"Is Unix Socket: {db_settings.is_unix_socket()}")

        # Test URL generation
        db_url = db_settings.get_db_url()
        print("✅ Database URL generated successfully")
        print(f"URL format: {db_url.split('://')[0]}://<user>@{db_settings.db_host}:{db_settings.db_port}/{db_settings.db_database}")

        return True

    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False


if __name__ == "__main__":
    print("🧪 Testing Database Connection Configurations\n")

    # Test Cloud SQL configuration
    success1 = test_cloud_sql_connection()

    # Test TCP configuration
    success2 = test_tcp_connection()

    if success1 and success2:
        print("\n✅ All tests passed! Database configuration looks good.")
    else:
        print("\n❌ Some tests failed. Please check the configuration.")
        sys.exit(1)
