#!/bin/bash

# Fixed commands to check logs and image
PROJECT_ID="vs-data-439613"

echo "📋 Checking recent Cloud Run logs (last 1 hour)..."

# Get recent Cloud Run logs
gcloud logging read \
    "resource.type=\"cloud_run_revision\" AND timestamp>=\"2025-06-25T12:00:00Z\"" \
    --project=$PROJECT_ID \
    --limit=30 \
    --format="table(timestamp,resource.labels.service_name,textPayload)"

echo ""
echo "🔍 Looking for error messages in Cloud Run:"
gcloud logging read \
    "resource.type=\"cloud_run_revision\" AND (textPayload:\"error\" OR textPayload:\"Error\" OR textPayload:\"ERROR\" OR textPayload:\"failed\" OR textPayload:\"traceback\")" \
    --project=$PROJECT_ID \
    --limit=20 \
    --format="table(timestamp,resource.labels.service_name,textPayload)"

echo ""
echo "🐳 Getting more detailed image information:"
gcloud container images describe europe-west1-docker.pkg.dev/vs-data-439613/vanilla-steel-ai/vanilla-steel-ai:20250625110729-1cb921f \
    --format="yaml" | head -20

echo ""
echo "📋 Checking all Cloud Run services in your project:"
gcloud run services list --region=europe-west1 --project=$PROJECT_ID

echo ""
echo "🔍 Let's also check if there are any recent builds:"
gcloud builds list --limit=5 --project=$PROJECT_ID --format="table(createTime,status,source.repoSource.repoName,source.repoSource.branchName)"
