#!/bin/bash

# start-sql-proxy.sh
# Start the Cloud SQL Proxy with Unix socket support

set -e

# Configuration
PROJECT_ID="vs-data-439613"
REGION="europe-west1"
INSTANCE_NAME="vs-ai-staging"
CONNECTION_NAME="$PROJECT_ID:$REGION:$INSTANCE_NAME"
SOCKET_DIR="/tmp/cloudsql"

echo "🚀 Starting Cloud SQL Proxy..."
echo "📍 Connection name: $CONNECTION_NAME"
echo "📁 Socket directory: $SOCKET_DIR"

# Ensure socket directory exists
mkdir -p "$SOCKET_DIR"

# Function to cleanup on exit
cleanup() {
    echo ""
    echo "🧹 Cleaning up..."
    if [ ! -z "$PROXY_PID" ]; then
        echo "🛑 Stopping Cloud SQL Proxy (PID: $PROXY_PID)..."
        kill $PROXY_PID 2>/dev/null || true
    fi
    echo "✅ Cleanup complete."
}

# Set trap for cleanup
trap cleanup EXIT INT TERM

# Start the proxy in the background
echo "▶️  Starting proxy in background..."
cloud-sql-proxy \
    --unix-socket="$SOCKET_DIR" \
    "$CONNECTION_NAME" &

PROXY_PID=$!
echo "🔄 Cloud SQL Proxy started with PID: $PROXY_PID"

# Wait for socket to be created
echo "⏳ Waiting for socket to be created..."
SOCKET_FILE="$SOCKET_DIR/$CONNECTION_NAME/.s.PGSQL.5432"
COUNTER=0
MAX_WAIT=30

while [ ! -S "$SOCKET_FILE" ] && [ $COUNTER -lt $MAX_WAIT ]; do
    echo "   Waiting... ($((COUNTER + 1))/$MAX_WAIT)"
    sleep 1
    COUNTER=$((COUNTER + 1))
done

if [ -S "$SOCKET_FILE" ]; then
    echo "✅ Socket created successfully!"
    echo "📍 Socket location: $SOCKET_FILE"

    # Show socket info
    echo ""
    echo "🔍 Socket information:"
    ls -la "$SOCKET_FILE"

    echo ""
    echo "🧪 Testing connection (you'll need to provide the password)..."
    echo "📝 Connection details:"
    echo "   Host: $SOCKET_DIR/$CONNECTION_NAME"
    echo "   Port: 5432"
    echo "   User: vs-ai-staging-admin"
    echo "   Database: postgres_stg"

    # Test connection (optional - will prompt for password)
    read -p "🔐 Do you want to test the connection now? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "🔑 Please enter the database password when prompted..."
        psql -h "$SOCKET_DIR/$CONNECTION_NAME" -p 5432 -U vs-ai-staging-admin -d postgres_stg -c "SELECT 1 as test_connection;"
        if [ $? -eq 0 ]; then
            echo "✅ Database connection test successful!"
        else
            echo "❌ Database connection test failed."
        fi
    fi

    echo ""
    echo "🎉 Cloud SQL Proxy is running!"
    echo "🔄 The proxy will continue running. Press Ctrl+C to stop."
    echo ""
    echo "📋 To use this socket in your Docker container:"
    echo "   docker run -v $SOCKET_DIR:/cloudsql your-image"
    echo ""

    # Keep the proxy running
    wait $PROXY_PID

else
    echo "❌ Failed to create socket within $MAX_WAIT seconds"
    echo "🔍 Check the proxy logs above for errors"
    exit 1
fi
