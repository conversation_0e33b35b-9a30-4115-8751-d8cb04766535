#!/bin/bash

############################################################################
# Environment Variable Validation Script
#
# This script validates that all required environment variables are set.
# It attempts to import the ApiSettings class from the api.settings module,
# which will fail if any required environment variables are missing.
#
# Exit codes:
# - 0: Success - all required environment variables are present and valid
# - 1: Failure - some required environment variables are missing or invalid
############################################################################

echo "Validating environment variables..."

# Attempt to create settings by importing api_settings
# This will validate all required environment variables
python -c "from api.settings import api_settings; print('✅ Environment validation successful!')"
RESULT=$?

if [ $RESULT -ne 0 ]; then
  echo "❌ Environment validation failed! Some required environment variables are missing or invalid."
  exit 1
else
  echo "✅ All required environment variables are present and valid."
  exit 0
fi
