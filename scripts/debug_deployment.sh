#!/bin/bash

############################################################################
# Debug Cloud Run Deployment Script
# This script helps debug the deployment issue by testing locally
############################################################################

set -e

# Configuration
PROJECT_ID="vs-data-439613"
SERVICE_NAME="vanilla-steel-ai-api-stg"
REGION="europe-west1"
IMAGE_REPO="europe-west1-docker.pkg.dev/vs-data-439613/vanilla-steel-ai"
IMAGE_NAME="vanilla-steel-ai"
IMAGE_TAG="stg"
SECRET_NAME="vanilla-steel-ai-staging-secrets"

echo "🔍 Debugging Cloud Run Deployment Issue"
echo "========================================"

# Step 1: Check if we can access the secret
echo "📋 Step 1: Checking GCP Secret access..."
if gcloud secrets versions access latest --secret="$SECRET_NAME" --project="$PROJECT_ID" > /tmp/secrets.yml 2>/dev/null; then
    echo "✅ Successfully downloaded secret to /tmp/secrets.yml"
    echo "📄 Secret file size: $(wc -c < /tmp/secrets.yml) bytes"
    echo "📄 Secret file lines: $(wc -l < /tmp/secrets.yml) lines"
    
    # Show first few lines (without sensitive data)
    echo "📄 First few lines of secret file:"
    head -5 /tmp/secrets.yml | sed 's/=.*/=***REDACTED***/'
else
    echo "❌ Failed to download secret. Check permissions."
    exit 1
fi

# Step 2: Check if image exists
echo ""
echo "📋 Step 2: Checking if Docker image exists..."
if gcloud container images describe "$IMAGE_REPO/$IMAGE_NAME:$IMAGE_TAG" --project="$PROJECT_ID" >/dev/null 2>&1; then
    echo "✅ Docker image exists: $IMAGE_REPO/$IMAGE_NAME:$IMAGE_TAG"
else
    echo "❌ Docker image not found: $IMAGE_REPO/$IMAGE_NAME:$IMAGE_TAG"
    echo "Available images:"
    gcloud container images list --repository="$IMAGE_REPO" --project="$PROJECT_ID" || true
    exit 1
fi

# Step 3: Test environment variable formatting
echo ""
echo "📋 Step 3: Testing environment variable formatting..."

# Create a test env vars file similar to what GitHub Actions would create
cat > /tmp/test_env_vars.yml << 'EOF'
RUNTIME_ENV: stg
DEBUG: true
WAIT_FOR_DB: true
MIGRATE_DB: true
WAIT_FOR_REDIS: false
DB_POOL_SIZE: 5
DB_MAX_OVERFLOW: 10
API_TIMEOUT: 120
DB_HOST: /cloudsql/vs-data-439613:europe-west1:vs-ai-staging
DB_PORT: 5432
POSTGRES_INIT_SCHEMA: postgres_stg
DEBUG_MODE: false
PERFORMANCE_MODE: true
EOF

echo "✅ Created test environment variables file"

# Step 4: Try a simplified deployment command
echo ""
echo "📋 Step 4: Testing simplified deployment command..."

# First, let's try with just a few environment variables
echo "🧪 Testing with minimal environment variables..."

gcloud run deploy "$SERVICE_NAME" \
    --image="$IMAGE_REPO/$IMAGE_NAME:$IMAGE_TAG" \
    --region="$REGION" \
    --project="$PROJECT_ID" \
    --set-env-vars="RUNTIME_ENV=stg,DEBUG=true,WAIT_FOR_DB=false,MIGRATE_DB=false" \
    --allow-unauthenticated \
    --port=8000 \
    --memory=2Gi \
    --cpu=2 \
    --min-instances=0 \
    --max-instances=5 \
    --timeout=900 \
    --no-traffic \
    --tag=debug-test \
    --quiet

if [ $? -eq 0 ]; then
    echo "✅ Simplified deployment succeeded!"
    
    # Get the service URL
    SERVICE_URL=$(gcloud run services describe "$SERVICE_NAME" --region="$REGION" --project="$PROJECT_ID" --format='value(status.url)')
    echo "🌐 Service URL: $SERVICE_URL"
    
    # Test the health endpoint
    echo "🧪 Testing health endpoint..."
    if curl -s -f "$SERVICE_URL/v1/health" >/dev/null 2>&1; then
        echo "✅ Health endpoint is responding!"
    else
        echo "⚠️ Health endpoint not responding (this might be expected with minimal env vars)"
    fi
    
    # Check logs
    echo "📋 Recent logs:"
    gcloud logging read \
        "resource.type=\"cloud_run_revision\" AND resource.labels.service_name=\"$SERVICE_NAME\" AND timestamp>=\"$(date -u -d '5 minutes ago' +%Y-%m-%dT%H:%M:%SZ)\"" \
        --project="$PROJECT_ID" \
        --limit=10 \
        --format="table(timestamp,textPayload)" || true
        
else
    echo "❌ Even simplified deployment failed!"
    echo "This suggests a more fundamental issue."
fi

# Step 5: Check current service status
echo ""
echo "📋 Step 5: Checking current service status..."
if gcloud run services describe "$SERVICE_NAME" --region="$REGION" --project="$PROJECT_ID" >/dev/null 2>&1; then
    echo "✅ Service exists. Current status:"
    gcloud run services describe "$SERVICE_NAME" --region="$REGION" --project="$PROJECT_ID" --format="table(status.conditions[0].type,status.conditions[0].status,status.conditions[0].reason)"
else
    echo "ℹ️ Service does not exist yet."
fi

# Step 6: Show what the full command would look like
echo ""
echo "📋 Step 6: Full deployment command analysis..."
echo "The problematic command from your error was:"
echo "gcloud run deploy vanilla-steel-ai-api-stg --image europe-west1-docker.pkg.dev/vs-data-439613/vanilla-steel-ai/vanilla-steel-ai:stg --update-env-vars ^,^OPENROUTER_API_KEY=..."

echo ""
echo "🔍 Issues identified:"
echo "1. The '^,^' syntax is unusual - this might be a GitHub Actions formatting issue"
echo "2. The command appears to be truncated at 'PERFORMANCE'"
echo "3. All environment variables are being passed inline, making the command very long"

echo ""
echo "💡 Recommendations:"
echo "1. Use --env-vars-file instead of --update-env-vars for better handling"
echo "2. Split the deployment into smaller chunks"
echo "3. Check GitHub Actions logs for the exact command being generated"
echo "4. Consider using Cloud Run YAML configuration files"

echo ""
echo "🎯 Next steps:"
echo "1. Check the GitHub Actions workflow logs to see the exact gcloud command"
echo "2. Try deploying with --env-vars-file using the secrets.yml"
echo "3. If needed, split environment variables across multiple update commands"

# Cleanup
rm -f /tmp/test_env_vars.yml
echo ""
echo "✅ Debug script completed!"
