#!/bin/bash

# Check Cloud SQL configuration for socket connections
PROJECT_ID="vs-data-439613"

echo "🔍 Checking Cloud SQL configuration for socket connections..."

echo ""
echo "📋 Cloud SQL instance details:"
gcloud sql instances describe vs-ai-staging --project=$PROJECT_ID --format="yaml(connectionName,ipAddresses,settings.ipConfiguration)"

echo ""
echo "🔗 Connection name verification:"
CONNECTION_NAME=$(gcloud sql instances describe vs-ai-staging --project=$PROJECT_ID --format="value(connectionName)")
echo "Connection name: $CONNECTION_NAME"

echo ""
echo "🌐 IP Configuration:"
gcloud sql instances describe vs-ai-staging --project=$PROJECT_ID --format="table(ipAddresses[].type,ipAddresses[].ipAddress)"

echo ""
echo "🔧 Checking if Cloud SQL Admin API is enabled:"
gcloud services list --enabled --filter="name:sqladmin.googleapis.com" --project=$PROJECT_ID

echo ""
echo "🔧 Checking if Cloud SQL is properly configured for Cloud Run:"
echo "Authorized networks:"
gcloud sql instances describe vs-ai-staging --project=$PROJECT_ID --format="table(settings.ipConfiguration.authorizedNetworks[].name,settings.ipConfiguration.authorizedNetworks[].value)"

echo ""
echo "🔧 Private network configuration:"
gcloud sql instances describe vs-ai-staging --project=$PROJECT_ID --format="value(settings.ipConfiguration.privateNetwork)"

echo ""
echo "💡 For Cloud Run socket connections, your instance should:"
echo "  ✅ Have Cloud SQL Admin API enabled"
echo "  ✅ Be in the same project as Cloud Run"
echo "  ✅ Allow connections from Cloud Run (no IP restrictions needed for sockets)"
echo "  ✅ Have the correct connection name format"

echo ""
echo "🎯 Your connection details:"
echo "  Instance name: vs-ai-staging"
echo "  Project: vs-data-439613"
echo "  Region: europe-west1"
echo "  Connection name: $CONNECTION_NAME"
echo "  Socket path: /cloudsql/$CONNECTION_NAME"
