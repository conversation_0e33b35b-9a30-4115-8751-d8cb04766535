#!/bin/bash

############################################################################
# Container Entrypoint script - CLOUD RUN COMPATIBLE VERSION
############################################################################

if [[ "$PRINT_ENV_ON_LOAD" = true || "$PRINT_ENV_ON_LOAD" = True ]]; then
  echo "=================================================="
  printenv
  echo "=================================================="
fi

############################################################################
# Validate environment variables
############################################################################

echo "Validating environment variables..."
python -c "from api.settings import api_settings; print('✅ Environment validation successful!')"
if [ $? -ne 0 ]; then
  echo "❌ Environment validation failed! Exiting..."
  exit 1
fi

if [[ -z "$DB_HOST" || -z "$DB_USER" || -z "$DB_PASS" ]]; then
  echo "⚠️  Database configuration incomplete. Skipping database operations."
  echo "This is expected during initial Cloud Run deployment."
  WAIT_FOR_DB=false
  MIGRATE_DB=false
fi

############################################################################
# Wait for Services (Skip for Cloud Run with Unix sockets)
############################################################################

if [[ "$WAIT_FOR_DB" = true || "$WAIT_FOR_DB" = True ]]; then
  echo "Checking database connection..."

  # For Cloud SQL Unix socket connections, skip the port check
  if [[ "$DB_HOST" == /cloudsql/* ]]; then
    echo "Using Cloud SQL Unix socket connection at $DB_HOST"

    # Test PostgreSQL connection directly
    echo "Testing PostgreSQL connection..."
    for i in {1..30}; do
      if PGPASSWORD=$DB_PASS psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_DATABASE -c "SELECT 1" >/dev/null 2>&1; then
        echo "✅ PostgreSQL is accepting connections!"
        break
      fi
      echo "Waiting for PostgreSQL to accept connections... ($i/30)"
      sleep 2
    done

    # Final check
    if ! PGPASSWORD=$DB_PASS psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_DATABASE -c "SELECT 1" >/dev/null 2>&1; then
      echo "❌ PostgreSQL is not accepting connections. This might be normal if the Cloud SQL proxy isn't ready yet."
      echo "Proceeding anyway since WAIT_FOR_DB might be misconfigured for Cloud Run..."
    fi
  else
    # Original TCP-based check for non-Cloud SQL deployments
    echo "Using TCP connection to database at $DB_HOST:$DB_PORT..."

    # Wait for port to be available
    for i in {1..60}; do
      if nc -z $DB_HOST $DB_PORT 2>/dev/null; then
        echo "Database port is open!"
        break
      fi
      echo "Waiting for database port... ($i/60)"
      sleep 5
    done
  fi
fi

############################################################################
# Migrate database
############################################################################

if [[ "$MIGRATE_DB" = true || "$MIGRATE_DB" = True ]]; then
  echo "++++++++++++++++++++++++++++++++++++++++++++++++++++++++"
  echo "Migrating Database"

  # Check if alembic is available
  if [ -x "$(command -v alembic)" ] || python -c "import alembic" >/dev/null 2>&1; then
    # Create pgvector extension if needed
    echo "Creating pgvector extension if it doesn't exist..."
    PGPASSWORD=$DB_PASS psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_DATABASE -c "CREATE EXTENSION IF NOT EXISTS vector;" || {
      echo "⚠️  Warning: Could not create pgvector extension. This might be expected if it's already installed."
    }

    # Environment-specific migration strategy
    if [[ "$RUNTIME_ENV" == "dev" || "$RUNTIME_ENV" == "stg" ]]; then
      echo "🔄 $RUNTIME_ENV environment detected - forcing table recreation..."
      echo "📋 This will drop and recreate all tables with fresh data."

      # Clear alembic version table to force re-running of schema creation migration
      echo "Clearing alembic version tracking to force migration re-run..."
      PGPASSWORD=$DB_PASS psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_DATABASE -c "DELETE FROM alembic_version;" 2>/dev/null || {
        echo "ℹ️  Note: alembic_version table doesn't exist yet (expected for first run)"
      }

      echo "Running full schema recreation for $RUNTIME_ENV environment..."
    else
      echo "🏭 Production environment detected - running standard migrations..."
      echo "📋 Only new migrations will be applied (safe production mode)."
    fi

    echo "Running Alembic migrations..."
    # Set POSTGRES_INIT_SCHEMA if not already set (required by api.settings)
    export POSTGRES_INIT_SCHEMA=${POSTGRES_INIT_SCHEMA:-$DB_DATABASE}
    # Change to db directory to run migrations (ensures correct path resolution)
    cd db && alembic upgrade head

    if [ $? -ne 0 ]; then
      echo "❌ Database migration failed. Exiting."
      exit 1
    fi

    echo "✅ Database migration completed successfully."

    # Log final migration status
    if [[ "$RUNTIME_ENV" == "dev" || "$RUNTIME_ENV" == "stg" ]]; then
      echo "📊 Tables recreated fresh for $RUNTIME_ENV environment"
    else
      echo "📊 Standard migration completed for $RUNTIME_ENV environment"
    fi
  else
    echo "❌ Alembic is not available. Skipping database migration."
    exit 1
  fi
  echo "++++++++++++++++++++++++++++++++++++++++++++++++++++++++"
fi

############################################################################
# Start App
############################################################################

if [[ -n "$START_COMMAND" ]]; then
  echo "Running start command: $START_COMMAND"
  exec $START_COMMAND
elif [[ "$1" == "api" || "$1" == "chill" ]]; then
  echo "Starting FastAPI server..."
  # Use PORT environment variable from Cloud Run, default to 8000
  PORT=${PORT:-8000}
  echo "Listening on port: $PORT"

  # Try different ways to start uvicorn
  if command -v uvicorn >/dev/null 2>&1; then
    # If uvicorn is in PATH, use it directly
    echo "Starting with uvicorn from PATH..."
    exec uvicorn api.main:app --host 0.0.0.0 --port $PORT --workers 1
  elif command -v uv >/dev/null 2>&1 && uv run uvicorn --version >/dev/null 2>&1; then
    # If uv is available and can run uvicorn
    echo "Starting with uv run uvicorn..."
    exec uv run uvicorn api.main:app --host 0.0.0.0 --port $PORT --workers 1
  elif python -m uvicorn --version >/dev/null 2>&1; then
    # Try running uvicorn as a Python module
    echo "Starting with python -m uvicorn..."
    exec python -m uvicorn api.main:app --host 0.0.0.0 --port $PORT --workers 1
  else
    # Last resort: try with python3
    echo "Starting with python3 -m uvicorn..."
    exec python3 -m uvicorn api.main:app --host 0.0.0.0 --port $PORT --workers 1
  fi
elif [[ "$1" == "ui" ]]; then
  echo "Starting Streamlit UI..."
  PORT=${PORT:-8501}
  echo "Listening on port: $PORT"
  exec streamlit run ui/Home.py --server.port $PORT --server.address 0.0.0.0
else
  echo "Running: $@"
  exec "$@"
fi

# This should never be reached
echo ">>> Unexpected: No command executed!"
exit 1
