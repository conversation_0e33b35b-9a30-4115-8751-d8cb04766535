#!/bin/bash

echo "=== Fixing Unkey Package Conflict ==="

echo "1. Remove the conflicting 'unkey' package..."
docker exec -it vanilla-steel-ai-api pip uninstall -y unkey

echo "2. Keep the correct 'unkey.py' package..."
docker exec -it vanilla-steel-ai-api pip list | grep unkey

echo "3. Reinstall unkey.py to ensure it's properly configured..."
docker exec -it vanilla-steel-ai-api pip install --force-reinstall unkey.py==0.7.2

echo "4. Test the correct import..."
docker exec -it vanilla-steel-ai-api python -c "
try:
    from unkey_py import Unkey
    print('✅ SUCCESS: from unkey_py import Unkey worked!')
    print('Unkey class:', Unkey)

    # Test creating an instance
    client = Unkey(root_key='test_key')
    print('✅ SUCCESS: Unkey instance created')
    print('Available methods:', [method for method in dir(client) if not method.startswith('_')])

except ImportError as e:
    print(f'❌ FAILED: {e}')
except Exception as e:
    print(f'⚠️  Instance creation failed (expected with test key): {e}')
"

echo "5. Verify no conflicts remain..."
docker exec -it vanilla-steel-ai-api python -c "
import sys
print('Checking for package conflicts...')

try:
    import unkey_py
    print('✅ unkey_py module found')
except ImportError:
    print('❌ unkey_py module missing')

try:
    import unkey
    print('⚠️  unkey module still present (should be removed)')
except ImportError:
    print('✅ unkey module properly removed')
"
