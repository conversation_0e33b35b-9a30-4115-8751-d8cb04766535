#!/bin/bash

############################################################################
# Deploy to Cloud Run with Correct Environment Variables
# This script deploys with the proper Cloud SQL configuration
############################################################################

set -e

# Configuration
PROJECT_ID="vs-data-439613"
SERVICE_NAME="vanilla-steel-ai-api-stg"
REGION="europe-west1"
IMAGE_REPO="europe-west1-docker.pkg.dev/vs-data-439613/vanilla-steel-ai"
IMAGE_NAME="vanilla-steel-ai"
IMAGE_TAG="stg"
SECRET_NAME="vanilla-steel-ai-staging-secrets"

echo "🚀 Deploying to Cloud Run with Correct Environment Variables"
echo "============================================================="

# Step 1: Download the secrets file
echo "📋 Step 1: Downloading secrets..."
gcloud secrets versions access latest --secret="$SECRET_NAME" --project="$PROJECT_ID" > /tmp/secrets.yml
echo "✅ Secrets downloaded"

# Step 2: Deploy with proper environment variables using env-vars-file
echo ""
echo "📋 Step 2: Deploying with proper configuration..."

gcloud run deploy "$SERVICE_NAME" \
    --image="$IMAGE_REPO/$IMAGE_NAME:$IMAGE_TAG" \
    --region="$REGION" \
    --project="$PROJECT_ID" \
    --env-vars-file="/tmp/secrets.yml" \
    --set-env-vars="RUNTIME_ENV=stg,DEBUG=true,WAIT_FOR_DB=true,MIGRATE_DB=true,WAIT_FOR_REDIS=false,DB_POOL_SIZE=5,DB_MAX_OVERFLOW=10,API_TIMEOUT=120,DB_HOST=/cloudsql/vs-data-439613:europe-west1:vs-ai-staging,DB_PORT=5432,POSTGRES_INIT_SCHEMA=postgres_stg,DEBUG_MODE=false,PERFORMANCE_MODE=true" \
    --allow-unauthenticated \
    --port=8000 \
    --memory=2Gi \
    --cpu=2 \
    --min-instances=0 \
    --max-instances=5 \
    --concurrency=80 \
    --timeout=900 \
    --add-cloudsql-instances=vs-data-439613:europe-west1:vs-ai-staging \
    --quiet

if [ $? -eq 0 ]; then
    echo "✅ Deployment succeeded!"
    
    # Get the service URL
    SERVICE_URL=$(gcloud run services describe "$SERVICE_NAME" --region="$REGION" --project="$PROJECT_ID" --format='value(status.url)')
    echo "🌐 Service URL: $SERVICE_URL"
    
    # Wait a bit for the service to be ready
    echo "⏳ Waiting for service to be ready..."
    sleep 30
    
    # Test the health endpoint
    echo "🧪 Testing health endpoint..."
    if curl -s -f "$SERVICE_URL/v1/health" >/dev/null 2>&1; then
        echo "✅ Health endpoint is responding!"
        echo "🎉 Deployment successful and service is healthy!"
    else
        echo "⚠️ Health endpoint not responding, checking logs..."
        
        # Check recent logs
        echo "📋 Recent logs:"
        gcloud logging read \
            "resource.type=\"cloud_run_revision\" AND resource.labels.service_name=\"$SERVICE_NAME\" AND timestamp>=\"$(date -u -v-5M +%Y-%m-%dT%H:%M:%SZ)\"" \
            --project="$PROJECT_ID" \
            --limit=20 \
            --format="table(timestamp,textPayload)"
    fi
    
else
    echo "❌ Deployment failed!"
    exit 1
fi

# Cleanup
rm -f /tmp/secrets.yml
echo ""
echo "✅ Deployment script completed!"
