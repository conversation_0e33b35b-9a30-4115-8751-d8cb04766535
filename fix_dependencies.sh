#!/bin/bash

# Fix Dependencies Script
# This script resolves the uv dependency resolution issues for CI/CD

echo "🔧 Fixing dependency resolution issues..."

# Remove existing lock file
echo "📝 Removing existing uv.lock file..."
rm -f uv.lock

# Regenerate lock file with Python version constraint
echo "🔄 Regenerating uv.lock with Python version constraints..."
uv lock --python-version 3.12

# Verify the lock file was created successfully
if [ -f "uv.lock" ]; then
    echo "✅ uv.lock regenerated successfully"
    echo "📊 Lock file info:"
    head -10 uv.lock
else
    echo "❌ Failed to regenerate uv.lock"
    exit 1
fi

# Test dependency installation
echo "🧪 Testing dependency installation..."
uv sync --all-groups --python-preference only-managed

if [ $? -eq 0 ]; then
    echo "✅ Dependencies installed successfully"
else
    echo "❌ Dependency installation failed"
    exit 1
fi

echo "🎉 Dependency resolution fixed!"
echo ""
echo "📋 Changes made:"
echo "  - Updated pyproject.toml: requires-python = '>=3.12,<3.13'"
echo "  - Added Python version constraint to types-aiohttp"
echo "  - Regenerated uv.lock file"
echo "  - Updated GitHub Actions workflow"
echo ""
echo "🚀 Your CI/CD should now work without dependency resolution errors!"
