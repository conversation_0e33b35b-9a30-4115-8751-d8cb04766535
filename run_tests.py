#!/usr/bin/env python3
"""
Simple Test Runner for Vanilla Steel AI
======================================

Quick and easy way to run tests with different configurations.

Usage:
    python run_tests.py                    # Run critical tests
    python run_tests.py --all              # Run all tests
    python run_tests.py --quick             # Run quick smoke tests
    python run_tests.py --coverage         # Run with coverage
    python run_tests.py --deployment       # Run deployment readiness tests
    python run_tests.py --unit             # Run only unit tests
    python run_tests.py --integration      # Run only integration tests
    python run_tests.py --e2e              # Run only e2e tests
"""

import argparse
import os
import subprocess
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Set test environment
os.environ['TESTING'] = 'True'
os.environ['RUNTIME_ENV'] = 'test'


def run_command(cmd: list, description: str) -> bool:
    """Run a command and return success status."""
    print(f"\n{'='*60}")
    print(f"🧪 {description}")
    print(f"{'='*60}")
    print(f"Command: {' '.join(cmd)}")
    print()
    
    try:
        result = subprocess.run(cmd, check=False, cwd=project_root)
        success = result.returncode == 0
        
        if success:
            print(f"\n✅ {description} - PASSED")
        else:
            print(f"\n❌ {description} - FAILED (exit code: {result.returncode})")
            
        return success
        
    except Exception as e:
        print(f"\n❌ {description} - ERROR: {e}")
        return False


def main():
    """Main test runner."""
    parser = argparse.ArgumentParser(description="Test Runner for Vanilla Steel AI")
    parser.add_argument("--all", action="store_true", help="Run all tests")
    parser.add_argument("--quick", action="store_true", help="Run quick smoke tests")
    parser.add_argument("--coverage", action="store_true", help="Run with coverage")
    parser.add_argument("--deployment", action="store_true", help="Run deployment readiness tests")
    parser.add_argument("--unit", action="store_true", help="Run only unit tests")
    parser.add_argument("--integration", action="store_true", help="Run only integration tests")
    parser.add_argument("--e2e", action="store_true", help="Run only e2e tests")
    parser.add_argument("--master", action="store_true", help="Use master test suite")
    parser.add_argument("-v", "--verbose", action="store_true", help="Verbose output")
    
    args = parser.parse_args()
    
    print("🧪 VANILLA STEEL AI - TEST RUNNER")
    print("="*60)
    print(f"Environment: {os.getenv('RUNTIME_ENV', 'unknown')}")
    print(f"Testing Mode: {os.getenv('TESTING', 'False')}")
    print(f"Python: {sys.executable}")
    print(f"Working Directory: {project_root}")
    
    success = True
    
    # Base pytest arguments
    pytest_args = ["python", "-m", "pytest"]
    if args.verbose:
        pytest_args.append("-v")
    else:
        pytest_args.extend(["-v", "--tb=short"])
    
    # Coverage arguments
    if args.coverage:
        pytest_args.extend([
            "--cov=api",
            "--cov=db", 
            "--cov=utils",
            "--cov=agents",
            "--cov=workflows",
            "--cov-report=term-missing",
            "--cov-report=html:htmlcov",
            "--cov-fail-under=70"
        ])
    
    if args.master:
        # Use master test suite
        master_args = ["python", "tests/master_test_suite.py"]
        if args.quick:
            master_args.append("--quick")
        elif args.deployment:
            master_args.append("--deployment-ready")
        elif args.all:
            master_args.append("--comprehensive")
        if args.coverage:
            master_args.append("--coverage")
            
        success = run_command(master_args, "MASTER TEST SUITE")
        
    elif args.quick:
        # Quick smoke tests
        quick_tests = [
            (pytest_args + ["tests/unit/test_imports.py"], "Import Tests"),
            (pytest_args + ["tests/unit/test_settings.py"], "Settings Tests"),
            (pytest_args + ["tests/e2e/test_complete_workflow.py::TestCompleteRFQWorkflow::test_health_check_workflow"], "Health Check"),
        ]
        
        for cmd, desc in quick_tests:
            if not run_command(cmd, desc):
                success = False
                break  # Stop on first failure for quick tests
                
    elif args.unit:
        # Unit tests only
        success = run_command(pytest_args + ["tests/unit/"], "UNIT TESTS")
        
    elif args.integration:
        # Integration tests only
        success = run_command(pytest_args + ["tests/integration/"], "INTEGRATION TESTS")
        
    elif args.e2e:
        # E2E tests only
        success = run_command(pytest_args + ["tests/e2e/", "-m", "not slow"], "E2E TESTS")
        
    elif args.deployment:
        # Deployment readiness tests
        deployment_tests = [
            (pytest_args + ["tests/unit/test_imports.py"], "Import Tests"),
            (pytest_args + ["tests/unit/test_settings.py"], "Settings Tests"),
            (pytest_args + ["tests/unit/test_extraction_retry.py"], "Extraction Retry Tests"),
            (pytest_args + ["tests/deployment/test_deployment_readiness.py"], "Deployment Readiness Tests"),
        ]
        
        for cmd, desc in deployment_tests:
            if not run_command(cmd, desc):
                success = False
                
    elif args.all:
        # All tests
        all_test_categories = [
            (pytest_args + ["tests/unit/"], "UNIT TESTS"),
            (pytest_args + ["tests/integration/"], "INTEGRATION TESTS"),
            (pytest_args + ["tests/deployment/"], "DEPLOYMENT TESTS"),
            (pytest_args + ["tests/e2e/", "-m", "not slow"], "E2E TESTS"),
        ]
        
        for cmd, desc in all_test_categories:
            if not run_command(cmd, desc):
                success = False
                
    else:
        # Default: Run critical tests
        critical_tests = [
            (pytest_args + ["tests/unit/test_imports.py"], "Import Tests"),
            (pytest_args + ["tests/unit/test_settings.py"], "Settings Tests"),
            (pytest_args + ["tests/unit/test_database_tools.py"], "Database Tools Tests"),
            (pytest_args + ["tests/unit/test_extraction_retry.py"], "Extraction Retry Tests"),
            (pytest_args + ["tests/deployment/test_deployment_readiness.py::TestDeploymentReadiness"], "Deployment Readiness"),
        ]
        
        for cmd, desc in critical_tests:
            if not run_command(cmd, desc):
                success = False
    
    # Final summary
    print(f"\n{'='*60}")
    print("📊 TEST EXECUTION SUMMARY")
    print(f"{'='*60}")
    
    if success:
        print("🎉 ALL TESTS PASSED!")
        print("✅ System is ready for deployment")
    else:
        print("❌ SOME TESTS FAILED!")
        print("⚠️  Review failures before deployment")
    
    print(f"{'='*60}")
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
