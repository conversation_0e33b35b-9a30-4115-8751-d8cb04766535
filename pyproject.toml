[project]
name = "vanilla-steel-ai"
version = "0.1.0"
requires-python = ">=3.12,<3.13"
readme = "README.md"
authors = [{ name = "Data Team", email = "<EMAIL>" }]

dependencies = [
    "agno[aws]==1.4.2",
    "aiofiles",
    "aiohttp",
    "alembic",
    "asyncpg",
    "beautifulsoup4",
    "duckduckgo-search",
    "fastapi[standard]",
    "langwatch>=0.0.13",
    "mypy_extensions==1.0.0",
    "nest_asyncio",
    "openai",
    "pgvector",
    "psutil",
    "psycopg[binary]",
    "psycopg2-binary",
    "pydantic",
    "pypdf",
    "python-docx",
    "python-dotenv",
    "rich",
    "sqlalchemy",
    "streamlit",
    "tiktoken",
    "tokenize-rt",
    "typer",
    "typing-inspect==0.9.0",
    "unkey.py==0.9.0",
    "uv",
]

[project.optional-dependencies]
dev = [
    "mypy",
    "pytest>=8.0.0",
    "pytest-asyncio",
    "pytest-cov",
    "pytest-mock",
    "ruff",
    "types-requests",
    "types-sqlalchemy",
    "pre-commit",
]

[build-system]
requires = ["setuptools"]
build-backend = "setuptools.build_meta"

[tool.setuptools.packages.find]

# Change this value to use a different directory for the agno workspace.
# [tool.agno]
# workspace = "workspace"

[tool.ruff]
target-version = "py312"
line-length = 150
exclude = [
    ".venv",
    "venv",
    "env",
    "__pycache__",
    ".local",
    "site-packages",
    "tests",
]

[tool.ruff.lint.per-file-ignores]
# Ignore `F401` (import violations) in all `__init__.py` files
"__init__.py" = ["F401", "F403"]

[tool.ruff.format]
quote-style = "double"
docstring-code-format = true
docstring-code-line-length = 150
indent-style = "space"
line-ending = "auto"


[tool.ruff.lint]
select = [
    # pycodestyle
    "E",
    # Pyflakes
    "F",
    # pyupgrade
    "UP",
    # flake8-bugbear
    "B",
    # flake8-simplify
    "SIM",
    # isort
    "I",
]
# Explicitly ignore rules that conflict with the formatter
ignore = [
    # Indentation rules that conflict with tab-based formatting
    "E111", # indentation-with-invalid-multiple
    "E114", # indentation-with-invalid-multiple-comment
    "E117", # over-indented
    # Other rules that conflict with formatter
    "W191", # tab-indentation (in case W rules are enabled in the future)
]

[tool.mypy]
check_untyped_defs = true
no_implicit_optional = true
warn_unused_configs = true
plugins = ["pydantic.mypy"]
exclude = [".venv*", ".local*", "site-packages*"]
python_version = "3.12"
namespace_packages = true
explicit_package_bases = true

[[tool.mypy.overrides]]
module = [
    "agno.*",
    "aiohttp.*",
    "alembic.*",
    "asyncpg.*",
    "dotenv.*",
    "fastapi.*",
    "nest_asyncio.*",
    "pgvector.*",
    "psutil.*",
    "psycopg2.*",
    "pydantic_settings.*",
    "rich.*",
    "setuptools.*",
    "sqlalchemy.*",
    "starlette.*",
    "unkey_py.*",
]
ignore_missing_imports = true

[tool.uv.pip]
no-annotate = true

[tool.pytest.ini_options]
log_cli = true
log_cli_level = "INFO"
log_cli_format = "%(asctime)s [%(levelname)s] %(message)s"
log_cli_date_format = "%Y-%m-%d %H:%M:%S"
addopts = "--strict-markers --tb=short"
testpaths = ["tests"]
python_files = "test_*.py"
python_classes = "Test*"
python_functions = "test_*"
markers = [
    "unit: Unit tests",
    "integration: Integration tests",
    "e2e: End-to-end tests",
    "slow: Tests that take a long time to run",
    "external: Tests that require external services",
]
asyncio_mode = "auto"

[tool.commitizen]
name = "cz_customize"
version = "0.1.0"

[tool.commitizen.customize]
message_template = "{ticket} {message}"
example = "AI-10 Enhance pre-commit configuration and add Commitizen support"
schema_pattern = "^([A-Z]+-\\d+)\\s+.+"

[[tool.commitizen.customize.questions]]
type = "input"
name = "ticket"
message = "Jira ticket (e.g. AI-10):"
validate = "^[A-Z]+-\\d+$"

[[tool.commitizen.customize.questions]]
type = "input"
name = "message"
message = "Commit message:"

[dependency-groups]
dev = ["mypy>=1.15.0", "pre-commit>=4.2.0", "pytest>=8.3.5", "ruff>=0.11.6"]
