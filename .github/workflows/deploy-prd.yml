name: Deploy to Cloud Run (Production)

on:
  push:
    branches:
      - main
  workflow_dispatch:
    inputs:
      deploy_message:
        description: 'Deployment Production (Manual)'
        required: false
        default: 'Manual deployment to production'

env:
  PROJECT_ID: vs-data-439613
  SERVICE_NAME_API: vanilla-steel-ai-api
  SERVICE_NAME_UI: vanilla-steel-ai-ui
  REGION: europe-west1
  IMAGE_REPO: europe-west1-docker.pkg.dev/vs-data-439613/vanilla-steel-ai
  IMAGE_NAME: vanilla-steel-ai
  IMAGE_TAG: prd
  GCP_SECRET_NAME: vanilla-steel-ai-production-secrets

permissions:
  contents: read
  id-token: write

jobs:
  deploy-production:
    name: Deploy API and UI to Production Cloud Run
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'

      - name: Install uv
        uses: astral-sh/setup-uv@v6
        with:
          enable-cache: true
          cache-dependency-glob: "requirements**.txt"

      - name: Install dependencies
        run: |
          uv venv .venv --python 3.12
          uv sync --all-groups

      - name: Generate version
        id: version
        run: |
          echo "version=$(date +'%Y%m%d%H%M%S')-$(git rev-parse --short "HEAD")" >> "$GITHUB_OUTPUT"

      - name: Google Auth
        id: auth
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2
        with:
          project_id: ${{ env.PROJECT_ID }}

      - name: Download GCP Secret to yml file
        run: |
          echo "Attempting to download secret ${{ env.GCP_SECRET_NAME }}..."
          gcloud secrets versions access latest --secret="${{ env.GCP_SECRET_NAME }}" --project="${{ env.PROJECT_ID }}" > secrets.yml
          echo "Secret downloaded to secrets.yml file."
          if [ -s secrets.yml ]; then
            echo "secrets.yml created successfully."
          else
            echo "ERROR: secrets.yml is empty or was not created."
            exit 1
          fi

      - name: Configure Docker for Artifact Registry
        run: |
          gcloud auth configure-docker ${{ env.REGION }}-docker.pkg.dev --quiet

      - name: Build and Push Docker image
        uses: docker/build-push-action@v6
        with:
          context: .
          platforms: linux/amd64
          push: true
          tags: |
            ${{ env.IMAGE_REPO }}/${{ env.IMAGE_NAME }}:${{ steps.version.outputs.version }}
            ${{ env.IMAGE_REPO }}/${{ env.IMAGE_NAME }}:${{ env.IMAGE_TAG }}

      - name: Deploy API to Cloud Run (Complete configuration)
        uses: google-github-actions/deploy-cloudrun@v2
        with:
          service: ${{ env.SERVICE_NAME_API }}
          region: ${{ env.REGION }}
          image: ${{ env.IMAGE_REPO }}/${{ env.IMAGE_NAME }}:${{ steps.version.outputs.version }}
          env_vars_file: secrets.yml
          env_vars: |-
            RUNTIME_ENV=prd
            DEBUG=false
            WAIT_FOR_DB=true
            MIGRATE_DB=true
            WAIT_FOR_REDIS=false
            DB_POOL_SIZE=10
            DB_MAX_OVERFLOW=20
            API_TIMEOUT=120
          flags: |
            --allow-unauthenticated
            --port=8000
            --memory=3Gi
            --cpu=2
            --min-instances=1
            --max-instances=10
            --concurrency=100
            --timeout=1200
            --command=uv
            --args=run,uvicorn,api.main:app,--host,0.0.0.0,--port,8000

      - name: Deploy UI to Cloud Run (Complete configuration)
        uses: google-github-actions/deploy-cloudrun@v2
        with:
          service: ${{ env.SERVICE_NAME_UI }}
          region: ${{ env.REGION }}
          image: ${{ env.IMAGE_REPO }}/${{ env.IMAGE_NAME }}:${{ steps.version.outputs.version }}
          env_vars_file: secrets.yml
          env_vars: |-
            RUNTIME_ENV=prd
            DEBUG=false
            WAIT_FOR_DB=true
            MIGRATE_DB=false
            WAIT_FOR_REDIS=false
            STREAMLIT_SERVER_HEADLESS=true
            PRINT_ENV_ON_LOAD=false
            PYTHONPATH=/app
          flags: |
            --allow-unauthenticated
            --port=8501
            --memory=3Gi
            --cpu=2
            --min-instances=1
            --max-instances=5
            --concurrency=100
            --timeout=1200
            --command=streamlit
            --args=run,ui/Home.py

      - name: Deployment success notification
        if: success()
        run: |
          echo "🚀 Successfully deployed to Cloud Run Production"
          echo "UI Service URL: $(gcloud run services describe ${{ env.SERVICE_NAME_UI }} --region=${{ env.REGION }} --format='value(status.url)')"
          echo "API Service URL: $(gcloud run services describe ${{ env.SERVICE_NAME_API }} --region=${{ env.REGION }} --format='value(status.url)')"
          echo "Deployed Version: ${{ steps.version.outputs.version }}"

      - name: Deployment failure notification
        if: failure()
        run: |
          echo "❌ Deployment to Cloud Run Production failed."
          echo "Check the logs for details."
