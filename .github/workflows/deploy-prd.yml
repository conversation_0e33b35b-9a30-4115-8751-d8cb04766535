name: Deploy to Cloud Run (Production)

on:
  push:
    branches:
      - main
  workflow_dispatch:
    inputs:
      deploy_message:
        description: 'Deployment Production'
        required: false
        default: 'Manual deployment to production'
      deployment_strategy:
        description: 'Deployment Strategy'
        required: false
        default: 'blue-green'
        type: choice
        options:
          - 'blue-green'
          - 'direct'
          - 'canary'
      canary_percentage:
        description: 'Initial canary traffic percentage (if canary selected)'
        required: false
        default: '10'
        type: choice
        options:
          - '5'
          - '10'
          - '25'
          - '50'
      debug_mode:
        description: 'Enable debug mode for agents'
        required: false
        default: 'false'
        type: choice
        options:
          - 'true'
          - 'false'
      performance_mode:
        description: 'Enable performance mode for agents'
        required: false
        default: 'true'
        type: choice
        options:
          - 'true'
          - 'false'

env:
  PROJECT_ID: vs-data-439613
  SERVICE_NAME_API: vanilla-steel-ai-api
  SERVICE_NAME_UI: vanilla-steel-ai-ui
  REGION: europe-west1
  IMAGE_REPO: europe-west1-docker.pkg.dev/vs-data-439613/vanilla-steel-ai
  IMAGE_NAME: vanilla-steel-ai
  IMAGE_TAG: prd
  GCP_SECRET_NAME: vanilla-steel-ai-production-secrets

permissions:
  contents: read
  id-token: write

jobs:
  deploy-production:
    name: Deploy API and UI to Production Cloud Run
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'

      - name: Install uv
        uses: astral-sh/setup-uv@v6
        with:
          enable-cache: true
          cache-dependency-glob: "requirements**.txt"

      - name: Install dependencies
        run: |
          uv venv .venv --python 3.12
          uv sync --all-groups

      - name: Generate version
        id: version
        run: |
          echo "version=$(date +'%Y%m%d%H%M%S')-$(git rev-parse --short "HEAD")" >> "$GITHUB_OUTPUT"

      - name: Google Auth
        id: auth
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2
        with:
          project_id: ${{ env.PROJECT_ID }}

      - name: Download GCP Secret to yml file
        run: |
          echo "Attempting to download secret ${{ env.GCP_SECRET_NAME }}..."
          gcloud secrets versions access latest --secret="${{ env.GCP_SECRET_NAME }}" --project="${{ env.PROJECT_ID }}" > secrets.yml
          echo "Secret downloaded to secrets.yml file."
          if [ -s secrets.yml ]; then
            echo "secrets.yml created successfully."
          else
            echo "ERROR: secrets.yml is empty or was not created."
            exit 1
          fi

      - name: Configure Docker for Artifact Registry
        run: |
          gcloud auth configure-docker ${{ env.REGION }}-docker.pkg.dev --quiet

      - name: Show Docker build context and source code
        run: |
          echo "🔨 Building Docker image from source code..."
          echo "📁 Build context: $(pwd)"
          echo "🐳 Using Dockerfile: $(pwd)/Dockerfile"
          echo ""
          echo "📋 Source code files being included in Docker build:"
          ls -la
          echo ""
          echo "🔍 Dockerfile content:"
          head -10 Dockerfile
          echo "..."
          echo ""
          echo "🏷️ Image tags to be created:"
          echo "  - ${{ env.IMAGE_REPO }}/${{ env.IMAGE_NAME }}:${{ steps.version.outputs.version }}"
          echo "  - ${{ env.IMAGE_REPO }}/${{ env.IMAGE_NAME }}:${{ env.IMAGE_TAG }}"
          echo "  - ${{ env.IMAGE_REPO }}/${{ env.IMAGE_NAME }}:latest"

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build and Push Docker image to Artifact Registry
        uses: docker/build-push-action@v5
        with:
          context: .  # Use current directory (contains Dockerfile and source code)
          file: ./Dockerfile  # Explicitly specify Dockerfile location
          push: true
          platforms: linux/amd64,linux/arm64  # Build for both platforms
          tags: |
            ${{ env.IMAGE_REPO }}/${{ env.IMAGE_NAME }}:${{ steps.version.outputs.version }}
            ${{ env.IMAGE_REPO }}/${{ env.IMAGE_NAME }}:${{ env.IMAGE_TAG }}
            ${{ env.IMAGE_REPO }}/${{ env.IMAGE_NAME }}:latest
          labels: |
            org.opencontainers.image.source=${{ github.repositoryUrl }}
            org.opencontainers.image.revision=${{ github.sha }}
            org.opencontainers.image.created=${{ steps.version.outputs.version }}

      - name: Verify Docker image was built and pushed
        run: |
          echo "✅ Docker image built and pushed successfully!"
          echo "Image: ${{ env.IMAGE_REPO }}/${{ env.IMAGE_NAME }}:${{ steps.version.outputs.version }}"
          gcloud container images list --repository=${{ env.IMAGE_REPO }} --filter="name:${{ env.IMAGE_NAME }}" --limit=5

      - name: Get current production revision (for rollback)
        id: current_revision
        run: |
          # Get current revision serving 100% traffic for API
          CURRENT_API_REVISION=$(gcloud run services describe ${{ env.SERVICE_NAME_API }} \
            --region=${{ env.REGION }} \
            --format='value(status.traffic[0].revisionName)' 2>/dev/null || echo "none")
          echo "current_api_revision=$CURRENT_API_REVISION" >> "$GITHUB_OUTPUT"

          # Get current revision serving 100% traffic for UI
          CURRENT_UI_REVISION=$(gcloud run services describe ${{ env.SERVICE_NAME_UI }} \
            --region=${{ env.REGION }} \
            --format='value(status.traffic[0].revisionName)' 2>/dev/null || echo "none")
          echo "current_ui_revision=$CURRENT_UI_REVISION" >> "$GITHUB_OUTPUT"

          echo "📌 Current Production Revisions:"
          echo "   API: $CURRENT_API_REVISION"
          echo "   UI: $CURRENT_UI_REVISION"

      # Blue-Green Deployment for API
      - name: Deploy API to Cloud Run (Blue-Green)
        if: ${{ github.event.inputs.deployment_strategy != 'direct' }}
        uses: google-github-actions/deploy-cloudrun@v2
        with:
          service: ${{ env.SERVICE_NAME_API }}
          region: ${{ env.REGION }}
          image: ${{ env.IMAGE_REPO }}/${{ env.IMAGE_NAME }}:${{ env.IMAGE_TAG }}
          env_vars_file: secrets.yml
          env_vars: |-
            RUNTIME_ENV=prd
            DEBUG=false
            WAIT_FOR_DB=true
            MIGRATE_DB=true
            WAIT_FOR_REDIS=false
            DB_POOL_SIZE=10
            DB_MAX_OVERFLOW=20
            API_TIMEOUT=120
            DB_HOST=/cloudsql/vs-data-439613:europe-west1:vs-ai-production
            DB_PORT=5432
            POSTGRES_INIT_SCHEMA=public
            DEBUG_MODE=${{ github.event.inputs.debug_mode || 'false' }}
            PERFORMANCE_MODE=${{ github.event.inputs.performance_mode || 'true' }}
          flags: |
            --allow-unauthenticated
            --port=8000
            --memory=3Gi
            --cpu=2
            --min-instances=1
            --max-instances=10
            --concurrency=100
            --timeout=1200
            --add-cloudsql-instances=vs-data-439613:europe-west1:vs-ai-production
            --tag=green
            --no-traffic

      # Direct Deployment for API (fallback)
      - name: Deploy API to Cloud Run (Direct)
        if: ${{ github.event.inputs.deployment_strategy == 'direct' }}
        uses: google-github-actions/deploy-cloudrun@v2
        with:
          service: ${{ env.SERVICE_NAME_API }}
          region: ${{ env.REGION }}
          image: ${{ env.IMAGE_REPO }}/${{ env.IMAGE_NAME }}:${{ env.IMAGE_TAG }}
          env_vars_file: secrets.yml
          env_vars: |-
            RUNTIME_ENV=prd
            DEBUG=false
            WAIT_FOR_DB=true
            MIGRATE_DB=true
            WAIT_FOR_REDIS=false
            DB_POOL_SIZE=10
            DB_MAX_OVERFLOW=20
            API_TIMEOUT=120
            DB_HOST=/cloudsql/vs-data-439613:europe-west1:vs-ai-production
            DB_PORT=5432
            POSTGRES_INIT_SCHEMA=public
            DEBUG_MODE=${{ github.event.inputs.debug_mode || 'false' }}
            PERFORMANCE_MODE=${{ github.event.inputs.performance_mode || 'true' }}
          flags: |
            --allow-unauthenticated
            --port=8000
            --memory=3Gi
            --cpu=2
            --min-instances=1
            --max-instances=10
            --concurrency=100
            --timeout=1200
            --add-cloudsql-instances=vs-data-439613:europe-west1:vs-ai-production

      # Blue-Green Deployment for UI
      - name: Deploy UI to Cloud Run (Blue-Green)
        if: ${{ github.event.inputs.deployment_strategy != 'direct' }}
        uses: google-github-actions/deploy-cloudrun@v2
        with:
          service: ${{ env.SERVICE_NAME_UI }}
          region: ${{ env.REGION }}
          image: ${{ env.IMAGE_REPO }}/${{ env.IMAGE_NAME }}:${{ env.IMAGE_TAG }}
          env_vars_file: secrets.yml
          env_vars: |-
            RUNTIME_ENV=prd
            DEBUG=false
            WAIT_FOR_DB=true
            MIGRATE_DB=false
            WAIT_FOR_REDIS=false
            STREAMLIT_SERVER_HEADLESS=true
            PRINT_ENV_ON_LOAD=false
            PYTHONPATH=/app
            DEBUG_MODE=${{ github.event.inputs.debug_mode || 'false' }}
            PERFORMANCE_MODE=${{ github.event.inputs.performance_mode || 'false' }}
          flags: |
            --allow-unauthenticated
            --port=8501
            --memory=3Gi
            --cpu=2
            --min-instances=1
            --max-instances=5
            --concurrency=100
            --timeout=1200
            --add-cloudsql-instances=vs-data-439613:europe-west1:vs-ai-production
            --tag=green
            --no-traffic

      # Direct Deployment for UI (fallback)
      - name: Deploy UI to Cloud Run (Direct)
        if: ${{ github.event.inputs.deployment_strategy == 'direct' }}
        uses: google-github-actions/deploy-cloudrun@v2
        with:
          service: ${{ env.SERVICE_NAME_UI }}
          region: ${{ env.REGION }}
          image: ${{ env.IMAGE_REPO }}/${{ env.IMAGE_NAME }}:${{ env.IMAGE_TAG }}
          env_vars_file: secrets.yml
          env_vars: |-
            RUNTIME_ENV=prd
            DEBUG=false
            WAIT_FOR_DB=true
            MIGRATE_DB=false
            WAIT_FOR_REDIS=false
            STREAMLIT_SERVER_HEADLESS=true
            PRINT_ENV_ON_LOAD=false
            PYTHONPATH=/app
            DEBUG_MODE=${{ github.event.inputs.debug_mode || 'false' }}
            PERFORMANCE_MODE=${{ github.event.inputs.performance_mode || 'false' }}
          flags: |
            --allow-unauthenticated
            --port=8501
            --memory=3Gi
            --cpu=2
            --min-instances=1
            --max-instances=5
            --concurrency=100
            --timeout=1200
            --add-cloudsql-instances=vs-data-439613:europe-west1:vs-ai-production

      # Test green deployment
      - name: Test green deployment
        if: ${{ github.event.inputs.deployment_strategy != 'direct' }}
        id: test_green
        run: |
          echo "⏳ Waiting for green deployment to be ready..."
          sleep 30

          # Get green revision URLs
          API_GREEN_URL=$(gcloud run services describe "${{ env.SERVICE_NAME_API }}" \
            --region="${{ env.REGION }}" \
            --format='value(status.address.url)')
          UI_GREEN_URL=$(gcloud run services describe "${{ env.SERVICE_NAME_UI }}" \
            --region="${{ env.REGION }}" \
            --format='value(status.address.url)')

          echo "API_GREEN_URL=$API_GREEN_URL" >> "$GITHUB_OUTPUT"
          echo "UI_GREEN_URL=$UI_GREEN_URL" >> "$GITHUB_OUTPUT"

          echo "🧪 Testing green API deployment..."
          # Test with the green tag URL
          if curl -s -f -w "\nHTTP Status: %{http_code}\n" "$API_GREEN_URL/v1/health" -H "Authorization: Bearer $(gcloud auth print-identity-token)"; then
            echo "✅ Green API deployment is healthy"
          else
            echo "❌ Green API deployment health check failed"
            exit 1
          fi

          echo "🧪 Testing green UI deployment..."
          if curl -s -f -w "\nHTTP Status: %{http_code}\n" "$UI_GREEN_URL" -H "Authorization: Bearer $(gcloud auth print-identity-token)" | head -20; then
            echo "✅ Green UI deployment is healthy"
          else
            echo "❌ Green UI deployment health check failed"
            exit 1
          fi

      # Traffic Management based on strategy
      - name: Manage Traffic (Blue-Green)
        if: ${{ github.event.inputs.deployment_strategy == 'blue-green' }}
        run: |
          echo "🔄 Switching traffic to green deployment..."

          # Switch API traffic to green
          gcloud run services update-traffic "${{ env.SERVICE_NAME_API }}" \
            --region="${{ env.REGION }}" \
            --to-tags=green=100

          # Switch UI traffic to green
          gcloud run services update-traffic "${{ env.SERVICE_NAME_UI }}" \
            --region="${{ env.REGION }}" \
            --to-tags=green=100

          echo "✅ Traffic switched to green deployment"

      - name: Manage Traffic (Canary)
        if: ${{ github.event.inputs.deployment_strategy == 'canary' }}
        run: |
          CANARY_PERCENT="${{ github.event.inputs.canary_percentage || '10' }}"
          echo "🐤 Starting canary deployment with ${CANARY_PERCENT}% traffic..."

          # Route canary traffic to API
          gcloud run services update-traffic "${{ env.SERVICE_NAME_API }}" \
            --region="${{ env.REGION }}" \
            --to-tags=green=${CANARY_PERCENT}

          # Route canary traffic to UI
          gcloud run services update-traffic "${{ env.SERVICE_NAME_UI }}" \
            --region="${{ env.REGION }}" \
            --to-tags=green=${CANARY_PERCENT}

          echo "✅ Canary deployment active with ${CANARY_PERCENT}% traffic"
          echo ""
          echo "📊 Monitor canary performance and manually increase traffic:"
          echo "   gcloud run services update-traffic ${{ env.SERVICE_NAME_API }} --region=${{ env.REGION }} --to-tags=green=50"
          echo "   gcloud run services update-traffic ${{ env.SERVICE_NAME_API }} --region=${{ env.REGION }} --to-tags=green=100"

      - name: Tag stable release
        if: success()
        run: |
          # Tag the current version as stable for easy rollback
          gcloud container images add-tag \
            "${{ env.IMAGE_REPO }}/${{ env.IMAGE_NAME }}:${{ steps.version.outputs.version }}" \
            "${{ env.IMAGE_REPO }}/${{ env.IMAGE_NAME }}:stable-${{ steps.version.outputs.version }}"

          # Update last-known-good tag
          gcloud container images add-tag \
            "${{ env.IMAGE_REPO }}/${{ env.IMAGE_NAME }}:${{ steps.version.outputs.version }}" \
            "${{ env.IMAGE_REPO }}/${{ env.IMAGE_NAME }}:last-known-good"

      - name: Test final deployment
        id: test_final
        run: |
          echo "⏳ Waiting for traffic routing to stabilize..."
          sleep 30

          API_URL=$(gcloud run services describe "${{ env.SERVICE_NAME_API }}" --region="${{ env.REGION }}" --format='value(status.url)')
          UI_URL=$(gcloud run services describe "${{ env.SERVICE_NAME_UI }}" --region="${{ env.REGION }}" --format='value(status.url)')

          echo "API_URL=$API_URL" >> "$GITHUB_OUTPUT"
          echo "UI_URL=$UI_URL" >> "$GITHUB_OUTPUT"

          echo "🧪 Testing production API endpoint..."
          curl -s -f -w "\nHTTP Status: %{http_code}\n" "$API_URL/v1/health" || {
            echo "⚠️ API health check failed, checking logs..."
            gcloud logging read \
              "resource.type=\"cloud_run_revision\" AND resource.labels.service_name=\"${{ env.SERVICE_NAME_API }}\" AND severity>=ERROR" \
              --project="${{ env.PROJECT_ID }}" \
              --limit=50 \
              --format="table(timestamp,textPayload)"
          }

          echo "🧪 Testing production UI endpoint..."
          curl -s -f -w "\nHTTP Status: %{http_code}\n" "$UI_URL" | head -20 || {
            echo "⚠️ UI check failed, checking logs..."
            gcloud logging read \
              "resource.type=\"cloud_run_revision\" AND resource.labels.service_name=\"${{ env.SERVICE_NAME_UI }}\" AND severity>=ERROR" \
              --project="${{ env.PROJECT_ID }}" \
              --limit=50 \
              --format="table(timestamp,textPayload)"
          }

      - name: Deployment success summary
        if: success()
        run: |
          echo "🚀 Successfully deployed to Cloud Run Production"
          echo ""
          echo "📊 Deployment Summary:"
          echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
          echo "🔖 Version: ${{ steps.version.outputs.version }}"
          echo "🏷️ Image Tag: ${{ env.IMAGE_TAG }}"
          echo "📦 Strategy: ${{ github.event.inputs.deployment_strategy || 'blue-green' }}"
          echo ""
          echo "🌐 Service URLs:"
          echo "   API:  ${{ steps.test_final.outputs.API_URL }}"
          echo "   UI:   ${{ steps.test_final.outputs.UI_URL }}"
          echo ""
          echo "📚 API Documentation: ${{ steps.test_final.outputs.API_URL }}/docs"
          echo ""
          echo "🔄 Rollback Commands (if needed):"
          echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
          if [[ "${{ steps.current_revision.outputs.current_api_revision }}" != "none" ]]; then
            echo "API Rollback:"
            echo "  gcloud run services update-traffic ${{ env.SERVICE_NAME_API }} \\"
            echo "    --region=${{ env.REGION }} \\"
            echo "    --to-revisions=${{ steps.current_revision.outputs.current_api_revision }}=100"
          fi
          if [[ "${{ steps.current_revision.outputs.current_ui_revision }}" != "none" ]]; then
            echo ""
            echo "UI Rollback:"
            echo "  gcloud run services update-traffic ${{ env.SERVICE_NAME_UI }} \\"
            echo "    --region=${{ env.REGION }} \\"
            echo "    --to-revisions=${{ steps.current_revision.outputs.current_ui_revision }}=100"
          fi
          echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

      - name: Create rollback issue on failure
        if: failure()
        uses: actions/create-issue@v2
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          title: "🚨 Production Deployment Failed - ${{ steps.version.outputs.version }}"
          body: |
            ## Production Deployment Failed

            **Version:** ${{ steps.version.outputs.version }}
            **Time:** ${{ github.event.repository.updated_at }}
            **Triggered by:** ${{ github.actor }}
            **Strategy:** ${{ github.event.inputs.deployment_strategy || 'blue-green' }}

            ### Rollback Commands

            ```bash
            # API Rollback
            gcloud run services update-traffic ${{ env.SERVICE_NAME_API }} \
              --region=${{ env.REGION }} \
              --to-revisions=${{ steps.current_revision.outputs.current_api_revision }}=100

            # UI Rollback
            gcloud run services update-traffic ${{ env.SERVICE_NAME_UI }} \
              --region=${{ env.REGION }} \
              --to-revisions=${{ steps.current_revision.outputs.current_ui_revision }}=100
            ```

            ### Investigation Steps
            1. Check the [workflow run](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }})
            2. Review Cloud Run logs
            3. Check monitoring dashboards

            cc: @DataTeam
          labels: deployment-failure,production,urgent

      - name: Deployment failure notification
        if: failure()
        run: |
          echo "❌ Deployment to Cloud Run Production failed."
          echo ""
          echo "🔍 Troubleshooting steps:"
          echo "1. Check the logs above for specific error messages"
          echo "2. Verify the Docker image was built correctly"
          echo "3. Check Cloud Run logs:"
          echo "   gcloud logging read \"resource.type=\\\"cloud_run_revision\\\" AND (resource.labels.service_name=\\\"${{ env.SERVICE_NAME_API }}\\\" OR resource.labels.service_name=\\\"${{ env.SERVICE_NAME_UI }}\\\")\" --project=\"${{ env.PROJECT_ID }}\" --limit=100"
          echo "4. Ensure all required environment variables are set in secrets"
          echo ""
          echo "🔄 Quick Rollback:"
          if [[ "${{ steps.current_revision.outputs.current_api_revision }}" != "none" ]]; then
            echo "gcloud run services update-traffic ${{ env.SERVICE_NAME_API }} --region=${{ env.REGION }} --to-revisions=${{ steps.current_revision.outputs.current_api_revision }}=100"
          fi
          if [[ "${{ steps.current_revision.outputs.current_ui_revision }}" != "none" ]]; then
            echo "gcloud run services update-traffic ${{ env.SERVICE_NAME_UI }} --region=${{ env.REGION }} --to-revisions=${{ steps.current_revision.outputs.current_ui_revision }}=100"
          fi
