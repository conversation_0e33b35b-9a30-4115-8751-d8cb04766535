name: Pre-commit Checks

on:
  pull_request:
    branches: ['*']
  push:
    branches:
      - 'main'
      - 'staging'

# Cancel in-progress runs on new commits to the same PR
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  pre-commit:
    runs-on: ubuntu-latest
    timeout-minutes: 10
    strategy:
      matrix:
        python-version: ["3.12"]
      fail-fast: false

    steps:
      - name: Checkout code
        uses: actions/checkout@v4.2.2
        with:
          fetch-depth: 0

      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5.5.0
        with:
          python-version: ${{ matrix.python-version }}
          cache: 'pip'

      - name: Install uv
        uses: astral-sh/setup-uv@v3
        with:
          enable-cache: true
          cache-dependency-glob: "requirements**.txt"

      - name: Create VENV and install dependencies
        run: |
          uv venv .venv --python ${{ matrix.python-version }}
          uv sync --all-groups --python-preference only-managed

      - name: Run pre-commit
        run: |
          .venv/bin/pre-commit run --all-files --show-diff-on-failure
