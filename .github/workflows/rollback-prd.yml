name: Rollback Production

on:
  workflow_dispatch:
    inputs:
      rollback_target:
        description: 'Rollback target'
        required: true
        type: choice
        options:
          - 'previous'
          - 'specific-revision'
          - 'last-known-good'
      api_revision:
        description: 'API revision name (if specific-revision selected)'
        required: false
      ui_revision:
        description: 'UI revision name (if specific-revision selected)'
        required: false
      services:
        description: 'Services to rollback'
        required: true
        type: choice
        default: 'both'
        options:
          - 'api'
          - 'ui'
          - 'both'
      reason:
        description: 'Reason for rollback'
        required: true

env:
  PROJECT_ID: vs-data-439613
  SERVICE_NAME_API: vanilla-steel-ai-api
  SERVICE_NAME_UI: vanilla-steel-ai-ui
  REGION: europe-west1
  IMAGE_REPO: europe-west1-docker.pkg.dev/vs-data-439613/vanilla-steel-ai
  IMAGE_NAME: vanilla-steel-ai
  GCP_SECRET_NAME: vanilla-steel-ai-production-secrets

permissions:
  contents: read
  id-token: write
  issues: write

jobs:
  rollback:
    name: Rollback Production Services
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Google Auth
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2
        with:
          project_id: ${{ env.PROJECT_ID }}

      - name: Get current revisions
        id: current
        run: |
          # Get current revisions
          CURRENT_API=$(gcloud run services describe ${{ env.SERVICE_NAME_API }} \
            --region=${{ env.REGION }} \
            --format='value(status.traffic[0].revisionName)')
          CURRENT_UI=$(gcloud run services describe ${{ env.SERVICE_NAME_UI }} \
            --region=${{ env.REGION }} \
            --format='value(status.traffic[0].revisionName)')

          echo "current_api=$CURRENT_API" >> "$GITHUB_OUTPUT"
          echo "current_ui=$CURRENT_UI" >> "$GITHUB_OUTPUT"

          echo "📌 Current Production Revisions:"
          echo "   API: $CURRENT_API"
          echo "   UI: $CURRENT_UI"

      - name: Determine rollback targets
        id: targets
        run: |
          case "${{ github.event.inputs.rollback_target }}" in
            "previous")
              # Get previous revision (second in the list)
              if [[ "${{ github.event.inputs.services }}" == "api" || "${{ github.event.inputs.services }}" == "both" ]]; then
                API_TARGET=$(gcloud run revisions list \
                  --service=${{ env.SERVICE_NAME_API }} \
                  --region=${{ env.REGION }} \
                  --format='value(name)' \
                  --limit=2 | tail -n 1)
                echo "api_target=$API_TARGET" >> "$GITHUB_OUTPUT"
              fi

              if [[ "${{ github.event.inputs.services }}" == "ui" || "${{ github.event.inputs.services }}" == "both" ]]; then
                UI_TARGET=$(gcloud run revisions list \
                  --service=${{ env.SERVICE_NAME_UI }} \
                  --region=${{ env.REGION }} \
                  --format='value(name)' \
                  --limit=2 | tail -n 1)
                echo "ui_target=$UI_TARGET" >> "$GITHUB_OUTPUT"
              fi
              ;;

            "specific-revision")
              if [[ -n "${{ github.event.inputs.api_revision }}" ]]; then
                echo "api_target=${{ github.event.inputs.api_revision }}" >> "$GITHUB_OUTPUT"
              fi
              if [[ -n "${{ github.event.inputs.ui_revision }}" ]]; then
                echo "ui_target=${{ github.event.inputs.ui_revision }}" >> "$GITHUB_OUTPUT"
              fi
              ;;

            "last-known-good")
              # Deploy using last-known-good image tag
              echo "use_lkg_image=true" >> "$GITHUB_OUTPUT"
              ;;
          esac

      - name: Rollback API Service
        if: ${{ (github.event.inputs.services == 'api' || github.event.inputs.services == 'both') && github.event.inputs.rollback_target != 'last-known-good' }}
        run: |
          API_TARGET="${{ steps.targets.outputs.api_target }}"
          if [[ -n "$API_TARGET" ]]; then
            echo "🔄 Rolling back API to revision: $API_TARGET"
            gcloud run services update-traffic ${{ env.SERVICE_NAME_API }} \
              --region=${{ env.REGION }} \
              --to-revisions="$API_TARGET=100"
            echo "✅ API rollback completed"
          else
            echo "❌ No API revision target found"
            exit 1
          fi

      - name: Rollback UI Service
        if: ${{ (github.event.inputs.services == 'ui' || github.event.inputs.services == 'both') && github.event.inputs.rollback_target != 'last-known-good' }}
        run: |
          UI_TARGET="${{ steps.targets.outputs.ui_target }}"
          if [[ -n "$UI_TARGET" ]]; then
            echo "🔄 Rolling back UI to revision: $UI_TARGET"
            gcloud run services update-traffic ${{ env.SERVICE_NAME_UI }} \
              --region=${{ env.REGION }} \
              --to-revisions="$UI_TARGET=100"
            echo "✅ UI rollback completed"
          else
            echo "❌ No UI revision target found"
            exit 1
          fi

      - name: Download GCP Secret for LKG deployment
        if: ${{ steps.targets.outputs.use_lkg_image == 'true' }}
        run: |
          echo "Downloading secrets for last-known-good deployment..."
          gcloud secrets versions access latest --secret="${{ env.GCP_SECRET_NAME }}" --project="${{ env.PROJECT_ID }}" > secrets.yml

      - name: Deploy Last Known Good (API)
        if: ${{ (github.event.inputs.services == 'api' || github.event.inputs.services == 'both') && steps.targets.outputs.use_lkg_image == 'true' }}
        uses: google-github-actions/deploy-cloudrun@v2
        with:
          service: ${{ env.SERVICE_NAME_API }}
          region: ${{ env.REGION }}
          image: ${{ env.IMAGE_REPO }}/${{ env.IMAGE_NAME }}:last-known-good
          env_vars_file: secrets.yml
          env_vars: |-
            RUNTIME_ENV=prd
            DEBUG=false
            WAIT_FOR_DB=true
            MIGRATE_DB=false
            WAIT_FOR_REDIS=false
            DB_POOL_SIZE=10
            DB_MAX_OVERFLOW=20
            API_TIMEOUT=120
            DB_HOST=/cloudsql/vs-data-439613:europe-west1:vs-ai-production
            DB_PORT=5432
            POSTGRES_INIT_SCHEMA=public
            DEBUG_MODE=false
            PERFORMANCE_MODE=true
          flags: |
            --allow-unauthenticated
            --port=8000
            --memory=3Gi
            --cpu=2
            --min-instances=1
            --max-instances=10
            --concurrency=100
            --timeout=1200
            --add-cloudsql-instances=vs-data-439613:europe-west1:vs-ai-production

      - name: Deploy Last Known Good (UI)
        if: ${{ (github.event.inputs.services == 'ui' || github.event.inputs.services == 'both') && steps.targets.outputs.use_lkg_image == 'true' }}
        uses: google-github-actions/deploy-cloudrun@v2
        with:
          service: ${{ env.SERVICE_NAME_UI }}
          region: ${{ env.REGION }}
          image: ${{ env.IMAGE_REPO }}/${{ env.IMAGE_NAME }}:last-known-good
          env_vars_file: secrets.yml
          env_vars: |-
            RUNTIME_ENV=prd
            DEBUG=false
            WAIT_FOR_DB=true
            MIGRATE_DB=false
            WAIT_FOR_REDIS=false
            STREAMLIT_SERVER_HEADLESS=true
            PRINT_ENV_ON_LOAD=false
            PYTHONPATH=/app
            DEBUG_MODE=false
            PERFORMANCE_MODE=false
          flags: |
            --allow-unauthenticated
            --port=8501
            --memory=3Gi
            --cpu=2
            --min-instances=1
            --max-instances=5
            --concurrency=100
            --timeout=1200
            --add-cloudsql-instances=vs-data-439613:europe-west1:vs-ai-production

      - name: Verify rollback
        id: verify
        run: |
          echo "⏳ Waiting for rollback to stabilize..."
          sleep 30

          # Test services
          if [[ "${{ github.event.inputs.services }}" == "api" || "${{ github.event.inputs.services }}" == "both" ]]; then
            API_URL=$(gcloud run services describe ${{ env.SERVICE_NAME_API }} \
              --region=${{ env.REGION }} \
              --format='value(status.url)')
            echo "🧪 Testing API after rollback..."
            if curl -s -f "$API_URL/v1/health"; then
              echo "✅ API is healthy after rollback"
              echo "api_healthy=true" >> "$GITHUB_OUTPUT"
            else
              echo "❌ API health check failed after rollback"
              echo "api_healthy=false" >> "$GITHUB_OUTPUT"
            fi
          fi

          if [[ "${{ github.event.inputs.services }}" == "ui" || "${{ github.event.inputs.services }}" == "both" ]]; then
            UI_URL=$(gcloud run services describe ${{ env.SERVICE_NAME_UI }} \
              --region=${{ env.REGION }} \
              --format='value(status.url)')
            echo "🧪 Testing UI after rollback..."
            if curl -s -f "$UI_URL" | head -10 > /dev/null; then
              echo "✅ UI is healthy after rollback"
              echo "ui_healthy=true" >> "$GITHUB_OUTPUT"
            else
              echo "❌ UI health check failed after rollback"
              echo "ui_healthy=false" >> "$GITHUB_OUTPUT"
            fi
          fi

      - name: Create rollback record issue
        if: always()
        uses: actions/create-issue@v2
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          title: "🔄 Production Rollback Executed - ${{ github.event.inputs.services }}"
          body: |
            ## Rollback Record

            **Executed by:** ${{ github.actor }}
            **Time:** ${{ github.event.repository.updated_at }}
            **Services:** ${{ github.event.inputs.services }}
            **Rollback Type:** ${{ github.event.inputs.rollback_target }}
            **Reason:** ${{ github.event.inputs.reason }}

            ### Previous Revisions
            - API: ${{ steps.current.outputs.current_api }}
            - UI: ${{ steps.current.outputs.current_ui }}

            ### Rolled Back To
            - API: ${{ steps.targets.outputs.api_target || 'N/A' }}
            - UI: ${{ steps.targets.outputs.ui_target || 'N/A' }}

            ### Health Check Results
            - API: ${{ steps.verify.outputs.api_healthy == 'true' && '✅ Healthy' || '❌ Failed' }}
            - UI: ${{ steps.verify.outputs.ui_healthy == 'true' && '✅ Healthy' || '❌ Failed' }}

            ### Next Steps
            1. Investigate the issue that caused the rollback
            2. Fix the issue in a new commit
            3. Deploy through standard deployment process

            /cc @DataTeam
          labels: rollback,production

      - name: Rollback summary
        if: always()
        run: |
          echo "📊 Rollback Summary"
          echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
          echo "Services: ${{ github.event.inputs.services }}"
          echo "Rollback Type: ${{ github.event.inputs.rollback_target }}"
          echo "Reason: ${{ github.event.inputs.reason }}"
          echo ""
          echo "Results:"
          if [[ "${{ github.event.inputs.services }}" == "api" || "${{ github.event.inputs.services }}" == "both" ]]; then
            echo "  API: ${{ steps.verify.outputs.api_healthy == 'true' && '✅ Healthy' || '❌ Failed' }}"
          fi
          if [[ "${{ github.event.inputs.services }}" == "ui" || "${{ github.event.inputs.services }}" == "both" ]]; then
            echo "  UI: ${{ steps.verify.outputs.ui_healthy == 'true' && '✅ Healthy' || '❌ Failed' }}"
          fi
          echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
