name: Promote Canary Production

on:
  workflow_dispatch:
    inputs:
      traffic_percentage:
        description: 'New traffic percentage for canary'
        required: true
        type: choice
        options:
          - '25'
          - '50'
          - '75'
          - '100'
      services:
        description: 'Services to promote'
        required: true
        type: choice
        default: 'both'
        options:
          - 'api'
          - 'ui'
          - 'both'

env:
  PROJECT_ID: vs-data-439613
  SERVICE_NAME_API: vanilla-steel-ai-api
  SERVICE_NAME_UI: vanilla-steel-ai-ui
  REGION: europe-west1

permissions:
  contents: read
  id-token: write

jobs:
  promote-canary:
    name: Promote Canary Traffic
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Google Auth
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2
        with:
          project_id: ${{ env.PROJECT_ID }}

      - name: Get current traffic split
        id: current_traffic
        run: |
          echo "📊 Current Traffic Distribution:"

          if [[ "${{ github.event.inputs.services }}" == "api" || "${{ github.event.inputs.services }}" == "both" ]]; then
            echo ""
            echo "API Service:"
            gcloud run services describe ${{ env.SERVICE_NAME_API }} \
              --region=${{ env.REGION }} \
              --format="table(status.traffic[].percent,status.traffic[].revisionName,status.traffic[].tag)"
          fi

          if [[ "${{ github.event.inputs.services }}" == "ui" || "${{ github.event.inputs.services }}" == "both" ]]; then
            echo ""
            echo "UI Service:"
            gcloud run services describe ${{ env.SERVICE_NAME_UI }} \
              --region=${{ env.REGION }} \
              --format="table(status.traffic[].percent,status.traffic[].revisionName,status.traffic[].tag)"
          fi

      - name: Promote API Canary
        if: ${{ github.event.inputs.services == 'api' || github.event.inputs.services == 'both' }}
        run: |
          PERCENTAGE="${{ github.event.inputs.traffic_percentage }}"
          echo "🚀 Promoting API canary to ${PERCENTAGE}% traffic..."

          gcloud run services update-traffic ${{ env.SERVICE_NAME_API }} \
            --region=${{ env.REGION }} \
            --to-tags=green=${PERCENTAGE}

          echo "✅ API canary promoted to ${PERCENTAGE}%"

      - name: Promote UI Canary
        if: ${{ github.event.inputs.services == 'ui' || github.event.inputs.services == 'both' }}
        run: |
          PERCENTAGE="${{ github.event.inputs.traffic_percentage }}"
          echo "🚀 Promoting UI canary to ${PERCENTAGE}% traffic..."

          gcloud run services update-traffic ${{ env.SERVICE_NAME_UI }} \
            --region=${{ env.REGION }} \
            --to-tags=green=${PERCENTAGE}

          echo "✅ UI canary promoted to ${PERCENTAGE}%"

      - name: Verify services health
        run: |
          echo "⏳ Waiting for traffic changes to stabilize..."
          sleep 20

          if [[ "${{ github.event.inputs.services }}" == "api" || "${{ github.event.inputs.services }}" == "both" ]]; then
            API_URL=$(gcloud run services describe ${{ env.SERVICE_NAME_API }} \
              --region=${{ env.REGION }} \
              --format='value(status.url)')
            echo "🧪 Testing API health..."
            curl -s -f "$API_URL/v1/health" && echo "✅ API is healthy" || echo "⚠️ API health check failed"
          fi

          if [[ "${{ github.event.inputs.services }}" == "ui" || "${{ github.event.inputs.services }}" == "both" ]]; then
            UI_URL=$(gcloud run services describe ${{ env.SERVICE_NAME_UI }} \
              --region=${{ env.REGION }} \
              --format='value(status.url)')
            echo "🧪 Testing UI health..."
            curl -s -f "$UI_URL" | head -5 > /dev/null && echo "✅ UI is healthy" || echo "⚠️ UI health check failed"
          fi

      - name: Show new traffic distribution
        run: |
          echo ""
          echo "📊 New Traffic Distribution:"

          if [[ "${{ github.event.inputs.services }}" == "api" || "${{ github.event.inputs.services }}" == "both" ]]; then
            echo ""
            echo "API Service:"
            gcloud run services describe ${{ env.SERVICE_NAME_API }} \
              --region=${{ env.REGION }} \
              --format="table(status.traffic[].percent,status.traffic[].revisionName,status.traffic[].tag)"
          fi

          if [[ "${{ github.event.inputs.services }}" == "ui" || "${{ github.event.inputs.services }}" == "both" ]]; then
            echo ""
            echo "UI Service:"
            gcloud run services describe ${{ env.SERVICE_NAME_UI }} \
              --region=${{ env.REGION }} \
              --format="table(status.traffic[].percent,status.traffic[].revisionName,status.traffic[].tag)"
          fi

      - name: Promotion summary
        run: |
          echo ""
          echo "✅ Canary Promotion Complete"
          echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
          echo "Services: ${{ github.event.inputs.services }}"
          echo "New Traffic: ${{ github.event.inputs.traffic_percentage }}%"
          echo ""
          if [[ "${{ github.event.inputs.traffic_percentage }}" != "100" ]]; then
            echo "📈 Next Steps:"
            echo "1. Monitor metrics and error rates"
            echo "2. If stable, promote to next level"
            echo "3. If issues arise, rollback using the rollback workflow"
          else
            echo "🎉 Canary fully promoted to production!"
            echo "Consider removing the canary tag in next deployment"
          fi
          echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
