name: Deploy to Cloud Run (Staging)

on:
  push:
    branches:
      - staging
  workflow_dispatch:
    inputs:
      deploy_message:
        description: 'Deployment Staging'
        required: false
        default: 'Manual deployment to staging'
      debug_mode:
        description: 'Enable debug mode for agents'
        required: false
        default: 'false'
        type: choice
        options:
          - 'true'
          - 'false'
      performance_mode:
        description: 'Enable performance mode for agents'
        required: false
        default: 'false'
        type: choice
        options:
          - 'true'
          - 'false'

env:
  PROJECT_ID: vs-data-439613
  SERVICE_NAME_API: vanilla-steel-ai-api-stg
  SERVICE_NAME_UI: vanilla-steel-ai-ui-stg
  REGION: europe-west1
  IMAGE_REPO: europe-west1-docker.pkg.dev/vs-data-439613/vanilla-steel-ai
  IMAGE_NAME: vanilla-steel-ai
  IMAGE_TAG: stg
  GCP_SECRET_NAME: vanilla-steel-ai-staging-secrets

permissions:
  contents: read
  id-token: write

jobs:
  deploy-staging:
    name: Deploy API and UI to Staging Cloud Run
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'

      - name: Install uv
        uses: astral-sh/setup-uv@v6
        with:
          enable-cache: true
          cache-dependency-glob: "requirements**.txt"

      - name: Install dependencies
        run: |
          uv venv .venv --python 3.12
          uv sync --all-groups

      - name: Generate version
        id: version
        run: |
          echo "version=$(date +'%Y%m%d%H%M%S')-$(git rev-parse --short "HEAD")" >> "$GITHUB_OUTPUT"

      - name: Google Auth
        id: auth
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2
        with:
          project_id: ${{ env.PROJECT_ID }}

      - name: Download GCP Secret to yml file
        run: |
          echo "Attempting to download secret ${{ env.GCP_SECRET_NAME }}..."
          gcloud secrets versions access latest --secret="${{ env.GCP_SECRET_NAME }}" --project="${{ env.PROJECT_ID }}" > secrets.yml
          echo "Secret downloaded to secrets.yml file."
          if [ -s secrets.yml ]; then
            echo "secrets.yml created successfully."
          else
            echo "ERROR: secrets.yml is empty or was not created."
            exit 1
          fi

      - name: Configure Docker for Artifact Registry
        run: |
          gcloud auth configure-docker ${{ env.REGION }}-docker.pkg.dev --quiet

      - name: Show Docker build context and source code
        run: |
          echo "🔨 Building Docker image from source code..."
          echo "📁 Build context: $(pwd)"
          echo "🐳 Using Dockerfile: $(pwd)/Dockerfile"
          echo ""
          echo "📋 Source code files being included in Docker build:"
          ls -la
          echo ""
          echo "🔍 Dockerfile content:"
          head -10 Dockerfile
          echo "..."
          echo ""
          echo "🏷️ Image tags to be created:"
          echo "  - ${{ env.IMAGE_REPO }}/${{ env.IMAGE_NAME }}:${{ steps.version.outputs.version }}"
          echo "  - ${{ env.IMAGE_REPO }}/${{ env.IMAGE_NAME }}:${{ env.IMAGE_TAG }}"
          echo "  - ${{ env.IMAGE_REPO }}/${{ env.IMAGE_NAME }}:latest"

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build and Push Docker image to Artifact Registry
        uses: docker/build-push-action@v5
        with:
          context: .  # Use current directory (contains Dockerfile and source code)
          file: ./Dockerfile  # Explicitly specify Dockerfile location
          push: true
          platforms: linux/amd64,linux/arm64  # Build for both platforms
          tags: |
            ${{ env.IMAGE_REPO }}/${{ env.IMAGE_NAME }}:${{ steps.version.outputs.version }}
            ${{ env.IMAGE_REPO }}/${{ env.IMAGE_NAME }}:${{ env.IMAGE_TAG }}
            ${{ env.IMAGE_REPO }}/${{ env.IMAGE_NAME }}:latest
          labels: |
            org.opencontainers.image.source=${{ github.repositoryUrl }}
            org.opencontainers.image.revision=${{ github.sha }}
            org.opencontainers.image.created=${{ steps.version.outputs.version }}

      - name: Verify Docker image was built and pushed
        run: |
          echo "✅ Docker image built and pushed successfully!"
          echo "Image: ${{ env.IMAGE_REPO }}/${{ env.IMAGE_NAME }}:${{ steps.version.outputs.version }}"
          gcloud container images list --repository=${{ env.IMAGE_REPO }} --filter="name:${{ env.IMAGE_NAME }}" --limit=5

      # Database test removed - it doesn't serve HTTP traffic so Cloud Run fails

      - name: Deploy API to Cloud Run
        uses: google-github-actions/deploy-cloudrun@v2
        with:
          service: ${{ env.SERVICE_NAME_API }}
          region: ${{ env.REGION }}
          image: ${{ env.IMAGE_REPO }}/${{ env.IMAGE_NAME }}:${{ env.IMAGE_TAG }}
          env_vars_file: secrets.yml
          env_vars: |-
            RUNTIME_ENV=stg
            DEBUG=true
            WAIT_FOR_DB=true
            MIGRATE_DB=true
            WAIT_FOR_REDIS=false
            DB_POOL_SIZE=5
            DB_MAX_OVERFLOW=10
            API_TIMEOUT=120
            DB_HOST=/cloudsql/vs-data-439613:europe-west1:vs-ai-staging
            DB_PORT=5432
            POSTGRES_INIT_SCHEMA=postgres_stg
            DEBUG_MODE=${{ github.event.inputs.debug_mode || 'false' }}
            PERFORMANCE_MODE=${{ github.event.inputs.performance_mode || 'true' }}
          flags: |
            --allow-unauthenticated
            --port=8000
            --memory=2Gi
            --cpu=2
            --min-instances=0
            --max-instances=5
            --concurrency=80
            --timeout=900
            --add-cloudsql-instances=vs-data-439613:europe-west1:vs-ai-staging

      - name: Test API deployment
        id: test_api
        run: |
          echo "⏳ Waiting for API service to be ready..."
          sleep 30

          API_URL=$(gcloud run services describe "${{ env.SERVICE_NAME_API }}" --region="${{ env.REGION }}" --format='value(status.url)')
          echo "API_URL=$API_URL" >> "$GITHUB_OUTPUT"

          echo "🧪 Testing API health endpoint..."
          curl -s -f -w "\nHTTP Status: %{http_code}\n" "$API_URL/v1/health" || {
            echo "⚠️ API health check failed, checking logs..."
            gcloud logging read \
              "resource.type=\"cloud_run_revision\" AND resource.labels.service_name=\"${{ env.SERVICE_NAME_API }}\" AND severity>=ERROR" \
              --project="${{ env.PROJECT_ID }}" \
              --limit=50 \
              --format="table(timestamp,textPayload)"
          }

      - name: Deploy UI to Cloud Run
        uses: google-github-actions/deploy-cloudrun@v2
        with:
          service: ${{ env.SERVICE_NAME_UI }}
          region: ${{ env.REGION }}
          image: ${{ env.IMAGE_REPO }}/${{ env.IMAGE_NAME }}:${{ env.IMAGE_TAG }}
          env_vars_file: secrets.yml
          env_vars: |-
            RUNTIME_ENV=stg
            DEBUG=true
            WAIT_FOR_DB=true
            MIGRATE_DB=true
            WAIT_FOR_REDIS=false
            STREAMLIT_SERVER_HEADLESS=true
            PRINT_ENV_ON_LOAD=false
            PYTHONPATH=/app
            DEBUG_MODE=${{ github.event.inputs.debug_mode || 'false' }}
            PERFORMANCE_MODE=${{ github.event.inputs.performance_mode || 'false' }}
          flags: |
            --allow-unauthenticated
            --port=8501
            --memory=2Gi
            --cpu=1
            --min-instances=0
            --max-instances=3
            --concurrency=80
            --timeout=900
            --add-cloudsql-instances=vs-data-439613:europe-west1:vs-ai-staging

      - name: Test UI deployment
        id: test_ui
        run: |
          echo "⏳ Waiting for UI service to be ready..."
          sleep 30

          UI_URL=$(gcloud run services describe "${{ env.SERVICE_NAME_UI }}" --region="${{ env.REGION }}" --format='value(status.url)')
          echo "UI_URL=$UI_URL" >> "$GITHUB_OUTPUT"

          echo "🧪 Testing UI endpoint..."
          curl -s -f -w "\nHTTP Status: %{http_code}\n" "$UI_URL" | head -20 || {
            echo "⚠️ UI check failed, checking logs..."
            gcloud logging read \
              "resource.type=\"cloud_run_revision\" AND resource.labels.service_name=\"${{ env.SERVICE_NAME_UI }}\" AND severity>=ERROR" \
              --project="${{ env.PROJECT_ID }}" \
              --limit=50 \
              --format="table(timestamp,textPayload)"
          }

      - name: Deployment success summary
        if: success()
        run: |
          echo "🚀 Successfully deployed to Cloud Run Staging"
          echo ""
          echo "📊 Deployment Summary:"
          echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
          echo "🔖 Version: ${{ steps.version.outputs.version }}"
          echo "🏷️ Image Tag: ${{ env.IMAGE_TAG }}"
          echo ""
          echo "🌐 Service URLs:"
          echo "   API:  ${{ steps.test_api.outputs.API_URL }}"
          echo "   UI:   ${{ steps.test_ui.outputs.UI_URL }}"
          echo ""
          echo "📚 API Documentation: ${{ steps.test_api.outputs.API_URL }}/docs"
          echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

      - name: Deployment failure notification
        if: failure()
        run: |
          echo "❌ Deployment to Cloud Run Staging failed."
          echo ""
          echo "🔍 Troubleshooting steps:"
          echo "1. Check the logs above for specific error messages"
          echo "2. Verify the Docker image was built correctly"
          echo "3. Check Cloud Run logs:"
          echo "   gcloud logging read \"resource.type=\\\"cloud_run_revision\\\" AND (resource.labels.service_name=\\\"${{ env.SERVICE_NAME_API }}\\\" OR resource.labels.service_name=\\\"${{ env.SERVICE_NAME_UI }}\\\")\" --project=\"${{ env.PROJECT_ID }}\" --limit=100"
          echo "4. Ensure all required environment variables are set in secrets"
