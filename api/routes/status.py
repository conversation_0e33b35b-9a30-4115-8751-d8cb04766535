from fastapi import APIRouter

from api.settings import get_settings
from utils.dttm import current_utc_str

######################################################
## Router for API status
######################################################

status_router = APIRouter(tags=["Status"])


@status_router.get("/health")
def get_health():
    """
    Check the health of the Api.
    """

    settings = get_settings()

    return {
        "status": "success",
        "router": "status",
        "path": "/health",
        "utc": current_utc_str(),
        "environment": settings.runtime_env,
    }


@status_router.get("/config")
def get_config():
    """
    Return non-sensitive configuration information for monitoring and debugging.

    Returns:
        dict: Configuration information safe for external consumption
    """
    settings = get_settings()

    return {
        "environment": settings.runtime_env.value,
        "api_version": settings.version,
        "api_title": settings.title,
        "docs_enabled": settings.docs_enabled,
        "api_timeout": settings.API_TIMEOUT,
        "max_retries": settings.MAX_RETRIES,
        "request_rate_limit": settings.REQUEST_RATE_LIMIT,
        "max_concurrent_requests": settings.MAX_CONCURRENT_REQUESTS,
        "cors_origins_count": (len(settings.cors_origin_list) if settings.cors_origin_list else 0),
        "database_driver": "postgresql",  # Don't expose actual connection details
        "timestamp": current_utc_str(),
    }
