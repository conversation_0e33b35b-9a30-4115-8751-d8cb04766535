"""
API routes for client management.
"""

# ruff: noqa: B008

import time
import uuid
from datetime import datetime
from typing import Any
from uuid import uuid4

from fastapi import APIRouter, Depends, Header, HTTPException, Request, status
from sqlalchemy.orm import Session
from unkey_py import Unkey

from api.settings import api_settings
from db.session import get_db
from db.tables.rfq_tables import (
    CatalogType,
    ClientConfiguration,
    GradeCoatingCompatibility,
    ReferenceData,
    RFQClient,
    RFQInstructionSchema,
)
from models.rfq.api import (
    CatalogEntryRequest,
    ClientCreateRequest,
    ClientListResponse,
    ClientOnboardingResponse,
    ClientResponse,
    ClientSchemaInfoResponse,
)
from models.rfq.dynamic_schema import DynamicMaterialSpecFactory, SchemaRegistry
from utils.log import get_app_logger
from utils.rfq.rules import extraction_rules, formatter_rules, normalizer_rules, validation_rules
from utils.rfq.schemas import (
    EXTRACTION_AGENT_OUTPUT_SCHEMA,
    VALIDATION_AGENT_OUTPUT_SCHEMA,
)

# Import default schemas and rules for client onboarding

# Initialize logger
logger = get_app_logger()

# Initialize Unkey client
unkey_client = Unkey(bearer_auth=api_settings.UNKEY_API_KEY)


clients_router = APIRouter(prefix="/clients", tags=["Clients Configure"])


async def verify_admin_key(
    request: Request,
    admin_key: str = Header(..., description="Admin API key for client management"),
):
    """
    Verify the admin API key using Unkey.

    Args:
        admin_key: The admin API key provided in the request header

    Returns:
        bool: True if the admin key is valid

    Raises:
        HTTPException: If the admin key is invalid
    """

    try:
        # Verify admin key with Unkey
        verification = unkey_client.keys.verify(request={"key": admin_key, "api_id": api_settings.UNKEY_API_ID})

        # Check if the result is valid
        if hasattr(verification, "v1_keys_verify_key_response") and verification.v1_keys_verify_key_response:
            verify_response = verification.v1_keys_verify_key_response
            if not verify_response.valid:
                logger.warning("Invalid API key verification result")
                raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid API key")
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="API key verify response is empty",
            )

        # Check if key has admin role in metadata
        is_admin = verify_response.meta.get("role") == "admin"

        if not is_admin:
            logger.warning("API key is valid but not an admin key")
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Insufficient permissions")

        return True

    except Exception as e:
        if isinstance(e, HTTPException):
            raise e

        logger.error(f"Error verifying admin key: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error verifying admin key",
        ) from e


@clients_router.post("", response_model=ClientResponse, status_code=status.HTTP_201_CREATED)
async def create_client(
    request: ClientCreateRequest,
    db: Session = Depends(get_db),  # noqa: B008
    _: bool = Depends(verify_admin_key),  # noqa: B008
):  # -> Any:
    """
    Create a new client with configuration and generate an API key in Unkey.

    Args:
        request: Client creation request
        db: Database session

    Returns:
        ClientResponse: Created client information
    """
    # Generate client ID
    client_id = str(uuid.uuid4())

    try:
        # Create API key in Unkey
        result = await unkey_client.keys.create_key(
            api_id=api_settings.UNKEY_API_ID,  # Your Unkey API ID
            prefix="rfq",  # Optional prefix for the key
            name=f"Client: {request.name}",  # Name for the key
            meta={"client_id": client_id, "client_name": request.name},
            expires_at=None,  # No expiration
            ratelimit=None,  # No rate limit
            enabled=True,
        )

        if not result.is_ok:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to create API key: {result.unwrap_err()}",
            )

        key_response = result.unwrap()

        if not key_response.key:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create API key",
            )

        api_key = key_response.key

        # Create the client in our database (without storing the API key)
        client = RFQClient(
            client_id=client_id,
            name=request.name,
            api_key="unkey_managed",  # We don't store the actual key
            status="active",
            has_kb=getattr(request, "has_kb", False),  # Default to False if not provided
            has_instruction=getattr(request, "has_instruction", False),  # Default to False if not provided
        )
        db.add(client)

        # Create client configuration with enhanced model preferences
        model_preferences = request.llm_preferences or {}

        # Ensure the model preferences follow the new structure
        if model_preferences and "agent_models" not in model_preferences:
            # Handle legacy format or create default structure
            default_model = model_preferences.get("model", "google/gemini-flash-1.5")
            model_preferences = {
                "default_model": default_model,
                "agent_models": {"extraction": default_model, "validation": default_model, "normalizer": default_model, "formatter": default_model},
                "performance_settings": {
                    "extraction": {"temperature": 0.0, "max_tokens": 6144},
                    "validation": {"temperature": 0.1, "max_tokens": 8192},
                    "normalizer": {"temperature": 0.0, "max_tokens": 4096},
                    "formatter": {"temperature": 0.0, "max_tokens": 4096},
                },
                "fallback_model": default_model,
            }

        config = ClientConfiguration(
            client_id=client_id,
            extraction_output_schema=request.output_schema or {},
            validation_output_schema=request.output_schema or {},  # Can be different if needed
            extraction_rules=request.extraction_rules or {},
            validation_rules=request.validation_rules or {},
            normalization_rules=request.normalization_rules or {},
            model_preferences=model_preferences,
        )
        db.add(config)

        db.commit()

        return ClientResponse(
            client_id=client_id,
            name=request.name,
            client_code=api_key,  # Return the API key to the admin
            status="active",
        )

    except Exception as e:
        db.rollback()
        logger.error(f"Failed to create client: {e}")

        # Try to clean up Unkey key if it was created
        try:
            if "key_response" in locals() and key_response.key_id:
                await unkey_client.keys.delete_key(key_id=key_response.key_id)
        except Exception as cleanup_error:
            logger.error(f"Failed to clean up Unkey key: {cleanup_error}")

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create client: {str(e)}",
        ) from e


@clients_router.get("/{client_id}", response_model=ClientResponse)
async def get_client(
    client_id: str,
    db: Session = Depends(get_db),
    _: bool = Depends(verify_admin_key),  # noqa: B008
):
    """
    Get client information.

    Args:
        client_id: Client ID
        db: Database session

    Returns:
        ClientResponse: Client information
    """
    client = db.query(RFQClient).filter(RFQClient.client_id == client_id).first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Client not found: {client_id}",
        )

    # For security, we don't return the actual API key since it's managed by Unkey
    return ClientResponse(
        client_id=str(client.client_id),
        name=client.name,
        api_key="unkey_managed",  # We don't expose the actual key
        status=client.status,
    )


@clients_router.post("/{client_id}/regenerate-key")
async def regenerate_client_api_key(
    client_id: str,
    db: Session = Depends(get_db),
    _: bool = Depends(verify_admin_key),  # noqa: B008
):
    """
    Regenerate a client's API key in Unkey.

    Args:
        client_id: Client ID
        db: Database session

    Returns:
        dict: New API key information
    """
    # Verify client exists
    client = db.query(RFQClient).filter(RFQClient.client_id == client_id).first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Client not found: {client_id}",
        )

    try:
        # Create new API key in Unkey
        result = await unkey_client.keys.create_key(
            api_id=api_settings.UNKEY_API_ID,
            prefix="rfq",
            name=f"Client: {client.name} (regenerated)",
            meta={"client_id": client_id, "client_name": client.name},
            enabled=True,
        )

        if not result.is_ok:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to create new API key: {result.unwrap_err()}",
            )

        key_response = result.unwrap()

        if not key_response.key:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create new API key",
            )

        # Return the new key
        return {
            "client_id": client_id,
            "name": client.name,
            "new_api_key": key_response.key,
            "message": "API key regenerated successfully",
        }

    except Exception as e:
        if isinstance(e, HTTPException):
            raise e

        logger.error(f"Failed to regenerate API key: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to regenerate API key: {str(e)}",
        ) from e


@clients_router.put("/{client_id}/config")
async def update_client_config(
    client_id: str,
    config: dict[str, Any],
    db: Session = Depends(get_db),  # noqa: B008
    _: bool = Depends(verify_admin_key),  # noqa: B008
):
    """
    Update client configuration.

    Args:
        client_id: Client ID
        config: Configuration to update
        db: Database session

    Returns:
        dict: Success message
    """
    client_config = db.query(ClientConfiguration).filter(ClientConfiguration.client_id == client_id).first()

    if not client_config:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Client configuration not found: {client_id}",
        )

    try:
        # Update the configuration fields
        for key, value in config.items():
            if hasattr(client_config, key):
                setattr(client_config, key, value)

        db.commit()

        return {"message": "Client configuration updated successfully"}

    except Exception as e:
        db.rollback()
        logger.error(f"Failed to update client configuration: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update client configuration: {str(e)}",
        ) from None


@clients_router.post("/{client_id}/catalog/{catalog_type}")
async def update_client_catalog(
    client_id: str,
    catalog_type: str,
    entries: list[CatalogEntryRequest],
    db: Session = Depends(get_db),  # noqa: B008
    _: bool = Depends(verify_admin_key),  # noqa: B008
):
    """
    Update client catalog entries.

    Args:
        client_id: Client ID
        catalog_type: Catalog type name
        entries: Catalog entries to update
        db: Database session

    Returns:
        dict: Success message
    """
    # Verify client exists
    client = db.query(RFQClient).filter(RFQClient.client_id == client_id).first()
    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Client not found: {client_id}",
        )

    # Get or create catalog type
    catalog_type_obj = db.query(CatalogType).filter(CatalogType.name == catalog_type).first()
    if not catalog_type_obj:
        catalog_type_obj = CatalogType(name=catalog_type, description=f"Catalog for {catalog_type}")
        db.add(catalog_type_obj)
        db.flush()

    try:
        # Update or create catalog entries
        for entry in entries:
            existing_entry = (
                db.query(ReferenceData)
                .filter(
                    ReferenceData.client_id == client_id,
                    ReferenceData.catalog_type_id == catalog_type_obj.catalog_type_id,
                    ReferenceData.code == entry.code,
                )
                .first()
            )

            if existing_entry:
                # Update existing entry
                existing_entry.name = entry.name
                existing_entry.description = entry.description
                existing_entry.properties = entry.properties
            else:
                # Create new entry
                new_entry = ReferenceData(
                    client_id=client_id,
                    catalog_type_id=catalog_type_obj.catalog_type_id,
                    code=entry.code,
                    name=entry.name,
                    description=entry.description,
                    properties=entry.properties,
                    is_active=True,
                )
                db.add(new_entry)

        db.commit()

        return {"message": f"Updated {len(entries)} entries in {catalog_type} catalog for client {client_id}"}

    except Exception as e:
        db.rollback()
        logger.error(f"Failed to update client catalog: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update client catalog: {str(e)}",
        ) from e


@clients_router.post("/{client_id}/compatibility")
async def update_grade_coating_compatibility(
    client_id: str,
    compatibilities: list[dict[str, str]],
    db: Session = Depends(get_db),  # noqa: B008
    _: bool = Depends(verify_admin_key),  # noqa: B008
):
    """
    Update grade-coating compatibility for a client.

    Args:
        client_id: Client ID
        compatibilities: List of grade-coating pairs
        db: Database session

    Returns:
        dict: Success message
    """
    # Verify client exists
    client = db.query(RFQClient).filter(RFQClient.client_id == client_id).first()
    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Client not found: {client_id}",
        )

    try:
        # Add new compatibility entries
        for compat in compatibilities:
            if "grade_code" not in compat or "coating_code" not in compat:
                continue

            # Check if entry already exists
            existing = (
                db.query(GradeCoatingCompatibility)
                .filter(
                    GradeCoatingCompatibility.client_id == client_id,
                    GradeCoatingCompatibility.grade_code == compat["grade_code"],
                    GradeCoatingCompatibility.coating_code == compat["coating_code"],
                )
                .first()
            )

            if not existing:
                new_compat = GradeCoatingCompatibility(
                    client_id=client_id,
                    grade_code=compat["grade_code"],
                    coating_code=compat["coating_code"],
                )
                db.add(new_compat)

        db.commit()

        return {"message": f"Updated {len(compatibilities)} grade-coating compatibility entries for client {client_id}"}

    except Exception as e:
        db.rollback()
        logger.error(f"Failed to update compatibility: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update compatibility: {str(e)}",
        ) from e


@clients_router.get("/admin/clients", response_model=ClientListResponse)
async def list_all_clients_with_schemas(
    _: bool = Depends(verify_admin_key),
    db: Session = Depends(get_db),  # noqa: B008
):
    """
    List all clients with their schema information (admin only).

    Args:
        _: Result of admin key verification (ignored)
        db: Database session

    Returns:
        ClientListResponse: List of all clients with schema info
    """
    # Directly query clients and their configurations
    clients = db.query(RFQClient).all()

    clients_info = []
    for client in clients:
        # Get client configuration
        config = db.query(ClientConfiguration).filter(ClientConfiguration.client_id == client.client_id).first()

        # Check if client has custom schema
        has_custom_schema = config is not None and (config.extraction_output_schema is not None or config.validation_output_schema is not None)

        # Get schema info if available
        schema_info = {}
        if config and has_custom_schema:
            try:
                schema_info = DynamicMaterialSpecFactory.get_schema_info(client_id=str(client.client_id), db_session=db)
            except Exception as e:
                logger.warning(f"Could not get schema info for client {client.client_id}: {e}")
                schema_info = {"error": "Schema info unavailable"}

        client_info = ClientSchemaInfoResponse(
            client_id=str(client.client_id),
            client_name=client.name,
            has_custom_schema=has_custom_schema,
            schema_info=schema_info,
            last_updated=client.created_at.isoformat(),
        )
        clients_info.append(client_info)

    return ClientListResponse(clients=clients_info, total_count=len(clients_info))


@clients_router.post("/admin/schema/clear-cache")
async def clear_schema_cache(_: bool = Depends(verify_admin_key)):
    """
    Clear the schema cache (admin only).

    Args:
        _: Result of admin key verification (ignored)

    Returns:
        dict: Cache clear confirmation
    """
    # Clear the cache
    SchemaRegistry.clear_cache()
    logger.info("Schema cache cleared by admin")

    return {
        "success": True,
        "message": "Schema cache cleared successfully",
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
    }


@clients_router.get("/status", status_code=status.HTTP_200_OK)
async def clients_status(db: Session = Depends(get_db)):  # noqa: B008
    """
    Check the status of the client management service.

    Args:
        db: Database session

    Returns:
        dict: Status information about the client management service
    """
    # Get some statistics
    total_clients = db.query(RFQClient).count()
    active_clients = db.query(RFQClient).filter(RFQClient.status == "active").count()
    cached_schemas = len(SchemaRegistry._schemas)

    return {
        "status": "operational",
        "service": "Client Management",
        "version": "2.0.0",
        "features": ["dynamic_schemas", "client_specific_processing", "unkey_auth"],
        "statistics": {
            "total_clients": total_clients,
            "active_clients": active_clients,
            "cached_schemas": cached_schemas,
        },
    }


@clients_router.post("/onboard", response_model=ClientOnboardingResponse)
async def onboard_new_client(
    request: ClientCreateRequest,
    admin_key: str = Depends(verify_admin_key),
    db: Session = Depends(get_db),
):
    """
    Comprehensive client onboarding endpoint.

    Creates a new client with:
    1. Client record in rfq_clients table
    2. Complete set of instruction schemas in rfq_instruction_schemas table
    3. Client configuration in rfq_client_configurations table
    4. API key generation via Unkey

    Only accessible with admin API key.
    """
    logger.info(f"Starting comprehensive client onboarding for: {request.name}")

    # Input validation
    if not request.name or not request.name.strip():
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Client name cannot be empty")

    # Generate client code if not provided
    client_code = request.client_code
    if not client_code:
        # Generate a safe client code from name
        import re

        client_code = re.sub(r"[^a-zA-Z0-9_-]", "_", request.name.lower().replace(" ", "_"))
        client_code = f"{client_code}_{uuid4().hex[:8]}"

    # Check if client code already exists
    existing_client = db.query(RFQClient).filter(RFQClient.client_code == client_code).first()
    if existing_client:
        raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail=f"Client code '{client_code}' already exists")

    # Check if client name already exists
    existing_name = db.query(RFQClient).filter(RFQClient.name == request.name).first()
    if existing_name:
        raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail=f"Client name '{request.name}' already exists")

    try:
        # Step 1: Create client record
        client_id = uuid4()
        new_client = RFQClient(
            client_id=client_id,
            name=request.name,
            client_code=client_code,
            status="active",
            has_kb=request.has_kb,
            has_instruction=request.has_instruction,
            has_output_preference=request.has_output_preference,
        )

        db.add(new_client)
        db.flush()  # Get the client_id

        logger.info(f"Created client record: {client_id}")

        # Step 2: Create API key in Unkey
        try:
            key_response = await unkey_client.keys.create_key(
                api_id=api_settings.UNKEY_API_ID,
                prefix="rfq",
                name=f"Client: {request.name}",
                meta={"client_id": str(client_id), "client_name": request.name},
                expires_at=None,
            )
            api_key = key_response.key
            logger.info(f"Created Unkey API key for client: {client_id}")
        except Exception as e:
            logger.error(f"Failed to create Unkey API key: {e}")
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to create API key")

        # Step 3: Create instruction schemas
        schemas_created = {}
        schema_definitions = [
            # Extraction schemas
            {
                "type": "extraction_schema",
                "name": "Extraction Output Schema",
                "agent_prefix": "extraction",
                "content": request.custom_extraction_schema or EXTRACTION_AGENT_OUTPUT_SCHEMA,
                "description": "Schema for extraction agent output with material specifications",
            },
            {
                "type": "extraction_rules",
                "name": "Extraction Rules",
                "agent_prefix": "extraction",
                "content": request.custom_extraction_rules or extraction_rules.DEFAULT_EXTRACTION_RULES,
                "description": "Rules and instructions for extraction agent",
            },
            # Validation schemas
            {
                "type": "validation_schema",
                "name": "Validation Output Schema",
                "agent_prefix": "validation",
                "content": request.custom_validation_schema or VALIDATION_AGENT_OUTPUT_SCHEMA,
                "description": "Schema for validation agent output with categorical data",
            },
            {
                "type": "validation_rules",
                "name": "Validation Rules",
                "agent_prefix": "validation",
                "content": request.custom_validation_rules or validation_rules.DEFAULT_VALIDATION_RULES,
                "description": "Rules and instructions for validation agent",
            },
            # Normalizer schemas
            {
                "type": "normalizer_schema",
                "name": "Normalizer Output Schema",
                "agent_prefix": "normalizer",
                "content": request.custom_normalizer_schema or NORMALIZER_AGENT_OUTPUT_SCHEMA,
                "description": "Schema for normalizer agent output with numerical data",
            },
            {
                "type": "normalizer_rules",
                "name": "Normalizer Rules",
                "agent_prefix": "normalizer",
                "content": request.custom_normalization_rules or normalizer_rules.DEFAULT_NORMALIZER_RULES,
                "description": "Rules and instructions for normalizer agent",
            },
            # Formatter schemas
            {
                "type": "formatter_schema",
                "name": "Formatter Output Schema",
                "agent_prefix": "formatter",
                "content": request.custom_formatter_schema or formatter_schema.DEFAULT_FORMATTER_OUTPUT_SCHEMA,
                "description": "Schema for formatter agent output",
            },
            {
                "type": "formatter_rules",
                "name": "Formatter Rules",
                "agent_prefix": "formatter",
                "content": request.custom_formatter_rules or formatter_rules.DEFAULT_FORMATTER_RULES,
                "description": "Rules and instructions for formatter agent",
            },
            {
                "type": "formatter_configuration",
                "name": "Formatter Configuration",
                "agent_prefix": "formatter",
                "content": request.custom_formatter_config or formatter_schema.CLIENT_CONFIGURATION_SCHEMA,
                "description": "Configuration for formatter agent output preferences",
            },
        ]

        # Create all schemas
        for schema_def in schema_definitions:
            schema_id = uuid4()
            new_schema = RFQInstructionSchema(
                id=schema_id,
                client_id=client_id,
                name=schema_def["name"],
                type=schema_def["type"],
                agent_prefix=schema_def["agent_prefix"],
                version="1.0.0",
                content=schema_def["content"],
                description=schema_def["description"],
                is_active=True,
            )
            db.add(new_schema)
            schemas_created[schema_def["type"]] = schema_id

        logger.info(f"Created {len(schemas_created)} instruction schemas for client: {client_id}")

        # Step 4: Create client configuration
        config_id = uuid4()
        new_config = ClientConfiguration(
            config_id=config_id,
            client_id=client_id,
            version="1.0.0",
            extraction_output_schema_id=schemas_created.get("extraction_schema"),
            validation_output_schema_id=schemas_created.get("validation_schema"),
            normalizer_output_schema_id=schemas_created.get("normalizer_schema"),
            formatter_output_schema_id=schemas_created.get("formatter_schema"),
            extraction_rules_id=schemas_created.get("extraction_rules"),
            validation_rules_id=schemas_created.get("validation_rules"),
            normalization_rules_id=schemas_created.get("normalizer_rules"),
            formatter_rules_id=schemas_created.get("formatter_rules"),
            formatter_output_config_id=schemas_created.get("formatter_configuration"),
            model_preferences=request.model_preferences or {},
        )

        db.add(new_config)
        db.commit()

        logger.info(f"Successfully onboarded client: {client_id} with {len(schemas_created)} schemas")

        # Determine if using default schemas
        uses_default_schemas = not any(
            [
                request.custom_extraction_schema,
                request.custom_validation_schema,
                request.custom_normalizer_schema,
                request.custom_formatter_schema,
                request.custom_extraction_rules,
                request.custom_validation_rules,
                request.custom_normalization_rules,
                request.custom_formatter_rules,
                request.custom_formatter_config,
            ]
        )

        return ClientOnboardingResponse(
            client_id=client_id,
            name=request.name,
            client_code=client_code,
            api_key=api_key,
            status="active",
            config_id=config_id,
            schemas_created=schemas_created,
            total_schemas_created=len(schemas_created),
            uses_default_schemas=uses_default_schemas,
            created_at=datetime.utcnow().isoformat(),
        )

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        # Rollback database changes
        db.rollback()

        # Try to clean up Unkey key if it was created
        try:
            if "key_response" in locals() and hasattr(key_response, "key_id"):
                await unkey_client.keys.delete_key(key_id=key_response.key_id)
        except Exception as cleanup_error:
            logger.error(f"Failed to clean up Unkey key: {cleanup_error}")

        logger.error(f"Failed to onboard client: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Failed to onboard client: {str(e)}")
