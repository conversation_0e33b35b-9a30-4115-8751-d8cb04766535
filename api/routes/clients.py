"""
API routes for client management.
"""

# ruff: noqa: B008

import time
import uuid
from typing import Any

from fastapi import API<PERSON>out<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, HTTPException, Request, status
from sqlalchemy.orm import Session
from unkey_py import Unkey

from api.settings import api_settings
from db.session import get_db
from db.tables.rfq_tables import (
    CatalogType,
    ClientConfiguration,
    GradeCoatingCompatibility,
    ReferenceData,
    RFQClient,
)
from models.rfq.api import (
    CatalogEntryRequest,
    ClientCreateRequest,
    ClientListResponse,
    ClientResponse,
    ClientSchemaInfoResponse,
)
from models.rfq.dynamic_schema import DynamicMaterialSpecFactory, SchemaRegistry
from utils.log import get_app_logger

# Initialize logger
logger = get_app_logger()

# Initialize Unkey client
unkey_client = Unkey(bearer_auth=api_settings.UNKEY_API_KEY)


clients_router = APIRouter(prefix="/clients", tags=["Clients Configure"])


async def verify_admin_key(
    request: Request,
    admin_key: str = Header(..., description="Admin API key for client management"),
):
    """
    Verify the admin API key using Unkey.

    Args:
        admin_key: The admin API key provided in the request header

    Returns:
        bool: True if the admin key is valid

    Raises:
        HTTPException: If the admin key is invalid
    """

    try:
        # Verify admin key with Unkey
        verification = unkey_client.keys.verify(request={"key": admin_key, "api_id": api_settings.UNKEY_API_ID})

        # Check if the result is valid
        if hasattr(verification, "v1_keys_verify_key_response") and verification.v1_keys_verify_key_response:
            verify_response = verification.v1_keys_verify_key_response
            if not verify_response.valid:
                logger.warning("Invalid API key verification result")
                raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid API key")
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="API key verify response is empty",
            )

        # Check if key has admin role in metadata
        is_admin = verify_response.meta.get("role") == "admin"

        if not is_admin:
            logger.warning("API key is valid but not an admin key")
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Insufficient permissions")

        return True

    except Exception as e:
        if isinstance(e, HTTPException):
            raise e

        logger.error(f"Error verifying admin key: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error verifying admin key",
        ) from e


@clients_router.post("", response_model=ClientResponse, status_code=status.HTTP_201_CREATED)
async def create_client(
    request: ClientCreateRequest,
    db: Session = Depends(get_db),  # noqa: B008
    _: bool = Depends(verify_admin_key),  # noqa: B008
):  # -> Any:
    """
    Create a new client with configuration and generate an API key in Unkey.

    Args:
        request: Client creation request
        db: Database session

    Returns:
        ClientResponse: Created client information
    """
    # Generate client ID
    client_id = str(uuid.uuid4())

    try:
        # Create API key in Unkey
        result = await unkey_client.keys.create_key(
            api_id=api_settings.UNKEY_API_ID,  # Your Unkey API ID
            prefix="rfq",  # Optional prefix for the key
            name=f"Client: {request.name}",  # Name for the key
            meta={"client_id": client_id, "client_name": request.name},
            expires_at=None,  # No expiration
            ratelimit=None,  # No rate limit
            enabled=True,
        )

        if not result.is_ok:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to create API key: {result.unwrap_err()}",
            )

        key_response = result.unwrap()

        if not key_response.key:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create API key",
            )

        api_key = key_response.key

        # Create the client in our database (without storing the API key)
        client = RFQClient(
            client_id=client_id,
            name=request.name,
            api_key="unkey_managed",  # We don't store the actual key
            status="active",
            has_kb=getattr(request, "has_kb", False),  # Default to False if not provided
            has_instruction=getattr(request, "has_instruction", False),  # Default to False if not provided
        )
        db.add(client)

        # Create client configuration with enhanced model preferences
        model_preferences = request.llm_preferences or {}

        # Ensure the model preferences follow the new structure
        if model_preferences and "agent_models" not in model_preferences:
            # Handle legacy format or create default structure
            default_model = model_preferences.get("model", "google/gemini-flash-1.5")
            model_preferences = {
                "default_model": default_model,
                "agent_models": {"extraction": default_model, "validation": default_model, "normalizer": default_model, "formatter": default_model},
                "performance_settings": {
                    "extraction": {"temperature": 0.0, "max_tokens": 6144},
                    "validation": {"temperature": 0.1, "max_tokens": 8192},
                    "normalizer": {"temperature": 0.0, "max_tokens": 4096},
                    "formatter": {"temperature": 0.0, "max_tokens": 4096},
                },
                "fallback_model": default_model,
            }

        config = ClientConfiguration(
            client_id=client_id,
            extraction_output_schema=request.output_schema or {},
            validation_output_schema=request.output_schema or {},  # Can be different if needed
            extraction_rules=request.extraction_rules or {},
            validation_rules=request.validation_rules or {},
            normalization_rules=request.normalization_rules or {},
            model_preferences=model_preferences,
        )
        db.add(config)

        db.commit()

        return ClientResponse(
            client_id=client_id,
            name=request.name,
            client_code=api_key,  # Return the API key to the admin
            status="active",
        )

    except Exception as e:
        db.rollback()
        logger.error(f"Failed to create client: {e}")

        # Try to clean up Unkey key if it was created
        try:
            if "key_response" in locals() and key_response.key_id:
                await unkey_client.keys.delete_key(key_id=key_response.key_id)
        except Exception as cleanup_error:
            logger.error(f"Failed to clean up Unkey key: {cleanup_error}")

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create client: {str(e)}",
        ) from e


@clients_router.get("/{client_id}", response_model=ClientResponse)
async def get_client(
    client_id: str,
    db: Session = Depends(get_db),
    _: bool = Depends(verify_admin_key),  # noqa: B008
):
    """
    Get client information.

    Args:
        client_id: Client ID
        db: Database session

    Returns:
        ClientResponse: Client information
    """
    client = db.query(RFQClient).filter(RFQClient.client_id == client_id).first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Client not found: {client_id}",
        )

    # For security, we don't return the actual API key since it's managed by Unkey
    return ClientResponse(
        client_id=str(client.client_id),
        name=client.name,
        api_key="unkey_managed",  # We don't expose the actual key
        status=client.status,
    )


@clients_router.post("/{client_id}/regenerate-key")
async def regenerate_client_api_key(
    client_id: str,
    db: Session = Depends(get_db),
    _: bool = Depends(verify_admin_key),  # noqa: B008
):
    """
    Regenerate a client's API key in Unkey.

    Args:
        client_id: Client ID
        db: Database session

    Returns:
        dict: New API key information
    """
    # Verify client exists
    client = db.query(RFQClient).filter(RFQClient.client_id == client_id).first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Client not found: {client_id}",
        )

    try:
        # Create new API key in Unkey
        result = await unkey_client.keys.create_key(
            api_id=api_settings.UNKEY_API_ID,
            prefix="rfq",
            name=f"Client: {client.name} (regenerated)",
            meta={"client_id": client_id, "client_name": client.name},
            enabled=True,
        )

        if not result.is_ok:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to create new API key: {result.unwrap_err()}",
            )

        key_response = result.unwrap()

        if not key_response.key:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create new API key",
            )

        # Return the new key
        return {
            "client_id": client_id,
            "name": client.name,
            "new_api_key": key_response.key,
            "message": "API key regenerated successfully",
        }

    except Exception as e:
        if isinstance(e, HTTPException):
            raise e

        logger.error(f"Failed to regenerate API key: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to regenerate API key: {str(e)}",
        ) from e


@clients_router.put("/{client_id}/config")
async def update_client_config(
    client_id: str,
    config: dict[str, Any],
    db: Session = Depends(get_db),  # noqa: B008
    _: bool = Depends(verify_admin_key),  # noqa: B008
):
    """
    Update client configuration.

    Args:
        client_id: Client ID
        config: Configuration to update
        db: Database session

    Returns:
        dict: Success message
    """
    client_config = db.query(ClientConfiguration).filter(ClientConfiguration.client_id == client_id).first()

    if not client_config:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Client configuration not found: {client_id}",
        )

    try:
        # Update the configuration fields
        for key, value in config.items():
            if hasattr(client_config, key):
                setattr(client_config, key, value)

        db.commit()

        return {"message": "Client configuration updated successfully"}

    except Exception as e:
        db.rollback()
        logger.error(f"Failed to update client configuration: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update client configuration: {str(e)}",
        ) from None


@clients_router.post("/{client_id}/catalog/{catalog_type}")
async def update_client_catalog(
    client_id: str,
    catalog_type: str,
    entries: list[CatalogEntryRequest],
    db: Session = Depends(get_db),  # noqa: B008
    _: bool = Depends(verify_admin_key),  # noqa: B008
):
    """
    Update client catalog entries.

    Args:
        client_id: Client ID
        catalog_type: Catalog type name
        entries: Catalog entries to update
        db: Database session

    Returns:
        dict: Success message
    """
    # Verify client exists
    client = db.query(RFQClient).filter(RFQClient.client_id == client_id).first()
    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Client not found: {client_id}",
        )

    # Get or create catalog type
    catalog_type_obj = db.query(CatalogType).filter(CatalogType.name == catalog_type).first()
    if not catalog_type_obj:
        catalog_type_obj = CatalogType(name=catalog_type, description=f"Catalog for {catalog_type}")
        db.add(catalog_type_obj)
        db.flush()

    try:
        # Update or create catalog entries
        for entry in entries:
            existing_entry = (
                db.query(ReferenceData)
                .filter(
                    ReferenceData.client_id == client_id,
                    ReferenceData.catalog_type_id == catalog_type_obj.catalog_type_id,
                    ReferenceData.code == entry.code,
                )
                .first()
            )

            if existing_entry:
                # Update existing entry
                existing_entry.name = entry.name
                existing_entry.description = entry.description
                existing_entry.properties = entry.properties
            else:
                # Create new entry
                new_entry = ReferenceData(
                    client_id=client_id,
                    catalog_type_id=catalog_type_obj.catalog_type_id,
                    code=entry.code,
                    name=entry.name,
                    description=entry.description,
                    properties=entry.properties,
                    is_active=True,
                )
                db.add(new_entry)

        db.commit()

        return {"message": f"Updated {len(entries)} entries in {catalog_type} catalog for client {client_id}"}

    except Exception as e:
        db.rollback()
        logger.error(f"Failed to update client catalog: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update client catalog: {str(e)}",
        ) from e


@clients_router.post("/{client_id}/compatibility")
async def update_grade_coating_compatibility(
    client_id: str,
    compatibilities: list[dict[str, str]],
    db: Session = Depends(get_db),  # noqa: B008
    _: bool = Depends(verify_admin_key),  # noqa: B008
):
    """
    Update grade-coating compatibility for a client.

    Args:
        client_id: Client ID
        compatibilities: List of grade-coating pairs
        db: Database session

    Returns:
        dict: Success message
    """
    # Verify client exists
    client = db.query(RFQClient).filter(RFQClient.client_id == client_id).first()
    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Client not found: {client_id}",
        )

    try:
        # Add new compatibility entries
        for compat in compatibilities:
            if "grade_code" not in compat or "coating_code" not in compat:
                continue

            # Check if entry already exists
            existing = (
                db.query(GradeCoatingCompatibility)
                .filter(
                    GradeCoatingCompatibility.client_id == client_id,
                    GradeCoatingCompatibility.grade_code == compat["grade_code"],
                    GradeCoatingCompatibility.coating_code == compat["coating_code"],
                )
                .first()
            )

            if not existing:
                new_compat = GradeCoatingCompatibility(
                    client_id=client_id,
                    grade_code=compat["grade_code"],
                    coating_code=compat["coating_code"],
                )
                db.add(new_compat)

        db.commit()

        return {"message": f"Updated {len(compatibilities)} grade-coating compatibility entries for client {client_id}"}

    except Exception as e:
        db.rollback()
        logger.error(f"Failed to update compatibility: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update compatibility: {str(e)}",
        ) from e


@clients_router.get("/admin/clients", response_model=ClientListResponse)
async def list_all_clients_with_schemas(
    _: bool = Depends(verify_admin_key),
    db: Session = Depends(get_db),  # noqa: B008
):
    """
    List all clients with their schema information (admin only).

    Args:
        _: Result of admin key verification (ignored)
        db: Database session

    Returns:
        ClientListResponse: List of all clients with schema info
    """
    # Directly query clients and their configurations
    clients = db.query(RFQClient).all()

    clients_info = []
    for client in clients:
        # Get client configuration
        config = db.query(ClientConfiguration).filter(ClientConfiguration.client_id == client.client_id).first()

        # Check if client has custom schema
        has_custom_schema = config is not None and (config.extraction_output_schema is not None or config.validation_output_schema is not None)

        # Get schema info if available
        schema_info = {}
        if config and has_custom_schema:
            try:
                schema_info = DynamicMaterialSpecFactory.get_schema_info(client_id=str(client.client_id), db_session=db)
            except Exception as e:
                logger.warning(f"Could not get schema info for client {client.client_id}: {e}")
                schema_info = {"error": "Schema info unavailable"}

        client_info = ClientSchemaInfoResponse(
            client_id=str(client.client_id),
            client_name=client.name,
            has_custom_schema=has_custom_schema,
            schema_info=schema_info,
            last_updated=client.created_at.isoformat(),
        )
        clients_info.append(client_info)

    return ClientListResponse(clients=clients_info, total_count=len(clients_info))


@clients_router.post("/admin/schema/clear-cache")
async def clear_schema_cache(_: bool = Depends(verify_admin_key)):
    """
    Clear the schema cache (admin only).

    Args:
        _: Result of admin key verification (ignored)

    Returns:
        dict: Cache clear confirmation
    """
    # Clear the cache
    SchemaRegistry.clear_cache()
    logger.info("Schema cache cleared by admin")

    return {
        "success": True,
        "message": "Schema cache cleared successfully",
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
    }


@clients_router.get("/status", status_code=status.HTTP_200_OK)
async def clients_status(db: Session = Depends(get_db)):  # noqa: B008
    """
    Check the status of the client management service.

    Args:
        db: Database session

    Returns:
        dict: Status information about the client management service
    """
    # Get some statistics
    total_clients = db.query(RFQClient).count()
    active_clients = db.query(RFQClient).filter(RFQClient.status == "active").count()
    cached_schemas = len(SchemaRegistry._schemas)

    return {
        "status": "operational",
        "service": "Client Management",
        "version": "2.0.0",
        "features": ["dynamic_schemas", "client_specific_processing", "unkey_auth"],
        "statistics": {
            "total_clients": total_clients,
            "active_clients": active_clients,
            "cached_schemas": cached_schemas,
        },
    }
