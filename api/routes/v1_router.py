from fastapi import APIRouter

from api.routes.agents import agents_router
from api.routes.clients import clients_router
from api.routes.health import health_router
from api.routes.rfq import rfq_router
from api.routes.status import status_router

v1_router = APIRouter(prefix="/v1")
v1_router.include_router(status_router)
v1_router.include_router(agents_router)
# v1_router.include_router(playground_router)
v1_router.include_router(rfq_router)
v1_router.include_router(clients_router)
v1_router.include_router(health_router)
