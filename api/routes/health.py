"""
Health check endpoints for monitoring and operations.

This module provides health check and metrics endpoints
for monitoring the application's health and performance.
"""

from typing import Any

from fastapi import APIRouter, Request, Response, status
from sqlalchemy import text

from utils.log import get_app_logger
from utils.monitoring import HealthCheck

# Initialize application logger
logger = get_app_logger()

# Create router for health endpoints
health_router = APIRouter(
    prefix="/health",
    tags=["Health & Monitoring"],
    responses={
        503: {
            "description": "Service Unavailable",
            "content": {
                "application/json": {
                    "example": {
                        "status": "unhealthy",
                        "checks": {
                            "database": {
                                "status": "unhealthy",
                                "error": "Connection timeout",
                            }
                        },
                    }
                }
            },
        }
    },
)


@health_router.get(
    "",
    summary="Health Check",
    description="Get comprehensive health status of the application and its dependencies",
    response_model=dict[str, Any],
)
async def health_check(request: Request, response: Response) -> dict[str, Any]:
    """
    Comprehensive health check endpoint.

    This endpoint checks the health of:
    - Database connectivity
    - External services (Agno, OpenRouter, etc.)
    - Application status

    Returns:
        Dict containing health status of all components

    Status Codes:
        - 200: All components healthy
        - 503: One or more components unhealthy
    """
    try:
        health_check_service: HealthCheck = request.app.state.health_check
        health_status = await health_check_service.get_health_status()

        # Set response status code based on health
        if health_status["status"] != "healthy":
            response.status_code = status.HTTP_503_SERVICE_UNAVAILABLE

        return health_status

    except Exception as e:
        logger.error(f"Health check failed: {e}", exc_info=True)
        response.status_code = status.HTTP_503_SERVICE_UNAVAILABLE
        return {"status": "unhealthy", "error": str(e)}


@health_router.get(
    "/live",
    summary="Liveness Probe",
    description="Simple liveness check for container orchestration",
    response_model=dict[str, str],
)
async def liveness_probe() -> dict[str, str]:
    """
    Liveness probe endpoint.

    This endpoint is used by container orchestration systems
    (like Kubernetes) to determine if the application is alive.

    Returns:
        Dict with status "ok"
    """
    return {"status": "ok"}


@health_router.get(
    "/ready",
    summary="Readiness Probe",
    description="Check if the application is ready to serve requests",
    response_model=dict[str, Any],
)
async def readiness_probe(response: Response) -> dict[str, Any]:
    """
    Readiness probe endpoint.

    This endpoint checks if the application is ready to serve requests
    by verifying critical dependencies like database connectivity.

    Returns:
        Dict containing readiness status

    Status Codes:
        - 200: Application ready
        - 503: Application not ready
    """
    from db.session import db_engine

    ready_checks: dict[str, Any] = {"status": "ready", "checks": {}}

    # Check database
    try:
        with db_engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        ready_checks["checks"]["database"] = "ready"
    except Exception as e:
        ready_checks["status"] = "not_ready"
        ready_checks["checks"]["database"] = f"not_ready: {str(e)}"
        response.status_code = status.HTTP_503_SERVICE_UNAVAILABLE

    return ready_checks


@health_router.get(
    "/metrics",
    summary="Application Metrics",
    description="Get application performance metrics",
    response_model=dict[str, Any],
)
async def get_metrics(request: Request) -> dict[str, Any]:
    """
    Get application metrics.

    This endpoint provides performance metrics including:
    - Request counts
    - Error rates
    - Response times
    - Uptime

    Returns:
        Dict containing application metrics
    """
    try:
        health_check_service: HealthCheck = request.app.state.health_check
        metrics = await health_check_service.get_metrics()
        return metrics

    except Exception as e:
        logger.error(f"Failed to get metrics: {e}", exc_info=True)
        return {"error": str(e)}


@health_router.get(
    "/ping",
    summary="Ping",
    description="Simple ping endpoint for basic connectivity check",
    response_model=dict[str, str],
)
async def ping() -> dict[str, str]:
    """
    Simple ping endpoint.

    Returns:
        Dict with "pong" message
    """
    return {"message": "pong"}
