"""
API routes for RFQ processing with dynamic schema support.

This module provides REST API endpoints for processing Request for Quotation
(RFQ) emails, managing client schemas, and handling authentication.
"""

# ruff: noqa: B008

import json
import time
from typing import Any
from uuid import UUID

from fastapi import APIRouter, Depends, Header, HTTPException, Query, Request, status
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session
from unkey_py import Unkey

from api.settings import api_settings
from db.session import get_db
from db.tables.rfq_tables import ClientConfiguration, ProcessingHistory, RFQClient
from models.rfq.api import (
    ClientSchemaInfoResponse,
    RFQProcessRequest,
    RFQProcessResponse,
    SchemaManagementRequest,
    SchemaManagementResponse,
)
from models.rfq.dynamic_schema import DynamicMaterialSpecFactory, SchemaRegistry
from utils.exceptions import (
    AuthenticationError,
    ClientNotFoundError,
    DatabaseError,
    RFQProcessingError,
    UnkeyError,
)
from utils.json_encoder import safe_json_serialize
from utils.log import get_app_logger, log_api_request

# Removed ClientSchemaManager import - functionality implemented directly below
from workflows.wf_rfq_processor import create_rfq_workflow

# Initialize application logger
logger = get_app_logger()


# Helper functions to replace ClientSchemaManager functionality
def get_client_info_direct(db_session: Session, client_id: str) -> dict[str, Any] | None:
    """
    Get client information including schema details.

    Args:
        db_session: Database session
        client_id: Client ID

    Returns:
        Client information or None if not found
    """
    try:
        client_uuid = UUID(client_id) if isinstance(client_id, str) else client_id

        client = db_session.query(RFQClient).filter(RFQClient.client_id == client_uuid).first()
        if not client:
            return None

        config = db_session.query(ClientConfiguration).filter(ClientConfiguration.client_id == client_uuid).first()
        schema_info = DynamicMaterialSpecFactory.get_schema_info(client_id)

        return {
            "client_id": str(client.client_id),
            "name": client.name,
            "status": client.status,
            "has_custom_schema": config is not None and config.output_schema is not None,
            "schema_info": schema_info,
            "created_at": client.created_at.isoformat(),
            "updated_at": client.updated_at.isoformat(),
        }
    except Exception as e:
        logger.error(f"Error getting client info for {client_id}: {e}")
        return None


def update_client_schema_direct(db_session: Session, client_id: str, schema_definition: dict[str, Any]) -> bool:
    """
    Update client's output schema in database.

    Args:
        db_session: Database session
        client_id: Client ID
        schema_definition: New schema definition

    Returns:
        True if successful, False otherwise
    """
    try:
        # Validate schema by trying to create a model with it
        try:
            DynamicMaterialSpecFactory.create_material_spec_model(client_id=client_id, schema_definition=schema_definition)
        except Exception as e:
            logger.error(f"Invalid schema definition for client {client_id}: {e}")
            return False

        client_uuid = UUID(client_id) if isinstance(client_id, str) else client_id
        config = db_session.query(ClientConfiguration).filter(ClientConfiguration.client_id == client_uuid).first()

        if config:
            config.output_schema = schema_definition
            db_session.commit()

            # Clear cache for this client
            SchemaRegistry._schemas.pop(client_id, None)
            logger.info(f"Updated schema for client {client_id}")
            return True
        else:
            logger.error(f"Client configuration not found for {client_id}")
            return False

    except Exception as e:
        logger.error(f"Error updating client schema for {client_id}: {e}")
        db_session.rollback()
        return False


# Initialize Unkey client
try:
    unkey_client = Unkey(bearer_auth=api_settings.UNKEY_API_KEY)
    logger.info("✅ Unkey client initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize Unkey client: {e}")
    raise UnkeyError("Failed to initialize Unkey client") from e


# Create router
rfq_router = APIRouter(
    prefix="/rfq",
    tags=["RFQ Processor"],
    responses={
        401: {"description": "Authentication failed"},
        403: {"description": "Insufficient permissions"},
        404: {"description": "Resource not found"},
        500: {"description": "Internal server error"},
    },
)


async def verify_api_key_with_client(
    request: Request,
    api_key: str = Header(..., description="API key for authentication"),
    db: Session = Depends(get_db),  # noqa: B008
) -> str:
    """
    Verify API key using Unkey and return associated client ID.

    This dependency function authenticates requests by:
    1. Verifying the API key with Unkey
    2. Extracting client code from URL path
    3. Validating client code matches API key metadata
    4. Looking up and returning the client ID

    Args:
        request: FastAPI request object
        api_key: API key from Authorization header
        db: Database session

    Returns:
        str: Client ID (UUID) associated with the API key

    Raises:
        AuthenticationError: If API key is invalid or expired
        ClientNotFoundError: If client doesn't exist or is inactive
        HTTPException: For other HTTP errors
    """
    start_time = time.time()

    try:
        # Log authentication attempt
        log_api_request(
            method=request.method,
            path=request.url.path,
            client_ip=request.client.host if request.client else "unknown",
        )

        # Verify API key with Unkey
        logger.debug("Verifying API key with Unkey")
        result = unkey_client.keys.verify(request={"key": api_key, "api_id": api_settings.UNKEY_API_ID})

        # Validate Unkey response
        if not (hasattr(result, "v1_keys_verify_key_response") and result.v1_keys_verify_key_response):
            raise UnkeyError("Invalid response from Unkey API")

        verify_response = result.v1_keys_verify_key_response
        if not verify_response.valid:
            logger.warning("Invalid API key attempted")
            raise AuthenticationError("Invalid or expired API key")

        # Extract client code from URL path
        client_code = request.path_params.get("client_code")
        if not client_code:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Missing client identifier in URL",
            )

        # Validate client code matches API key metadata
        api_key_metadata = verify_response.meta
        if not api_key_metadata or "client_name" not in api_key_metadata:
            raise UnkeyError("API key missing required metadata")

        if client_code != api_key_metadata["client_name"]:
            logger.warning(f"Client code mismatch: {client_code} != {api_key_metadata['client_name']}")
            raise AuthenticationError("Client identifier does not match API key")

        # Look up client in database
        try:
            client = db.query(RFQClient).filter(RFQClient.client_code == client_code, RFQClient.status == "active").first()
        except SQLAlchemyError as e:
            logger.error(f"Database error during client lookup: {e}")
            raise DatabaseError("Failed to verify client") from e

        if not client:
            raise ClientNotFoundError(client_code)

        # Log successful authentication
        duration_ms = (time.time() - start_time) * 1000
        logger.info(f"API key verified for client {client.name} in {duration_ms:.2f}ms")

        return str(client.client_id)

    except (AuthenticationError, ClientNotFoundError, UnkeyError):
        # Re-raise our custom exceptions
        raise
    except HTTPException:
        # Re-raise FastAPI HTTP exceptions
        raise
    except Exception as e:
        # Catch any unexpected errors
        logger.error(f"Unexpected error during authentication: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Authentication service error",
        ) from e


@rfq_router.post(
    "/{client_code}/process",
    response_model=RFQProcessResponse,
    status_code=status.HTTP_200_OK,
    summary="Process RFQ Email",
    description="""
    Process an RFQ email and extract structured material specifications.

    This endpoint uses AI agents to analyze unstructured RFQ emails and
    extract standardized material specifications according to the client's
    configured schema.

    The extraction process includes:
    - Material grade identification
    - Coating specification extraction
    - Dimension parsing
    - Quantity extraction
    - Delivery requirements
    - Special instructions

    Each extracted field includes confidence scores to indicate
    extraction reliability.
    """,
)
async def process_client_rfq_endpoint(
    request: RFQProcessRequest,
    performance_mode: bool = Query(
        default=False,
        description=(
            "Enable performance optimizations for faster processing. "
            "Maintains all extraction logic and confidence tracking "
            "while disabling non-essential features like session storage."
        ),
    ),
    client_id: str = Depends(verify_api_key_with_client),
    db: Session = Depends(get_db),  # noqa: B008
) -> RFQProcessResponse:
    """
    Process an RFQ email for a specific client.

    Args:
        request: RFQ processing request with email content
        performance_mode: Enable performance optimizations
        client_id: Client ID from authentication
        db: Database session

    Returns:
        RFQProcessResponse: Extracted material specifications

    Raises:
        RFQProcessingError: If processing fails
        DatabaseError: If database operations fail
    """
    logger.info(f"RFQ process request received: {request.request_id} for client {client_id} (performance_mode={performance_mode})")
    start_time = time.time()

    # Set the client ID in the request
    request.client_id = UUID(client_id)

    # Force schema refresh if requested
    if request.force_schema_refresh:
        SchemaRegistry._schemas.pop(client_id, None)
        logger.info(f"Schema cache cleared for client {client_id}")

    try:
        # Create and run the workflow (environment variables will be read automatically)
        workflow = create_rfq_workflow(client_id=client_id, performance_mode=performance_mode)

        logger.debug(f"Running RFQ workflow for request {request.request_id}")
        responses = list(workflow.run(rfq_body=request.email_body))

        # Validate workflow response
        if not responses:
            raise RFQProcessingError(
                "Workflow did not return any responses",
                details={"request_id": request.request_id},
            )

        final_response = responses[-1]
        result = final_response.content

        # Debug the workflow response structure
        logger.info(f"🔍 API ROUTE: Workflow response keys: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")
        logger.info(f"🔍 API ROUTE: Result type: {type(result)}")
        if isinstance(result, dict):
            for key in result:
                value = result[key]
                if isinstance(value, dict):
                    logger.info(f"🔍 API ROUTE: {key} -> dict with keys: {list(value.keys())}")
                elif isinstance(value, list):
                    logger.info(f"🔍 API ROUTE: {key} -> list with {len(value)} items")
                else:
                    logger.info(f"🔍 API ROUTE: {key} -> {type(value)}")

        # Handle the final specifications from workflow
        if "final_specifications" in result:
            logger.info("🛸 API ROUTE: Taking final_specifications path")
            # Use the final processed specifications (preferred path)
            final_specs = result["final_specifications"]
            material_specs = final_specs.get("material_specs", [])

            # Add debugging
            logger.info(
                f"📝 API ROUTE: Found final_specifications with {len(material_specs) if isinstance(material_specs, list) else 'unknown'} specs"
            )
            logger.info(f"📝 API ROUTE: material_specs type: {type(material_specs)}")

            # material_specs should now be a direct list after our fixes
            if isinstance(material_specs, list):
                material_specs_list = material_specs
                extraction_metadata = final_specs.get("extraction_metadata", {})
            else:
                # Fallback for unexpected structure
                logger.warning(f"⚠️ API ROUTE: Unexpected material_specs structure: {type(material_specs)}")
                material_specs_list = []
                extraction_metadata = final_specs.get("extraction_metadata", {})

            logger.info(f"📝 API ROUTE: Extracted {len(material_specs_list)} specs from final_specifications")

        elif "material_specs" in result and isinstance(result["material_specs"], list) and len(result["material_specs"]) > 0:
            logger.info("🛸 API ROUTE: Taking direct material_specs path (workflow yielding at root level)")
            # NEW: Handle workflow yielding specs directly at root level
            material_specs_list = result["material_specs"]
            extraction_metadata = result.get("extraction_metadata", {})

            logger.info(f"📝 API ROUTE: Found {len(material_specs_list)} specs at root level")

        elif "corrected_rfq_data" in result:
            logger.info("🛸 API ROUTE: Taking corrected_rfq_data path")
            # Fallback: Use the validated and corrected data directly
            corrected_data = result["corrected_rfq_data"]
            material_specs = corrected_data.get("material_specs", [])

            # Add debugging
            logger.info(f"📝 API ROUTE: Found corrected_rfq_data with {len(material_specs) if isinstance(material_specs, list) else 'unknown'} specs")

            # material_specs should be a direct list
            if isinstance(material_specs, list):
                material_specs_list = material_specs
                extraction_metadata = corrected_data.get("extraction_metadata", {})
            else:
                # Fallback for unexpected structure
                logger.warning(f"⚠️ API ROUTE: Unexpected corrected material_specs structure: {type(material_specs)}")
                material_specs_list = []
                extraction_metadata = corrected_data.get("extraction_metadata", {})

            logger.info(f"📝 API ROUTE: Using corrected data with {len(material_specs_list)} specs")
        else:
            logger.info("🛸 API ROUTE: Taking final fallback path (extraction results)")
            # Final fallback: Use extraction results (legacy format)
            material_specs_list = result.get("result", [])
            extraction_metadata = result.get("extraction_metadata", {})
            logger.info("Using extraction results (validation may have failed)")

        # Ensure material_specs_list is a list
        if isinstance(material_specs_list, str):
            try:
                material_specs_list = json.loads(material_specs_list)
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse material specs JSON: {e}")
                material_specs_list = []

        # Ensure extraction_metadata is a dict
        if isinstance(extraction_metadata, str):
            try:
                extraction_metadata = json.loads(extraction_metadata)
            except json.JSONDecodeError:
                extraction_metadata = {}

        # Build processing log
        processing_log = []
        workflow_trace = result.get("workflow_trace", [])
        for step in workflow_trace:
            processing_log.append(
                {
                    "step": step.get("step"),
                    "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                    "client_id": client_id,
                    "status": "completed",
                }
            )

        # Calculate processing time
        processing_time = time.time() - start_time

        # Final debug before creating API response
        logger.info(f"🚀 API ROUTE: About to create response with {len(material_specs_list)} specs")
        if material_specs_list:
            sample_spec = material_specs_list[0]
            logger.info(f"🚀 API ROUTE: Sample spec keys: {list(sample_spec.keys()) if isinstance(sample_spec, dict) else 'Not a dict'}")
        else:
            logger.error("⚠️ API ROUTE: material_specs_list is EMPTY! This is the problem.")

        # Create response with client's schema
        response = RFQProcessResponse.create_from_processed_data_for_extractor(
            request_id=request.request_id,
            email_id=request.email_id,
            client_id=UUID(client_id),
            raw_material_specs=material_specs_list,
            processing_log=processing_log,
            processing_time=processing_time,
            db_session=db,
            use_generic_for_extraction=False,
        )

        # Record processing history
        try:
            history = ProcessingHistory(
                client_id=UUID(client_id),
                request_id=request.request_id,
                email_id=request.email_id,
                input_text=request.email_body,
                output_json=safe_json_serialize(response.dict()),
                processing_time=processing_time,
            )
            db.add(history)
            db.commit()
        except SQLAlchemyError as e:
            logger.error(f"Failed to save processing history: {e}")
            # Don't fail the request if history save fails
            db.rollback()

        logger.info(f"RFQ processed successfully for client {client_id} in {processing_time:.2f}s")
        return response

    except RFQProcessingError:
        # Re-raise processing errors
        raise
    except Exception as e:
        logger.error(f"Unexpected error processing RFQ: {e}")

        # Try to record error in history
        try:
            processing_time = time.time() - start_time
            history = ProcessingHistory(
                client_id=UUID(client_id),
                request_id=request.request_id,
                email_id=request.email_id,
                input_text=request.email_body,
                output_json=safe_json_serialize({"error": str(e)}),
                processing_time=processing_time,
            )
            db.add(history)
            db.commit()
        except Exception as hist_e:
            logger.error(f"Failed to save error history: {hist_e}")
            db.rollback()

        raise RFQProcessingError(
            f"Failed to process RFQ: {str(e)}",
            details={
                "request_id": request.request_id,
                "client_id": client_id,
                "processing_time": time.time() - start_time,
            },
        ) from e


# Schema Management Endpoints


@rfq_router.get(
    "/{client_code}/schema/info",
    response_model=ClientSchemaInfoResponse,
    summary="Get Client Schema Information",
    description="""
    Retrieve schema configuration for the authenticated client.

    Returns details about:
    - Output schema structure
    - Extraction rules
    - Validation rules
    - Last update timestamp
    """,
)
async def get_client_schema_info(
    request: Request,
    client_id: str = Depends(verify_api_key_with_client),
    db: Session = Depends(get_db),  # noqa: B008
) -> ClientSchemaInfoResponse:
    """
    Get schema information for the authenticated client.

    Args:
        request: FastAPI request object
        client_id: Client ID from authentication
        db: Database session

    Returns:
        ClientSchemaInfoResponse: Client's schema configuration

    Raises:
        ClientNotFoundError: If client not found
    """
    try:
        client_info = get_client_info_direct(db, client_id)

        if not client_info:
            raise ClientNotFoundError(client_id)

        return ClientSchemaInfoResponse(**client_info)

    except ClientNotFoundError:
        raise
    except Exception as e:
        logger.error(f"Error retrieving schema info: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve schema information",
        ) from e


@rfq_router.post(
    "/{client_code}/schema/update",
    response_model=SchemaManagementResponse,
    summary="Update Client Schema",
    description="""
    Update the schema configuration for the authenticated client.

    Allows modification of:
    - Output schema fields and types
    - Extraction patterns and rules
    - Validation constraints
    - Normalization rules

    Use validate_only=true to test changes without saving.
    """,
)
async def update_client_schema(
    schema_request: SchemaManagementRequest,
    client_id: str = Depends(verify_api_key_with_client),
    db: Session = Depends(get_db),  # noqa: B008
) -> SchemaManagementResponse:
    """
    Update schema for the authenticated client.

    Args:
        schema_request: Schema update request
        client_id: Client ID from authentication
        db: Database session

    Returns:
        SchemaManagementResponse: Update operation result

    Raises:
        HTTPException: If update fails or unauthorized
    """
    # Verify client can only update their own schema
    if str(schema_request.client_id) != client_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Cannot update schema for a different client",
        )

    try:
        if schema_request.validate_only:
            # Validate without saving
            schema_info = DynamicMaterialSpecFactory.get_schema_info(client_id, db)
            return SchemaManagementResponse(
                success=True,
                message="Schema validation successful",
                schema_info=schema_info,
            )

        # Update the schema
        success = update_client_schema_direct(db, client_id, schema_request.schema_definition)

        if success:
            # Clear schema cache
            SchemaRegistry._schemas.pop(client_id, None)

            schema_info = DynamicMaterialSpecFactory.get_schema_info(client_id, db)
            return SchemaManagementResponse(
                success=True,
                message="Schema updated successfully",
                schema_info=schema_info,
            )
        else:
            return SchemaManagementResponse(
                success=False,
                message="Failed to update schema",
                errors=["Database update failed"],
            )

    except Exception as e:
        logger.error(f"Error updating schema: {e}")
        return SchemaManagementResponse(
            success=False,
            message="Schema update failed",
            errors=[str(e)],
        )


@rfq_router.get(
    "/schema/template",
    summary="Get Schema Template",
    description="Get a template for creating custom schemas with examples.",
)
async def get_schema_template() -> dict[str, Any]:
    """
    Get schema template with examples and documentation.

    Returns:
        dict: Schema template with field definitions and examples
    """
    return {
        "template_version": "1.0",
        "description": "Schema template for RFQ material specifications",
        "output_schema": {
            "fields": {
                "grade": {
                    "type": "string",
                    "required": True,
                    "description": "Steel grade specification",
                    "examples": ["DX51D", "S355J2", "DC01"],
                },
                "coating": {
                    "type": "string",
                    "required": False,
                    "description": "Coating specification",
                    "examples": ["Z275", "Z140", "AZ150"],
                },
                "form": {
                    "type": "string",
                    "required": True,
                    "enum": ["coil", "sheet", "plate", "strip"],
                    "description": "Material form",
                },
                "thickness_min": {
                    "type": "number",
                    "required": True,
                    "description": "Minimum thickness in mm",
                },
                "thickness_max": {
                    "type": "number",
                    "required": True,
                    "description": "Maximum thickness in mm",
                },
                "width_min": {
                    "type": "number",
                    "required": True,
                    "description": "Minimum width in mm",
                },
                "width_max": {
                    "type": "number",
                    "required": True,
                    "description": "Maximum width in mm",
                },
                "quantity": {
                    "type": "number",
                    "required": True,
                    "description": "Quantity requested",
                },
                "quantity_unit": {
                    "type": "string",
                    "required": True,
                    "enum": ["tons", "kg", "pieces", "meters"],
                    "description": "Unit of quantity",
                },
            }
        },
        "extraction_rules": {
            "patterns": {
                "grade": r"[A-Z][0-9]{2}[A-Z]?[0-9]?",
                "coating": r"[A-Z]{1,2}[0-9]{2,3}",
            },
            "keywords": {
                "grade": ["grade", "steel", "material"],
                "coating": ["coating", "galvanized", "zinc"],
            },
        },
        "validation_rules": {
            "thickness": {
                "min": 0.1,
                "max": 100,
            },
            "width": {
                "min": 100,
                "max": 2500,
            },
        },
    }
