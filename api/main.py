import os
import time
from contextlib import asynccontextmanager

from fastapi import FastAP<PERSON>
from sqlalchemy import text
from starlette.middleware.cors import CORSMiddleware

from api.routes.v1_router import v1_router
from api.settings import api_settings
from db.session import db_engine
from utils.exceptions.handlers import register_error_handlers
from utils.log import configure_root_logging, get_app_logger
from utils.monitoring import register_monitoring_middleware

# Startup
logger = get_app_logger()
logger.info("Starting Vanilla Steel AI application...")


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Manage application lifecycle.
    """
    # Configure logging
    configure_root_logging()

    # Store settings in app state for access in handlers
    app.state.settings = api_settings
    app.state.start_time = time.time()

    # Initialize external services
    try:
        # Initialize Agno telemetry
        logger.info("Initializing Agno telemetry...")

        # Check if we should wait for database (respect WAIT_FOR_DB env var)
        wait_for_db = os.getenv("WAIT_FOR_DB", "true").lower() in ("true", "1")

        if wait_for_db:
            # Initialize database connection pool
            logger.info("Testing database connection...")
            with db_engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            logger.info("Database connection successful")
        else:
            logger.info("WAIT_FOR_DB=false - skipping database connection test")

    except Exception as e:
        if wait_for_db:
            logger.error(f"Failed to initialize services: {e}")
            raise
        else:
            logger.warning(f"Failed to initialize database (continuing anyway): {e}")

    logger.info("Application startup complete")

    yield

    # Shutdown
    logger.info("Shutting down Vanilla Steel AI application...")
    try:
        # Only dispose if we initialized the database
        if wait_for_db:
            db_engine.dispose()
            logger.info("Database connections closed")
    except Exception as e:
        logger.error(f"Error during shutdown: {e}")
    logger.info("Application shutdown complete")


def create_app() -> FastAPI:
    """
    Create and configure a FastAPI application.

    This function initializes the FastAPI application with:
    - API routes and routers
    - CORS middleware configuration
    - Documentation settings based on environment
    - Proper error handling and logging
    - Monitoring and metrics
    - Request/response middleware

    Returns:
        FastAPI: Configured FastAPI application instance

    Raises:
        Exception: If application configuration fails

    Example:
        >>> app = create_app()
        >>> import uvicorn
        >>> uvicorn.run(app, host="0.0.0.0", port=8000)
    """
    try:
        logger.info(f"Creating FastAPI application for {api_settings.runtime_env} environment")

        # Create FastAPI App with environment-specific configuration
        app: FastAPI = FastAPI(
            title=api_settings.title,
            version=api_settings.version,
            description="""Vanilla Steel AI - RFQ Processing API

            This API provides intelligent processing of Request for Quotation (RFQ) emails
            for steel materials. It uses advanced AI agents to extract, validate, and
            structure material specifications from unstructured email content.

            ## Features
            - Automatic extraction of steel specifications
            - Validation against reference catalogs
            - Client-specific schema support
            - Real-time processing with streaming support
            - Comprehensive error handling and monitoring

            ## Authentication
            All endpoints require API key authentication via Bearer token.
            """,
            docs_url="/docs" if api_settings.docs_enabled else None,
            redoc_url="/redoc" if api_settings.docs_enabled else None,
            openapi_url="/openapi.json" if api_settings.docs_enabled else None,
            lifespan=lifespan,
        )

        # Register error handlers
        register_error_handlers(app)
        logger.info("Error handlers registered successfully")

        # Register monitoring middleware
        register_monitoring_middleware(app)
        logger.info("Monitoring middleware registered successfully")

        # Add API routes
        app.include_router(v1_router)
        logger.info("API routes registered successfully")

        # Configure CORS middleware
        cors_origins: list[str] = api_settings.cors_origin_list or []
        app.add_middleware(
            CORSMiddleware,
            allow_origins=cors_origins,
            allow_credentials=True,
            allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
            allow_headers=["*"],
        )
        logger.info(f"CORS middleware configured with {len(cors_origins)} allowed origins")

        # Log application configuration
        logger.info("FastAPI application created successfully:")
        logger.info(f"  - Title: {app.title}")
        logger.info(f"  - Version: {app.version}")
        logger.info(f"  - Docs enabled: {api_settings.docs_enabled}")
        logger.info(f"  - Environment: {api_settings.runtime_env}")

        return app

    except Exception as e:
        logger.error(f"Failed to create FastAPI application: {e}")
        raise


# Create FastAPI app
app = create_app()
