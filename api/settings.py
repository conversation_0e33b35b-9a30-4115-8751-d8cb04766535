from enum import Enum

from pydantic import Field, field_validator
from pydantic_core.core_schema import FieldValidationInfo
from pydantic_settings import BaseSettings, SettingsConfigDict


class EnvironmentType(str, Enum):
    """Environment types for the application."""

    DEV = "dev"
    STG = "stg"
    PRD = "prd"
    TEST = "test"


class ApiSettings(BaseSettings):
    """
    Api settings that can be set using environment variables.

    This class includes validation for all required environment variables.
    If any required variable is missing or invalid, the application will
    fail to start.

    Reference: https://pydantic-docs.helpmanual.io/usage/settings/
    """

    # Api title and version
    title: str = "vanilla-steel-ai"
    version: str = "1.0"

    # Api runtime_env
    runtime_env: EnvironmentType = Field(
        default=EnvironmentType.DEV,
        description="Runtime environment for the API",
        validation_alias="RUNTIME_ENV",  # This maps RUNTIME_ENV to runtime_env
    )

    # Set to False to disable docs at /docs and /redoc
    docs_enabled: bool = True

    # Cors origin list to allow requests from.
    cors_origin_list: list[str] | None = Field(None, validate_default=True)

    # API keys (required)
    OPENROUTER_API_KEY: str = Field(..., description="OpenRouter API key")
    AGNO_API_KEY: str = Field(..., description="Agno API key for agent framework")
    UNKEY_API_KEY: str = Field(..., description="Unkey API key for API key management")
    UNKEY_API_ID: str = Field(..., description="Unkey API ID from dashboard")
    AGNO_WORKSPACE_ID: str = Field(..., description="AGNO Workspace Key")
    AGNO_ENABLE_TELEMETRY: str = Field(..., description="Enable telemetry flag, e.g. '1' or 'true'")
    EXA_API_KEY: str = Field(..., description="EXA API KEy")

    # Admin API key for client management
    ADMIN_API_KEY: str = Field(..., description="Admin API key for client onboarding and management")

    # Database Connection
    DB_HOST: str = Field(..., description="Database host address")
    DB_PORT: int = Field(..., description="Database port number")
    DB_USER: str = Field(..., description="Database username")
    DB_PASS: str = Field(..., description="Database password")
    DB_DATABASE: str = Field(..., description="Database name")
    POSTGRES_INIT_SCHEMA: str = Field(..., description="Database name")

    # API Configuration
    API_TIMEOUT: int = Field(default=120, description="API request timeout in seconds")
    MAX_RETRIES: int = Field(default=3, description="Maximum number of retry attempts")

    # Agent Configuration
    DEBUG_MODE: bool = Field(default=False, description="Enable debug mode for agents")
    PERFORMANCE_MODE: bool = Field(default=False, description="Enable performance mode for agents")

    # Performance settings
    REQUEST_RATE_LIMIT: int = Field(default=100, description="Requests per minute limit")
    MAX_CONCURRENT_REQUESTS: int = Field(default=10, description="Maximum concurrent requests")

    # Default Client Configuration
    DEFAULT_CLIENT_ID: str = Field(
        default="00000000-0000-0000-0000-000000000000",
        description="Default client ID used for generic operations when no specific client is provided",
    )

    @field_validator("cors_origin_list", mode="before")
    def set_cors_origin_list(cls, cors_origin_list, info: FieldValidationInfo):
        valid_cors = cors_origin_list or []

        # Add app.agno.com to cors to allow requests from the Agno playground.
        valid_cors.append("https://app.agno.com")
        # Add localhost to cors to allow requests from the local environment.
        valid_cors.append("http://localhost")
        # Add localhost:3000 to cors to allow requests from local Agent UI.
        valid_cors.append("http://localhost:3000")

        return valid_cors

    model_config = SettingsConfigDict(case_sensitive=True, env_file=".env", env_file_encoding="utf-8")


api_settings = ApiSettings()


def get_settings() -> ApiSettings:
    """Return the settings singleton for dependency injection."""
    return api_settings
