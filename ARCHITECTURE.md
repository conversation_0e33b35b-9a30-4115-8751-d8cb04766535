# Vanilla Steel AI - Production Architecture Guide

## 🏗️ Project Structure

```
vanilla-steel-ai/
├── agents/                 # AI agent implementations
│   ├── ag_rfq_extractor.py # Main RFQ extraction agent
│   ├── ag_rfq_validator.py # RFQ validation agent
│   ├── operator.py         # Agent factory and management
│   ├── sage.py            # General assistant agent
│   └── scholar.py         # Research agent
├── api/                   # FastAPI application
│   ├── main.py           # Application entry point
│   ├── settings.py       # Configuration management
│   └── routes/           # API endpoints
│       ├── agents.py     # Agent interaction endpoints
│       ├── rfq.py        # RFQ processing endpoints
│       ├── health.py     # Health check endpoints
│       └── v1_router.py  # Version 1 API router
├── db/                    # Database layer
│   ├── session.py        # Database session management
│   ├── settings.py       # Database configuration
│   └── tables/           # SQLAlchemy models
├── models/               # Pydantic models
│   └── rfq/
│       ├── api.py        # API request/response models
│       └── dynamic_schema.py # Dynamic schema support
├── utils/                # Utility modules
│   ├── exceptions/       # Custom exceptions
│   │   ├── __init__.py   # Exception definitions
│   │   └── handlers.py   # Error handlers
│   ├── monitoring.py     # Monitoring and metrics
│   ├── log.py           # Logging configuration
│   └── json_encoder.py  # JSON serialization
├── workflows/           # Business logic workflows
│   └── wf_rfq_processor.py # RFQ processing workflow
└── tests/              # Test suite
    ├── conftest.py     # Test fixtures
    ├── unit/           # Unit tests
    ├── integration/    # Integration tests
    └── e2e/           # End-to-end tests
```

## 🚀 Production Improvements Implemented

### 1. Error Handling & Custom Exceptions

**Location**: `utils/exceptions/`

- **Hierarchical Exception Structure**: Base `VanillaSteelError` with specific exceptions for different error types
- **Rich Error Information**: Each exception includes error codes, messages, and contextual details
- **HTTP-Aware Exceptions**: API exceptions include status codes for proper HTTP responses
- **Centralized Error Handlers**: Consistent error formatting across the application

**Example Usage**:
```python
from utils.exceptions import ClientConfigurationError, RFQProcessingError

# Specific error with context
raise ClientConfigurationError(
    "Invalid schema configuration",
    details={"client_id": client_id, "field": "output_schema"}
)

# API error with status code
raise AuthenticationError("Invalid API key")  # Returns 401
```

### 2. Comprehensive Documentation

**Improvements**:
- **Docstrings**: Every function has detailed docstrings with:
  - Description
  - Args with types
  - Returns with types
  - Raises section
  - Examples where helpful
- **Type Hints**: Complete type annotations throughout
- **Module Documentation**: Each module has a header explaining its purpose
- **API Documentation**: OpenAPI/Swagger documentation with examples

**Example**:
```python
def get_rfq_extractor(
    model_id: str = "google/gemini-flash-1.5",
    user_id: Optional[str] = None,
    session_id: Optional[str] = None,
    debug_mode: bool = True,
    performance_mode: bool = False,
) -> Agent:
    """
    Create and return the RFQ Extraction Agent.

    This agent processes Request for Quotation (RFQ) emails for steel materials,
    extracting structured specifications and returning them as JSON.

    Args:
        model_id: The LLM model ID to use
        user_id: Optional user/client ID for client-specific processing
        session_id: Optional session ID for conversation tracking
        debug_mode: Enable debug logging and detailed error messages
        performance_mode: Enable performance optimizations

    Returns:
        Agent: Configured RFQ extraction agent

    Raises:
        ClientConfigurationError: If client configuration is invalid
        AgnoServiceError: If agent creation fails

    Example:
        >>> agent = get_rfq_extractor(model_id="gpt-4", user_id="client-123")
        >>> response = await agent.arun("Extract specs from: 100 tons DX51D...")
    """
```

### 3. Monitoring & Observability

**Location**: `utils/monitoring.py`, `api/routes/health.py`

**Features**:
- **Request Tracking**: Automatic request ID generation and propagation
- **Performance Monitoring**: Request/response time tracking
- **Health Checks**: Comprehensive health endpoints
- **Metrics Collection**: Request counts, error rates, response times
- **Slow Request Detection**: Warnings for requests exceeding thresholds

**Endpoints**:
- `/v1/health` - Comprehensive health status
- `/v1/health/live` - Kubernetes liveness probe
- `/v1/health/ready` - Kubernetes readiness probe
- `/v1/health/metrics` - Application metrics

### 4. Enhanced Logging

**Location**: `utils/log.py`

**Features**:
- **Structured Logging**: Consistent log format with context
- **Environment-Aware**: Different log levels and formats for dev/staging/prod
- **Performance Logging**: Database operations and API requests tracked
- **Rich Integration**: Pretty formatting in development
- **Context Preservation**: Request IDs and client IDs included

**Usage**:
```python
from utils.log import get_app_logger, log_api_request, log_database_operation

# Standard logging
logger.info(f"Processing RFQ for client {client_id}")

# API request logging
log_api_request(method="POST", path="/v1/rfq/process", client_ip="*******")

# Database operation logging
log_database_operation(
    operation="INSERT",
    table="processing_history",
    duration_ms=45.2,
    success=True
)
```

### 5. Comprehensive Testing

**Location**: `tests/`

**Test Categories**:
- **Unit Tests**: Test individual components in isolation
- **Integration Tests**: Test component interactions
- **E2E Tests**: Test complete workflows

**Features**:
- **Fixtures**: Reusable test data and mocks
- **Markers**: Categorize tests (unit, integration, slow, external)
- **Coverage**: Track code coverage with reports
- **Test Runner**: Custom script for convenient test execution

**Running Tests**:
```bash
# Run all tests
python scripts/run_tests.py

# Run with coverage
python scripts/run_tests.py --coverage

# Run only unit tests
python scripts/run_tests.py --unit

# Run specific test
python scripts/run_tests.py tests/unit/test_exceptions.py -v
```

### 6. Database Improvements

**Location**: `db/session.py`

**Features**:
- **Connection Pooling**: Optimized for environment (dev/staging/prod)
- **Event Listeners**: Monitor connections and queries
- **Retry Logic**: Handle transient failures
- **Context Managers**: Safe session handling
- **Performance Tracking**: Log slow queries

### 7. Security Enhancements

**Implemented**:
- **API Key Authentication**: Via Unkey integration
- **Client Isolation**: Each client can only access their own data
- **Input Validation**: Pydantic models validate all inputs
- **Error Sanitization**: Production errors don't leak sensitive info
- **SSL Support**: Database SSL configuration for production

## 📋 Configuration Management

### Environment Variables

**Required**:
```bash
# API Keys
OPENROUTER_API_KEY=xxx
AGNO_API_KEY=xxx
AGNO_WORKSPACE_ID=xxx
UNKEY_API_KEY=xxx
UNKEY_API_ID=xxx

# Database
DB_HOST=localhost
DB_PORT=5432
DB_USER=xxx
DB_PASS=xxx
DB_DATABASE=xxx

# Environment
RUNTIME_ENV=dev|stg|prd
```

**Optional**:
```bash
# Performance
REQUEST_RATE_LIMIT=100
MAX_CONCURRENT_REQUESTS=10

# Logging
LOG_LEVEL=DEBUG|INFO|WARNING|ERROR
SQL_ECHO=true|false

# Testing
TESTING=True
```

## 🚦 API Error Responses

All API errors follow a consistent format:

```json
{
  "error": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "code": "CLIENT_NOT_FOUND",
    "message": "Client with ID xxx not found",
    "details": {
      "client_id": "xxx"
    }
  },
  "path": "/v1/rfq/process",
  "timestamp": 1634567890.123
}
```

**HTTP Status Codes**:
- `200` - Success
- `400` - Bad Request
- `401` - Authentication Failed
- `403` - Insufficient Permissions
- `404` - Resource Not Found
- `422` - Validation Error
- `429` - Rate Limit Exceeded
- `500` - Internal Server Error
- `503` - Service Unavailable

## 🔍 Monitoring Integration

### Metrics Exposed

```json
{
  "timestamp": "2024-01-01T12:00:00Z",
  "uptime_seconds": 3600,
  "requests": {
    "total": 1000,
    "errors": 5,
    "error_rate": 0.005,
    "avg_response_time_ms": 150.5
  }
}
```

### Health Check Response

```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00Z",
  "uptime_seconds": 3600,
  "version": "1.0.0",
  "environment": "prd",
  "checks": {
    "database": {
      "status": "healthy",
      "response_time_ms": 5.2
    },
    "external_services": {
      "agno": {
        "status": "healthy",
        "workspace_id": "6be40047..."
      }
    }
  }
}
```

## 🔧 Development Workflow

### 1. Local Development

```bash
# Set up environment
./scripts/dev_setup.sh
source .venv/bin/activate

# Start services
ag ws up

# Run tests
python scripts/run_tests.py

# Format code
ruff format .

# Lint code
ruff check . --fix

# Type check
mypy .
```

### 2. Pre-commit Hooks

Configured in `.pre-commit-config.yaml`:
- Code formatting (Ruff)
- Linting (Ruff)
- Type checking (mypy)
- Large file prevention
- Branch protection

### 3. Debugging

**VS Code Launch Configuration**:
```json
{
  "name": "FastAPI Debug",
  "type": "python",
  "request": "launch",
  "module": "uvicorn",
  "args": [
    "api.main:app",
    "--reload",
    "--port", "8000"
  ],
  "env": {
    "RUNTIME_ENV": "dev",
    "LOG_LEVEL": "DEBUG"
  }
}
```

## 🚀 Deployment Considerations

### Production Checklist

- [ ] Remove all hardcoded secrets
- [ ] Set `RUNTIME_ENV=prd`
- [ ] Configure SSL for database
- [ ] Set up monitoring/alerting
- [ ] Configure rate limiting
- [ ] Enable CORS for specific origins only
- [ ] Set up log aggregation
- [ ] Configure health check probes
- [ ] Set up backup strategy
- [ ] Document deployment process

### Performance Optimization

1. **Enable Performance Mode**: For high-throughput scenarios
2. **Database Indexing**: Ensure proper indexes on frequently queried fields
3. **Connection Pooling**: Tune pool size based on load
4. **Caching**: Implement Redis for frequently accessed data
5. **Async Processing**: Use background tasks for heavy operations

## 📚 Additional Resources

- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [SQLAlchemy Documentation](https://www.sqlalchemy.org/)
- [Pydantic Documentation](https://pydantic-docs.helpmanual.io/)
- [Agno Documentation](https://docs.agno.com/)
- [Pytest Documentation](https://docs.pytest.org/)
