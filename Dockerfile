FROM agnohq/python:3.12

ARG USER=app
ARG APP_DIR=/app
ENV APP_DIR=${APP_DIR}

# Create user and home directory
RUN groupadd -g 61000 ${USER} \
  && useradd -g 61000 -u 61000 -ms /bin/bash -d ${APP_DIR} ${USER}

# Install system dependencies including PostgreSQL client and netcat
RUN apt-get update && apt-get install -y \
  postgresql-client \
  netcat-traditional \
  && rm -rf /var/lib/apt/lists/*

WORKDIR ${APP_DIR}


# Install critical dependencies directly (unkey-py will be installed from requirements.txt)
RUN uv pip install --system typing-inspect==0.9.0 mypy_extensions==1.0.0 typing-extensions>=4.12.0

# Copy requirements.txt
COPY requirements.txt ./

# Install the rest of the requirements
RUN uv pip install --system -r requirements.txt

# Verify the installation
RUN uv pip list | grep unkey
RUN uv pip list | grep typing-inspect
RUN uv pip list | grep mypy-extensions

# Copy project files
COPY . .

# Set permissions for the /app directory and make entrypoint executable
RUN chown -R ${USER}:${USER} ${APP_DIR} && \
    chmod +x ${APP_DIR}/scripts/entrypoint.sh && \
    ls -la ${APP_DIR}/scripts/entrypoint.sh

# Switch to non-root user
USER ${USER}

ENTRYPOINT ["/bin/bash", "/app/scripts/entrypoint.sh"]
CMD ["api"]
