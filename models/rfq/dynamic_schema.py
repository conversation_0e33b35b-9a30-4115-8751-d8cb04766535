from typing import Any
from uuid import UUID

from pydantic import BaseModel, Field, create_model
from sqlalchemy.orm import Session

# Import database models
from db.tables.rfq_tables import ClientConfiguration
from utils.rfq.config import get_default_client_id

# Generic client UUID for extractor template
GENERIC_CLIENT_ID = get_default_client_id()


class SchemaRegistry:
    """Registry for caching dynamically created schemas."""

    _schemas: dict[str, type[BaseModel]] = {}
    _default_schema: type[BaseModel] | None = None

    @classmethod
    def register_schema(cls, client_id: str, schema_class: type[BaseModel]) -> None:
        """Register a dynamically created schema for a client."""
        cls._schemas[client_id] = schema_class

    @classmethod
    def get_schema(cls, client_id: str) -> type[BaseModel] | None:
        """Get a cached schema for a client."""
        return cls._schemas.get(client_id)

    @classmethod
    def clear_cache(cls) -> None:
        """Clear the schema cache."""
        cls._schemas.clear()

    @classmethod
    def set_default_schema(cls, schema_class: type[BaseModel]) -> None:
        """Set the default schema to use when client doesn't have custom schema."""
        cls._default_schema = schema_class

    @classmethod
    def get_default_schema(cls) -> type[BaseModel] | None:
        """Get the default schema."""
        return cls._default_schema


class DynamicMaterialSpecFactory:
    """Factory for creating dynamic Material Specification models based on client schemas."""

    # Minimal fallback schema - only used if database is completely unavailable
    MINIMAL_FALLBACK_SCHEMA = {
        "grade": {
            "type": "string",
            "required": False,
            "description": "Steel grade",
        },
        "thickness_min": {
            "type": "number",
            "required": False,
            "description": "Minimum thickness in mm",
        },
        "extraction_confidence": {
            "type": "object",
            "required": False,
            "description": "Confidence scores for extraction",
        },
    }

    @classmethod
    def load_schema_from_database(cls, db_session: Session, client_id: str) -> dict[str, Any]:
        """Load client schema from database tables.

        Args:
            db_session: Database session
            client_id: Client UUID as string

        Returns:
            Schema definition dictionary
        """
        try:
            # Convert string to UUID for database query
            client_uuid = UUID(client_id) if isinstance(client_id, str) else client_id
        except (ValueError, TypeError):
            # Invalid UUID, fall back to generic
            client_uuid = UUID(GENERIC_CLIENT_ID)

        # Query client configuration from database
        config = db_session.query(ClientConfiguration).filter(ClientConfiguration.client_id == client_uuid).first()

        if config and config.output_schema:
            return config.output_schema

        # Fallback to generic template from database
        generic_uuid = UUID(GENERIC_CLIENT_ID)
        generic_config = db_session.query(ClientConfiguration).filter(ClientConfiguration.client_id == generic_uuid).first()

        if generic_config and generic_config.output_schema:
            return generic_config.output_schema

        # Final fallback to minimal hardcoded schema (only if database completely unavailable)
        return cls.MINIMAL_FALLBACK_SCHEMA

    @classmethod
    def _convert_schema_to_pydantic_fields(cls, schema: dict[str, Any]) -> dict[str, Any]:
        """Convert a JSON schema to Pydantic field definitions."""
        pydantic_fields = {}

        for field_name, field_def in schema.items():
            field_type = field_def.get("type", "string")
            required = field_def.get("required", False)
            description = field_def.get("description", "")
            default_value = field_def.get("default")

            # Map JSON schema types to Python types
            python_type: Any
            if field_type == "string":
                python_type = str
            elif field_type == "number":
                python_type = float
            elif field_type == "integer":
                python_type = int
            elif field_type == "boolean":
                python_type = bool
            elif field_type == "array":
                items_type = field_def.get("items", {}).get("type", "string")
                if items_type == "string":
                    python_type = list[str]  # type: ignore
                elif items_type == "number":
                    python_type = list[float]  # type: ignore
                elif items_type == "integer":
                    python_type = list[int]  # type: ignore
                else:
                    python_type = list[Any]  # type: ignore
            else:
                python_type = Any

            # Create the field
            field: tuple[Any, Any]
            if not required:
                if field_type == "array" and default_value is None:
                    field = (
                        python_type | None,
                        Field(default_factory=lambda: [], description=description),
                    )
                else:
                    field = (
                        python_type | None,
                        Field(default=default_value, description=description),
                    )
            else:
                field = (python_type, Field(description=description))

            pydantic_fields[field_name] = field

        return pydantic_fields

    @classmethod
    def create_material_spec_model(
        cls,
        client_id: str | None = None,
        schema_definition: dict[str, Any] | None = None,
        db_session: Session | None = None,
        use_generic: bool = False,
    ) -> type[BaseModel]:
        """
        Create a dynamic MaterialSpec model for a client.

        Args:
            client_id: The client ID (used for caching and database lookup)
            schema_definition: Custom schema definition (JSON format) - overrides database
            db_session: Database session for loading client configurations
            use_generic: Force use of generic template instead of client-specific

        Returns:
            A Pydantic model class for the material specification
        """
        # Determine effective client_id for caching and lookup
        effective_client_id = GENERIC_CLIENT_ID if use_generic else client_id

        # Check cache first if client_id is provided
        if effective_client_id:
            cached_schema = SchemaRegistry.get_schema(effective_client_id)
            if cached_schema:
                return cached_schema

        # Determine schema source priority:
        # 1. Explicit schema_definition parameter (override)
        # 2. Database lookup (PRIMARY SOURCE - should always be used when available)
        # 3. Minimal fallback schema (only if database completely unavailable)
        if schema_definition:
            schema = schema_definition
        elif db_session:
            schema = cls.load_schema_from_database(db_session, effective_client_id or GENERIC_CLIENT_ID)
        else:
            # No database session provided - use minimal fallback
            schema = cls.MINIMAL_FALLBACK_SCHEMA

        # Convert schema to Pydantic fields
        pydantic_fields = cls._convert_schema_to_pydantic_fields(schema)

        # Create the dynamic model
        model_name = f"MaterialSpec_{effective_client_id.replace('-', '_')}" if effective_client_id else "MaterialSpec_Default"

        dynamic_model = create_model(
            model_name,
            **pydantic_fields,
            __doc__=f"""
            Dynamic material specification model for client: {effective_client_id or "default"}.
            This model is generated dynamically based on the client's output schema configuration.
            """,
        )

        # Cache the model if client_id is provided
        if effective_client_id:
            SchemaRegistry.register_schema(effective_client_id, dynamic_model)

        return dynamic_model

    @classmethod
    def get_or_create_model(
        cls,
        client_id: str | None = None,
        schema_definition: dict[str, Any] | None = None,
        db_session: Session | None = None,
        use_generic: bool = False,
    ) -> type[BaseModel]:
        """
        Get an existing model from cache or create a new one.

        This is just an alias for create_material_spec_model for backward compatibility.
        """
        return cls.create_material_spec_model(
            client_id=client_id,
            schema_definition=schema_definition,
            db_session=db_session,
            use_generic=use_generic,
        )

    @classmethod
    def get_schema_info(
        cls,
        client_id: str | None = None,
        db_session: Session | None = None,
        use_generic: bool = False,
    ) -> dict[str, Any]:
        """
        Get information about a client's schema.

        Args:
                client_id: The client ID
                db_session: Database session for loading client configurations
                use_generic: Force use of generic template

        Returns:
                Schema information including field names and types
        """
        model: type[BaseModel] = cls.get_or_create_model(
            client_id=client_id,
            db_session=db_session,
            use_generic=use_generic,
        )

        fields_info = {}

        # Pydantic v1
        if hasattr(model, "__fields__"):
            for field_name, field_info in model.model_fields.items():
                try:
                    field_type = str(
                        getattr(
                            field_info,
                            "type_",
                            getattr(field_info, "annotation", "unknown"),
                        )
                    )
                    required = getattr(field_info, "required", False)
                    description = getattr(getattr(field_info, "field_info", None), "description", "") or ""
                except AttributeError:
                    field_type = "unknown"
                    required = False
                    description = ""

                fields_info[field_name] = {
                    "type": field_type,
                    "required": required,
                    "description": description,
                }

        # Pydantic v2
        elif hasattr(model, "model_fields"):
            for field_name, field_info in model.model_fields.items():
                try:
                    field_type = str(getattr(field_info, "annotation", "unknown"))
                    required = field_info.is_required if hasattr(field_info, "is_required") else False
                    description = getattr(field_info, "description", "") or ""
                except AttributeError:
                    field_type = "unknown"
                    required = False
                    description = ""

                fields_info[field_name] = {
                    "type": field_type,
                    "required": required,
                    "description": description,
                }

        else:
            fields_info = {name: {"type": "unknown", "required": False, "description": ""} for name in getattr(model, "__annotations__", {})}

        return {
            "model_name": model.__name__,
            "fields": fields_info,
            "total_fields": len(fields_info),
        }

    @classmethod
    def get_comprehensive_generic_schema(cls) -> dict[str, Any]:
        """
        Get the comprehensive schema that should be stored in database for generic client.

        This method returns the full schema with all fields needed for the extractor,
        including confidence tracking and all material specification fields.

        Returns:
            Complete schema definition for generic client database storage
        """
        return {
            "form": {
                "type": "string",
                "required": False,
                "description": "The form of steel (e.g., Coils, Sheets, Plates, Bars)",
            },
            "choice": {
                "type": "string",
                "required": False,
                "description": "The choice of steel (e.g., Prime, 1st, 2nd, 3rd, 4th)",
            },
            "grade": {
                "type": "string",
                "required": False,
                "description": "Steel grade like DCXX, combination of alphabets and digits",
            },
            "coating": {
                "type": "string",
                "required": False,
                "description": "Coating type applied to material (e.g., Z275, ZE50/50)",
            },
            "finish": {
                "type": "string",
                "required": False,
                "description": "Surface finish of the material",
            },
            "surface_type": {
                "type": "string",
                "required": False,
                "description": "Surface type of steel (e.g., MA for Minimized spangle)",
            },
            "surface_protection": {
                "type": "string",
                "required": False,
                "description": "Surface protection type (e.g., C for Chemically passivated)",
            },
            "certificate": {
                "type": "array",
                "items": {"type": "string"},
                "required": False,
                "description": "List of required certificates",
            },
            "mandatory_tests": {
                "type": "array",
                "items": {"type": "string"},
                "required": False,
                "description": "List of required tests or analyses",
            },
            # Dimension fields
            "thickness_min": {
                "type": "number",
                "required": False,
                "description": "Minimum thickness value in mm",
            },
            "thickness_max": {
                "type": "number",
                "required": False,
                "description": "Maximum thickness value in mm",
            },
            "width_min": {
                "type": "number",
                "required": False,
                "description": "Minimum width value in mm",
            },
            "width_max": {
                "type": "number",
                "required": False,
                "description": "Maximum width value in mm",
            },
            "length_min": {
                "type": "number",
                "required": False,
                "description": "Minimum length value in mm",
            },
            "length_max": {
                "type": "number",
                "required": False,
                "description": "Maximum length value in mm",
            },
            "height_min": {
                "type": "number",
                "required": False,
                "description": "Minimum height value in mm",
            },
            "height_max": {
                "type": "number",
                "required": False,
                "description": "Maximum height value in mm",
            },
            "inner_diameter_min": {
                "type": "number",
                "required": False,
                "description": "Minimum inner diameter value in mm",
            },
            "inner_diameter_max": {
                "type": "number",
                "required": False,
                "description": "Maximum inner diameter value in mm",
            },
            "outer_diameter_min": {
                "type": "number",
                "required": False,
                "description": "Minimum outer diameter value in mm",
            },
            "outer_diameter_max": {
                "type": "number",
                "required": False,
                "description": "Maximum outer diameter value in mm",
            },
            "weight_min": {
                "type": "number",
                "required": False,
                "description": "Minimum weight value in kg",
            },
            "weight_max": {
                "type": "number",
                "required": False,
                "description": "Maximum weight value in kg",
            },
            "coil_max_weight": {
                "type": "number",
                "required": False,
                "description": "Maximum weight per coil in kg",
            },
            "yield_strength_min": {
                "type": "number",
                "required": False,
                "description": "Minimum yield strength value in N/mm²",
            },
            "yield_strength_max": {
                "type": "number",
                "required": False,
                "description": "Maximum yield strength value in N/mm²",
            },
            "tensile_strength_min": {
                "type": "number",
                "required": False,
                "description": "Minimum tensile strength value in N/mm²",
            },
            "tensile_strength_max": {
                "type": "number",
                "required": False,
                "description": "Maximum tensile strength value in N/mm²",
            },
            # Enhanced fields for extraction confidence and metadata
            "extraction_confidence": {
                "type": "object",
                "required": False,
                "description": "Confidence scores and source attribution for each field",
            },
            "extraction_flags": {
                "type": "object",
                "required": False,
                "description": "Extraction ambiguities, assumptions, and conflicts resolved",
            },
        }


# NOTE: Default schema initialization now happens from database
# The generic template should be loaded from database using GENERIC_CLIENT_ID
# Uncomment below only if you need a fallback model during module import
# default_model = DynamicMaterialSpecFactory.create_material_spec_model(use_generic=True)
# SchemaRegistry.set_default_schema(default_model)
