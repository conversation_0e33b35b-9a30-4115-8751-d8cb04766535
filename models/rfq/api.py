import json
from typing import Any
from uuid import UUID, uuid4

from pydantic import UUID4, BaseModel, Field, validator
from sqlalchemy.orm import Session

from models.rfq.dynamic_schema import GENERIC_CLIENT_ID, DynamicMaterialSpecFactory
from utils.json_encoder import UUIDEncoder


class UUIDMixin:
    """Mixin to add UUID serialization support to Pydantic models."""

    def model_dump(
        self,
        *,
        mode: str = "python",
        include: Any = None,
        exclude: Any = None,
        by_alias: bool = False,
        exclude_unset: bool = False,
        exclude_defaults: bool = False,
        exclude_none: bool = False,
    ) -> dict[str, Any]:
        """Override model_dump method to handle UUID serialization."""
        # Use super() to call the next method in MRO (Method Resolution Order)
        data = super().model_dump(  # type: ignore
            mode=mode,
            include=include,
            exclude=exclude,
            by_alias=by_alias,
            exclude_unset=exclude_unset,
            exclude_defaults=exclude_defaults,
            exclude_none=exclude_none,
        )
        return self._serialize_uuids(data)

    def model_dump_json(
        self,
        *,
        indent: int | None = None,
        include: Any = None,
        exclude: Any = None,
        by_alias: bool = False,
        exclude_unset: bool = False,
        exclude_defaults: bool = False,
        exclude_none: bool = False,
        round_trip: bool = False,
        warnings: bool = True,
    ) -> str:
        """Override model_dump_json method to use custom UUID encoder."""
        return json.dumps(
            self.model_dump(
                include=include,
                exclude=exclude,
                by_alias=by_alias,
                exclude_unset=exclude_unset,
                exclude_defaults=exclude_defaults,
                exclude_none=exclude_none,
            ),
            cls=UUIDEncoder,
            indent=indent,
        )

    @staticmethod
    def _serialize_uuids(obj):
        """Recursively convert UUIDs to strings."""
        if isinstance(obj, UUID):
            return str(obj)
        elif isinstance(obj, dict):
            return {key: UUIDMixin._serialize_uuids(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [UUIDMixin._serialize_uuids(item) for item in obj]
        return obj


class BaseUUIDModel(BaseModel, UUIDMixin):
    """Base model with UUID serialization support."""

    pass


class DynamicMaterialSpecWithValidation(BaseUUIDModel):
    """
    Dynamic material specification with validation information.
    This model wraps dynamically created material specs with additional metadata.
    """

    data: dict[str, Any] = Field(..., description="The material specification data conforming to client's schema")
    validation_info: dict[str, Any] | None = Field(
        default_factory=lambda: {},
        description="Validation metadata and processing info",
    )
    schema_version: str | None = Field(default=None, description="Version of the schema used for validation")
    client_id: UUID4 | None = Field(default=None, description="Client ID for which this spec was generated")

    @validator("data")
    def validate_data_structure(cls, v):
        """Ensure data is a dictionary."""
        if not isinstance(v, dict):
            raise ValueError("Data must be a dictionary")
        return v


class RFQProcessRequest(BaseUUIDModel):
    """
    Request model for the RFQ processing API.
    """

    email_body: str = Field(..., description="The body text of the RFQ email to process")
    email_id: str = Field(..., description="A unique identifier for the email being processed")
    request_id: str = Field(
        default_factory=lambda: str(uuid4()),
        description="A unique identifier for the request",
    )
    client_id: UUID4 | None = Field(default=None, description="Client ID for client-specific processing")
    force_schema_refresh: bool | None = Field(default=False, description="Force refresh of client schema cache")


class RFQProcessResponse(BaseUUIDModel):
    """
    Dynamic response model for the RFQ processing API.
    Uses client-specific schemas for material specifications.
    """

    request_id: str = Field(..., description="The request ID from the original request")
    email_id: str = Field(..., description="The email ID from the original request")
    client_id: UUID4 | None = Field(default=None, description="Client ID if provided")
    # material_specs: List[DynamicMaterialSpecWithValidation] = Field(
    #     default_factory=list,
    #     description="The extracted material specifications using client's dynamic schema",
    # )

    material_specs: list[dict[str, Any]] | None = Field(
        default_factory=lambda: [],
        description="The extracted material specifications using client's dynamic schema",
    )

    processing_log: list[dict[str, Any]] = Field(
        default_factory=lambda: [],
        description="Log of processing steps and any issues encountered",
    )
    processing_time: float | None = Field(default=None, description="Processing time in seconds")
    schema_info: dict[str, Any] | None = Field(default=None, description="Information about the schema used for this response")

    @classmethod
    def create_from_processed_data_for_extractor(
        cls,
        request_id: str,
        email_id: str,
        client_id: UUID4 | None,
        raw_material_specs: list[dict[str, Any]],
        processing_log: list[dict[str, Any]] | None = None,
        processing_time: float | None = None,
        db_session: Session = None,
        use_generic_for_extraction: bool = True,
    ) -> "RFQProcessResponse":
        response_schema_info: dict[str, Any] = {}
        # Fix: Use the list directly instead of wrapping it in a dictionary
        validated_specs = raw_material_specs

        return cls(
            request_id=request_id,
            email_id=email_id,
            client_id=client_id,
            material_specs=validated_specs,
            processing_log=processing_log or [],
            processing_time=processing_time,
            schema_info=response_schema_info,
        )

    @classmethod
    def create_from_processed_data(
        cls,
        request_id: str,
        email_id: str,
        client_id: UUID4 | None,
        raw_material_specs: list[dict[str, Any]],
        extraction_metadata: list[dict[str, Any]] | None = None,
        processing_log: list[dict[str, Any]] | None = None,
        processing_time: float | None = None,
        db_session: Session = None,
        use_generic_for_extraction: bool = True,
    ) -> "RFQProcessResponse":
        """
        Create a response with properly validated material specs.

        Args:
            request_id: Request identifier
            email_id: Email identifier
            client_id: Client identifier
            raw_material_specs: Raw extracted material specifications
            extraction_metadata: Additional metadata from extraction
            processing_log: Processing log entries
            processing_time: Processing time in seconds
            db_session: Database session for schema loading
            use_generic_for_extraction: Whether to use generic schema for extraction phase

        Returns:
            Validated response with dynamic schemas
        """
        # from utils.rfq.client_schema import ClientSchemaManager  # TODO: Missing file

        # For extraction phase, use generic template to support confidence scores
        # For validation/client-specific phase, use client schema
        schema_client_id = GENERIC_CLIENT_ID if use_generic_for_extraction else client_id

        # Get schema info using appropriate client_id
        schema_info = DynamicMaterialSpecFactory.get_schema_info(
            client_id=str(schema_client_id) if schema_client_id else None,
            db_session=db_session,
            use_generic=use_generic_for_extraction,
        )

        # Get the dynamic model using appropriate client_id
        # material_spec_model = ClientSchemaManager.get_client_material_spec_model(
        #     db_session=db_session,
        #     client_id=str(schema_client_id) if schema_client_id else None,
        # )
        material_spec_model = type("FallbackModel", (), {"__name__": "FallbackModel"})  # Temporary fallback

        # Validate and wrap each material spec
        validated_specs = []
        for i, raw_spec in enumerate(raw_material_specs or []):
            try:
                # For extraction phase, validation is more lenient (generic schema supports confidence)
                # For client phase, validation would be stricter (client-specific requirements)
                # is_valid, validated_data, errors = ClientSchemaManager.validate_client_data(
                #     db_session=db_session,
                #     client_id=str(schema_client_id) if schema_client_id else None,
                #     data=raw_spec,
                # )
                # Temporary fallback for validation
                is_valid, validated_data = True, raw_spec
                errors: list[str] = []

                validation_info = {
                    "index": i,
                    "is_valid": is_valid,
                    "errors": errors,
                    "validated_at": "2025-06-11T00:00:00Z",
                    "schema_used": material_spec_model.__name__,
                    "extraction_phase": use_generic_for_extraction,
                }

                if is_valid:
                    spec = DynamicMaterialSpecWithValidation(
                        data=validated_data,
                        validation_info=validation_info,
                        client_id=client_id,  # Always use actual client_id for tracking
                        schema_version="1.0",
                    )
                else:
                    # Include invalid data with error information
                    spec = DynamicMaterialSpecWithValidation(
                        data=raw_spec,
                        validation_info=validation_info,
                        client_id=client_id,
                        schema_version="1.0",
                    )

                validated_specs.append(spec)

            except Exception as e:
                # Handle validation errors gracefully
                error_spec = DynamicMaterialSpecWithValidation(
                    data=raw_spec,
                    validation_info={
                        "index": i,
                        "is_valid": False,
                        "errors": [f"Validation exception: {str(e)}"],
                        "validated_at": "2025-06-11T00:00:00Z",
                        "schema_used": material_spec_model.__name__,
                        "extraction_phase": use_generic_for_extraction,
                    },
                    client_id=client_id,
                    schema_version="1.0",
                )
                validated_specs.append(error_spec)

        # Include extraction metadata if available
        response_schema_info = schema_info.copy() if schema_info else {}
        if extraction_metadata:
            response_schema_info["extraction_metadata"] = extraction_metadata

        return cls(
            request_id=request_id,
            email_id=email_id,
            client_id=client_id,
            material_specs=validated_specs,
            processing_log=processing_log or [],
            processing_time=processing_time,
            schema_info=response_schema_info,
        )


# Schema Management Models
class SchemaManagementRequest(BaseUUIDModel):
    """Request model for schema management operations."""

    client_id: UUID4 = Field(..., description="Client ID")
    schema_definition: dict[str, Any] = Field(..., description="Schema definition in JSON format")
    validate_only: bool = Field(default=False, description="Only validate schema without saving")


class SchemaManagementResponse(BaseUUIDModel):
    """Response model for schema management operations."""

    success: bool = Field(..., description="Whether the operation was successful")
    message: str = Field(..., description="Operation result message")
    errors: list[str] = Field(default_factory=lambda: [], description="Any errors encountered")
    schema_info: dict[str, Any] | None = Field(default=None, description="Information about the schema")


class ClientSchemaInfoResponse(BaseUUIDModel):
    """Response model for client schema information."""

    client_id: UUID4 = Field(..., description="Client ID")
    client_name: str = Field(..., description="Client name")
    has_custom_schema: bool = Field(..., description="Whether client has custom schema")
    schema_info: dict[str, Any] = Field(..., description="Schema information")
    last_updated: str | None = Field(default=None, description="Last schema update time")


class ClientListResponse(BaseUUIDModel):
    """Response model for listing clients with schema information."""

    clients: list[ClientSchemaInfoResponse] = Field(default_factory=lambda: [], description="List of clients with schema info")
    total_count: int = Field(..., description="Total number of clients")


# Client management models
class ClientCreateRequest(BaseUUIDModel):
    """Request model for creating a new client."""

    name: str = Field(..., description="Client name")
    output_schema: dict[str, Any] | None = Field(default_factory=lambda: {}, description="Output schema configuration")
    extraction_rules: dict[str, Any] | None = Field(default_factory=lambda: {}, description="Extraction rules configuration")
    validation_rules: dict[str, Any] | None = Field(default_factory=lambda: {}, description="Validation rules configuration")
    normalization_rules: dict[str, Any] | None = Field(default_factory=lambda: {}, description="Normalization rules configuration")
    llm_preferences: dict[str, Any] | None = Field(default_factory=lambda: {}, description="Model preferences configuration")


class ClientResponse(BaseUUIDModel):
    """Response model for client information."""

    client_id: UUID4 = Field(..., description="Client ID")
    name: str = Field(..., description="Client name")
    client_code: str | None = Field(default=None, description="API key (only returned on creation or regeneration)")
    status: str = Field(..., description="Client status")
    created_at: str | None = Field(default=None, description="Creation timestamp")
    updated_at: str | None = Field(default=None, description="Last update timestamp")


class CatalogEntryRequest(BaseUUIDModel):
    """Request model for catalog entry."""

    code: str = Field(..., description="Catalog entry code")
    name: str = Field(..., description="Catalog entry name")
    description: str | None = Field(default=None, description="Catalog entry description")
    properties: dict[str, Any] | None = Field(default_factory=lambda: {}, description="Additional properties")
