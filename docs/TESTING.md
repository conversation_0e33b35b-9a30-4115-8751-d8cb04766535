# Testing Guide

This guide explains how to run tests for the vanilla-steel-ai project.

## Quick Start

```bash
# Run all tests
python scripts/run_tests.py

# Run specific test files
python scripts/run_tests.py tests/unit/test_database_simple.py

# Run with coverage
python scripts/run_tests.py --coverage

# Run quietly
python scripts/run_tests.py --quiet
```

## Test Structure

```
tests/
├── conftest.py              # Test configuration and fixtures
├── unit/                    # Unit tests
│   ├── test_database_simple.py
│   ├── test_main.py
│   └── test_settings.py
└── integration/             # Integration tests
    └── test_api_routes.py
```

## Test Environment

Tests automatically use:
- SQLite in-memory database (no real database needed)
- Mocked external API calls
- Test environment variables

## Database Testing

- All database connections are automatically mocked
- Tests use SQLite in-memory for actual database operations
- No real database setup required

## Environment Variables

These are automatically set for tests:
- `TESTING=True`
- `RUNTIME_ENV=test`
- `DB_DATABASE=sqlite:///:memory:`

## Fixtures Available

- `app` - FastAPI application instance
- `client` - Test client for API endpoints
- `mock_db_session` - Mocked database session
- `sample_rfq_text` - Sample RFQ text for testing
- `mock_database_tools` - Mocked database tools

## Common Test Patterns

### Testing API Endpoints
```python
def test_health_endpoint(client):
    response = client.get("/v1/health")
    assert response.status_code == 200
    assert response.json()["status"] == "success"
```

### Testing Database Operations
```python
@patch('psycopg2.connect')
def test_database_operation(mock_connect):
    # Your test code here
    pass
```

### Using Fixtures
```python
def test_with_fixtures(sample_rfq_text, mock_database_tools):
    assert "S235JR" in sample_rfq_text
    config = mock_database_tools.get_default_configuration()
    assert config is not None
```

## Running Tests in CI/CD

```bash
# In your CI/CD pipeline
export TESTING=True
export RUNTIME_ENV=test
python scripts/run_tests.py --coverage
```

## Troubleshooting

### Import Errors
Make sure you're running from the project root:
```bash
cd /Users/<USER>/Projects/vanilla-steel-ai
python scripts/run_tests.py
```

### Database Connection Errors
Tests should never connect to real databases. If you see connection errors:
1. Check that `TESTING=True` is set
2. Verify the test is using the mock fixtures
3. Ensure `@patch('psycopg2.connect')` decorator is used

### Missing Dependencies
Install test dependencies:
```bash
pip install -r test-requirements.txt
```
