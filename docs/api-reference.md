# API Reference

Complete reference for all Vanilla Steel AI API endpoints.

## Base URL

- **Development**: `http://localhost:8000`
- **Production**: `https://your-domain.com`

## Authentication

All API endpoints require authentication using API keys.

```http
Authorization: Bearer your-api-key
```

## Core Endpoints

### Health Check

Check system health and status.

#### `GET /v1/health`

**Response:**
```json
{
  "status": "success",
  "router": "status",
  "path": "/health",
  "utc": "2024-12-21T10:30:00Z",
  "environment": "production",
  "version": "1.0.0"
}
```

**Status Codes:**
- `200` - System healthy
- `503` - System unavailable

### Configuration

Get system configuration information.

#### `GET /v1/config`

**Response:**
```json
{
  "environment": "production",
  "version": "1.0.0",
  "features": {
    "extraction_retry": true,
    "validation": true,
    "normalization": true
  }
}
```

## RFQ Processing

### Process RFQ

Process an RFQ email and extract material specifications.

#### `POST /v1/rfq/{client_id}/process`

**Parameters:**
- `client_id` (path): Client identifier

**Headers:**
- `api-key`: Client API key
- `Content-Type`: application/json

**Request Body:**
```json
{
  "email_id": "unique-email-identifier",
  "email_body": "RFQ email content...",
  "email_subject": "RFQ for Steel Coils",
  "sender_email": "<EMAIL>",
  "performance_mode": false,
  "debug_mode": false
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "session_id": "sess_123456789",
    "material_specs": [
      {
        "spec_id": "spec_001",
        "specification": {
          "grade": "DX51D",
          "coating": "ZE25/25",
          "thickness_min": 0.4,
          "thickness_max": 0.4,
          "weight_min": 20000,
          "weight_max": 24000,
          "surface": "AC",
          "form": "Coils"
        },
        "validation": {
          "is_valid": true,
          "confidence": 0.95,
          "warnings": []
        },
        "normalization": {
          "units_standardized": true,
          "values_normalized": true
        }
      }
    ],
    "extraction_metadata": {
      "confidence": 0.95,
      "processing_time": 2.3,
      "retry_count": 0,
      "source": "email_body"
    }
  }
}
```

**Status Codes:**
- `200` - Success
- `400` - Invalid request
- `401` - Authentication failed
- `422` - Validation error
- `500` - Internal server error

### Get Processing Status

Check the status of an RFQ processing request.

#### `GET /v1/rfq/{client_id}/status/{session_id}`

**Parameters:**
- `client_id` (path): Client identifier
- `session_id` (path): Processing session ID

**Response:**
```json
{
  "session_id": "sess_123456789",
  "status": "completed",
  "progress": 100,
  "created_at": "2024-12-21T10:30:00Z",
  "completed_at": "2024-12-21T10:30:02Z",
  "processing_time": 2.3
}
```

**Status Values:**
- `pending` - Request received, processing not started
- `processing` - Currently processing
- `completed` - Processing completed successfully
- `failed` - Processing failed
- `timeout` - Processing timed out

## Schema Management

### Get Schema Information

Retrieve client-specific schema configuration.

#### `GET /v1/rfq/{client_id}/schema`

**Response:**
```json
{
  "client_id": "client_123",
  "schema_version": "1.0",
  "output_schema": {
    "fields": {
      "grade": {
        "type": "string",
        "required": true,
        "validation": {
          "pattern": "[A-Z][0-9]{2}[A-Z]?[0-9]?"
        }
      },
      "coating": {
        "type": "string",
        "required": false
      },
      "thickness_min": {
        "type": "number",
        "required": false,
        "unit": "mm"
      }
    }
  },
  "extraction_rules": {
    "patterns": {
      "grade": "[A-Z][0-9]{2}[A-Z]?[0-9]?",
      "coating": "Z[0-9]{2,3}"
    }
  }
}
```

### Update Schema

Update client schema configuration.

#### `PUT /v1/rfq/{client_id}/schema`

**Request Body:**
```json
{
  "output_schema": {
    "fields": {
      "grade": {
        "type": "string",
        "required": true
      }
    }
  },
  "extraction_rules": {
    "patterns": {
      "grade": "[A-Z][0-9]{2}[A-Z]?[0-9]?"
    }
  }
}
```

**Response:**
```json
{
  "status": "success",
  "message": "Schema updated successfully",
  "schema_version": "1.1"
}
```

### Get Schema Template

Get a template for schema configuration.

#### `GET /v1/rfq/schema/template`

**Response:**
```json
{
  "template": {
    "output_schema": {
      "fields": {
        "grade": {
          "type": "string",
          "required": true,
          "description": "Steel grade specification"
        },
        "coating": {
          "type": "string",
          "required": false,
          "description": "Coating specification"
        }
      }
    },
    "extraction_rules": {
      "patterns": {
        "grade": "[A-Z][0-9]{2}[A-Z]?[0-9]?",
        "coating": "Z[0-9]{2,3}"
      }
    }
  }
}
```

## Error Responses

All endpoints return consistent error responses:

```json
{
  "status": "error",
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid request format",
    "details": {
      "field": "email_body",
      "issue": "Field is required"
    }
  },
  "request_id": "req_123456789"
}
```

### Error Codes

- `AUTHENTICATION_FAILED` - Invalid or missing API key
- `AUTHORIZATION_FAILED` - Insufficient permissions
- `VALIDATION_ERROR` - Request validation failed
- `PROCESSING_ERROR` - RFQ processing failed
- `TIMEOUT_ERROR` - Request timed out
- `RATE_LIMIT_EXCEEDED` - Too many requests
- `INTERNAL_ERROR` - Internal server error

## Rate Limits

- **Default**: 100 requests per minute per API key
- **Burst**: Up to 200 requests in a 10-second window
- **Headers**: Rate limit information in response headers

```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995200
```

## SDKs and Examples

### Python SDK

```python
import requests

# Process RFQ
response = requests.post(
    "http://localhost:8000/v1/rfq/client_123/process",
    headers={
        "api-key": "your-api-key",
        "Content-Type": "application/json"
    },
    json={
        "email_id": "email_001",
        "email_body": "RFQ content...",
        "email_subject": "Steel RFQ"
    }
)

data = response.json()
print(f"Extracted {len(data['data']['material_specs'])} specifications")
```

### cURL Examples

```bash
# Health check
curl -X GET "http://localhost:8000/v1/health"

# Process RFQ
curl -X POST "http://localhost:8000/v1/rfq/client_123/process" \
  -H "api-key: your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "email_id": "email_001",
    "email_body": "RFQ for DX51D steel coils...",
    "email_subject": "Steel RFQ"
  }'
```
