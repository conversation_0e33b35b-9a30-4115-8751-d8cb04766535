# Running Tests with Database Mocking

This guide explains how to run the enhanced tests for the vanilla-steel-ai project with proper database mocking.

## Overview

The test suite has been enhanced with comprehensive database mocking to ensure:
- No real database connections during testing
- Fast, isolated test execution
- Reliable test results
- Proper error handling simulation

## Quick Start

### Using the Test Runner Script

The easiest way to run tests is using the provided test runner script:

```bash
# Run all tests with coverage
python run_tests.py

# Run only unit tests
python run_tests.py --unit

# Run only integration tests
python run_tests.py --integration

# Run only database mocking tests
python run_tests.py --mock

# Run specific test file
python run_tests.py tests/unit/test_database_tools.py

# Run without coverage (faster)
python run_tests.py --no-coverage

# Run quietly
python run_tests.py --quiet
```

### Using pytest directly

```bash
# Set environment variables first
export TESTING=True
export RUNTIME_ENV=test
export DB_DATABASE="sqlite:///:memory:"
export DB_DRIVER=sqlite

# Run tests
pytest tests/ -v
pytest tests/unit/ -v
pytest tests/integration/ -v
```

### Using the enhanced pytest configuration

```bash
# Use the enhanced configuration
pytest -c pytest_enhanced.ini tests/
```

## Test Categories

### Unit Tests
- `tests/unit/test_database_mocking.py` - Database mocking functionality
- `tests/unit/test_database_tools.py` - Database tools with mocked connections
- `tests/unit/test_main.py` - FastAPI application tests
- `tests/unit/test_settings.py` - Configuration tests
- `tests/unit/test_rfq_extraction.py` - RFQ extraction logic tests
- `tests/unit/test_multi_grade_bug.py` - Multi-grade extraction bug tests

### Integration Tests
- `tests/integration/test_api_routes.py` - API endpoint integration tests

### Test Utilities
- `tests/database_utils.py` - Reusable database mocking utilities
- `tests/conftest.py` - Enhanced pytest configuration and fixtures

## Database Mocking Features

### Automatic Mocking
All database connections are automatically mocked at the session level:
- PostgreSQL connections via `psycopg2`
- SQLAlchemy engine connections
- Database URL resolution

### Test Database Schema
Complete database schema with:
- `rfq_clients` table
- `rfq_client_configurations` table
- `rfq_extractions` table
- Sample test data

### Mock Utilities
- `MockDatabaseUtils` - Database mocking utilities
- `TestDataFactory` - Test data generation
- Comprehensive error simulation
- Successful query mocking

## Environment Variables

The following environment variables are automatically set for testing:

```bash
TESTING=True
RUNTIME_ENV=test
DB_DATABASE=sqlite:///:memory:
DB_DRIVER=sqlite
DOCS_ENABLED=true
OPENROUTER_API_KEY=test-openrouter-key
AGNO_API_KEY=test-agno-key
UNKEY_API_KEY=test-unkey-key
UNKEY_API_ID=test-unkey-id
AGNO_WORKSPACE_ID=test-workspace-id
EXA_API_KEY=test-exa-key
DB_HOST=localhost
DB_PORT=5432
DB_USER=test_user
DB_PASS=test_pass
```

## Running Specific Test Types

### Database Mocking Tests
```bash
python run_tests.py tests/unit/test_database_mocking.py -v
```

### Database Tools Tests
```bash
python run_tests.py tests/unit/test_database_tools.py -v
```

### Multi-Grade Bug Tests
```bash
python run_tests.py tests/unit/test_multi_grade_bug.py -v
```

### API Integration Tests
```bash
python run_tests.py tests/integration/test_api_routes.py -v
```

## Test Output

Tests will show:
- Test execution status
- Coverage reporting (if enabled)
- Database connection mocking status
- Any failures with detailed tracebacks

Example successful output:
```
tests/unit/test_database_mocking.py::test_database_mocking_works PASSED
tests/unit/test_database_mocking.py::test_mock_database_utils PASSED
tests/unit/test_database_tools.py::TestRFQDatabaseTools::test_init_with_sqlalchemy_url PASSED
tests/unit/test_database_tools.py::TestRFQDatabaseTools::test_get_client_configuration_success PASSED
```

## Troubleshooting

### If tests fail with database connection errors:
1. Ensure environment variables are set correctly
2. Check that `TESTING=True` is set
3. Verify the conftest.py fixtures are loading properly

### If imports fail:
1. Make sure you're running from the project root
2. Check that PYTHONPATH includes the project directory
3. Verify all required dependencies are installed

### Performance issues:
1. Use `--no-coverage` flag for faster execution
2. Run specific test files instead of the entire suite
3. Use `-x` flag to stop on first failure

## Coverage Reports

HTML coverage reports are generated in `htmlcov/` directory when running with coverage enabled.

View the report:
```bash
open htmlcov/index.html  # macOS
xdg-open htmlcov/index.html  # Linux
```

## Best Practices

1. Always run tests before committing code
2. Use the test runner script for consistent environment setup
3. Add new tests to appropriate categories (unit/integration)
4. Use the database utilities for consistent mocking
5. Keep tests isolated and independent

## Dependencies

Required packages for testing:
- pytest
- pytest-asyncio
- pytest-cov
- fastapi[testing]
- sqlalchemy
- psycopg2 (mocked)
- httpx

Install with:
```bash
pip install -r test-requirements.txt
```
