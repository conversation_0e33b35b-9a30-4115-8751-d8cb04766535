# System Architecture

Comprehensive overview of Vanilla Steel AI's system architecture, components, and design patterns.

## 🏗️ High-Level Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                        Client Layer                             │
├─────────────────────────────────────────────────────────────────┤
│  Web UI (Streamlit)  │  REST API Clients  │  Third-party Apps  │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                      API Gateway Layer                         │
├─────────────────────────────────────────────────────────────────┤
│           FastAPI Server (api/main.py)                         │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐              │
│  │ Auth        │ │ Rate Limit  │ │ Monitoring  │              │
│  │ Middleware  │ │ Middleware  │ │ Middleware  │              │
│  └─────────────┘ └─────────────┘ └─────────────┘              │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Business Logic Layer                        │
├─────────────────────────────────────────────────────────────────┤
│                 RFQ Processing Workflow                        │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐              │
│  │ Extractor   │ │ Validator   │ │ Normalizer  │              │
│  │ Agent       │ │ Agent       │ │ Agent       │              │
│  └─────────────┘ └─────────────┘ └─────────────┘              │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                     Data Access Layer                          │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐              │
│  │ Database    │ │ External    │ │ File        │              │
│  │ Tools       │ │ APIs        │ │ Storage     │              │
│  └─────────────┘ └─────────────┘ └─────────────┘              │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                      Storage Layer                             │
├─────────────────────────────────────────────────────────────────┤
│  PostgreSQL Database  │  Redis Cache  │  File System/S3       │
└─────────────────────────────────────────────────────────────────┘
```

## 🔧 Core Components

### 1. API Gateway Layer

**Location**: `api/`

The FastAPI-based API gateway handles all incoming requests and provides:

- **Authentication & Authorization** (`api/middleware/auth.py`)
- **Rate Limiting** (`api/middleware/rate_limit.py`)
- **Request/Response Logging** (`utils/monitoring.py`)
- **Error Handling** (`api/middleware/error_handlers.py`)
- **CORS Configuration** (`api/main.py`)

**Key Files**:
- `api/main.py` - FastAPI application setup
- `api/routes/rfq.py` - RFQ processing endpoints
- `api/routes/status.py` - Health and status endpoints
- `api/settings.py` - Configuration management

### 2. Business Logic Layer

**Location**: `workflows/`, `agents/`

The core business logic implements the RFQ processing pipeline:

#### RFQ Processing Workflow (`workflows/wf_rfq_processor.py`)

```python
class RFQProcessingWorkflow:
    async def arun(self, rfq_text: str) -> AsyncIterator[dict]:
        # 1. Extract material specifications
        extracted_data = await self._run_extraction_with_retry(rfq_text)
        
        # 2. Validate extracted data
        validated_data = await self._run_validation_with_fallback(extracted_data)
        
        # 3. Normalize and standardize
        normalized_data = await self._run_normalization_with_fallback(validated_data)
        
        # 4. Format final output
        final_output = self._format_final_output(normalized_data)
        
        yield final_output
```

#### AI Agents (`agents/`)

**Extractor Agent** (`agents/rfq_extractor.py`):
- Parses unstructured RFQ emails
- Extracts material specifications
- Implements retry mechanism for reliability

**Validator Agent** (`agents/rfq_validator.py`):
- Validates extracted data against business rules
- Checks data consistency and completeness
- Provides confidence scores

**Normalizer Agent** (`agents/rfq_normalizer.py`):
- Standardizes units and formats
- Normalizes numerical values
- Ensures data consistency

### 3. Data Access Layer

**Location**: `tools/`, `db/`

Handles all data operations and external integrations:

#### Database Tools (`tools/rfq/database_tools.py`)

```python
class RFQDatabaseTools:
    def get_client_configuration(self, client_id: str) -> dict
    def save_processing_result(self, session_id: str, result: dict) -> bool
    def execute_with_retry(self, query: str, params: dict) -> Any
```

#### External API Integration
- **OpenRouter API** - Language model access
- **Anthropic Claude** - Advanced text processing
- **OpenAI GPT** - Fallback language model

### 4. Storage Layer

**Location**: `db/`

#### PostgreSQL Database Schema

**Tables**:
- `rfq_clients` - Client configuration and metadata
- `rfq_client_configurations` - Processing rules and schemas
- `rfq_processing_sessions` - Processing session tracking
- `rfq_extraction_results` - Extracted material specifications
- `rfq_validation_results` - Validation outcomes
- `rfq_normalization_results` - Normalized data

**Key Features**:
- ACID compliance for data integrity
- Indexing for performance optimization
- Connection pooling for scalability
- Backup and recovery procedures

## 🔄 Data Flow

### 1. Request Processing Flow

```
Client Request
    │
    ▼
API Gateway (FastAPI)
    │
    ├─ Authentication Check
    ├─ Rate Limit Check
    ├─ Request Validation
    │
    ▼
RFQ Processing Workflow
    │
    ├─ Extract (with retry)
    ├─ Validate (with fallback)
    ├─ Normalize (with fallback)
    │
    ▼
Database Storage
    │
    ▼
Response to Client
```

### 2. Extraction Retry Mechanism

```python
# Retry logic with progressive delays
MAX_EXTRACTION_RETRIES = 3
EXTRACTION_RETRY_DELAY = 2.0

for attempt in range(MAX_EXTRACTION_RETRIES):
    try:
        result = await self.extractor.arun(rfq_text)
        if self._has_valid_specs(result):
            return result
    except (TimeoutError, APIError) as e:
        if attempt < MAX_EXTRACTION_RETRIES - 1:
            delay = EXTRACTION_RETRY_DELAY * (attempt + 1)
            await asyncio.sleep(delay)
            continue
        raise
```

### 3. Error Handling Strategy

```python
# Graceful degradation with fallbacks
try:
    # Primary processing path
    result = await primary_agent.process(data)
except ProcessingError:
    # Fallback to alternative method
    result = await fallback_agent.process(data)
except CriticalError:
    # Log error and return partial results
    logger.error("Critical processing error", exc_info=True)
    return partial_results
```

## 🏗️ Design Patterns

### 1. Agent Pattern

Each AI agent follows a consistent interface:

```python
class BaseAgent:
    async def arun(self, input_data: str) -> AgentResponse:
        """Process input and return structured response"""
        pass
    
    def _get_agent_goal(self) -> str:
        """Define agent's primary objective"""
        pass
    
    def _get_agent_instructions(self) -> str:
        """Get processing instructions"""
        pass
```

### 2. Workflow Pattern

Processing workflows orchestrate multiple agents:

```python
class BaseWorkflow:
    async def arun(self, input_data: str) -> AsyncIterator[dict]:
        """Execute workflow steps and yield results"""
        for step in self.steps:
            result = await step.execute(input_data)
            yield result
```

### 3. Repository Pattern

Data access is abstracted through repository classes:

```python
class RFQRepository:
    def save(self, entity: RFQEntity) -> bool
    def find_by_id(self, entity_id: str) -> Optional[RFQEntity]
    def find_by_criteria(self, criteria: dict) -> List[RFQEntity]
```

## 🔒 Security Architecture

### 1. Authentication & Authorization

- **API Key Authentication** - Client-specific API keys
- **Request Signing** - Optional request signature validation
- **Client Isolation** - Data segregation by client ID

### 2. Data Protection

- **Encryption at Rest** - Database encryption
- **Encryption in Transit** - TLS/SSL for all communications
- **Data Masking** - Sensitive data protection in logs

### 3. Security Monitoring

- **Request Logging** - All API requests logged
- **Anomaly Detection** - Unusual usage pattern detection
- **Audit Trails** - Complete processing history

## 📈 Scalability Considerations

### 1. Horizontal Scaling

- **Stateless Design** - No server-side session state
- **Load Balancing** - Multiple API server instances
- **Database Sharding** - Client-based data partitioning

### 2. Performance Optimization

- **Connection Pooling** - Database connection reuse
- **Caching Strategy** - Redis for frequently accessed data
- **Async Processing** - Non-blocking I/O operations

### 3. Resource Management

- **Memory Management** - Efficient data structures
- **CPU Optimization** - Parallel processing where possible
- **I/O Optimization** - Batch operations and streaming

## 🔧 Configuration Management

### 1. Environment-Based Configuration

```python
# api/settings.py
class ApiSettings(BaseSettings):
    runtime_env: EnvironmentType = EnvironmentType.DEV
    openrouter_api_key: str
    db_host: str
    db_port: int
    
    class Config:
        env_file = ".env"
```

### 2. Client-Specific Configuration

```python
# Database-stored client configuration
{
    "client_id": "client_123",
    "output_schema": {...},
    "extraction_rules": {...},
    "validation_rules": {...},
    "normalization_rules": {...}
}
```

## 🔍 Monitoring & Observability

### 1. Logging Strategy

- **Structured Logging** - JSON format for machine parsing
- **Log Levels** - DEBUG, INFO, WARNING, ERROR, CRITICAL
- **Context Preservation** - Request ID tracking across components

### 2. Metrics Collection

- **Request Metrics** - Response times, error rates
- **Business Metrics** - Processing success rates, extraction accuracy
- **System Metrics** - CPU, memory, database performance

### 3. Health Checks

- **Liveness Probes** - Basic application health
- **Readiness Probes** - Service dependency checks
- **Deep Health Checks** - Database connectivity, external API status

This architecture ensures scalability, reliability, and maintainability while providing the flexibility needed for enterprise deployment.
