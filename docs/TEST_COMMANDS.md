# Quick Test Commands for Vanilla Steel AI

## Make Test Script Executable
```bash
chmod +x scripts/run_tests.sh
chmod +x scripts/test_imports.sh
```

## Test Order (Run in This Sequence)

### 1. Test Import Issues First
```bash
# Test just the imports (quick check)
python -m pytest tests/unit/test_imports.py -v

# If imports work, test database mocking
python -m pytest tests/unit/test_database_mocking.py -v
```

### 2. Run Core Tests
```bash
# Test database tools (should now work with mocking)
python -m pytest tests/unit/test_database_tools.py -v

# Test settings
python -m pytest tests/unit/test_settings.py -v
```

### 3. Run All Tests
```bash
# Run all unit tests
python -m pytest tests/unit/ -v

# Or use the test runner
./scripts/run_tests.sh --unit
```

## Using the Test Runner Script
```bash
# Run all tests
./scripts/run_tests.sh

# Run only unit tests
./scripts/run_tests.sh --unit

# Run with HTML coverage report
./scripts/run_tests.sh --html

# Run multi-grade specific tests
./scripts/run_tests.sh --multi-grade
```

## Using Pytest Directly
```bash
# Run all tests
python -m pytest

# Run with coverage
python -m pytest --cov=api --cov=db --cov=utils --cov-report=html

# Run specific test file
python -m pytest tests/unit/test_multi_grade_bug.py

# Run specific test
python -m pytest tests/unit/test_multi_grade_bug.py::TestMultiGradeExtractionBug::test_multi_grade_detection_logic -v
```

## Database Connection Troubleshooting

If you still get database connection errors:

```bash
# 1. Check if database mocking test passes
python -m pytest tests/unit/test_database_mocking.py -v

# 2. Test with explicit SQLite database
export DB_DATABASE="sqlite:///:memory:"
python -m pytest tests/unit/test_database_tools.py -v

# 3. Run tests with maximum verbosity
python -m pytest tests/unit/test_database_tools.py -vvs
```

## Troubleshooting Import Issues

```bash
# Check if project root is in Python path
python -c "import sys; print('\n'.join(sys.path))"

# Test basic import
python -c "from utils.log import get_app_logger ; print('Import successful')"

# Test from project root
cd /Users/<USER>/Projects/vanilla-steel-ai
python -c "from utils.log import get_app_logger; print('Import successful')"
```

## Test Categories

- `--unit` - Unit tests only
- `--integration` - Integration tests only
- `--multi-grade` - Multi-grade extraction tests
- `-m "not slow"` - Skip slow tests

## Multi-Grade Bug Specific Tests

```bash
# Test the S235JR/S235DC issue specifically
python -m pytest tests/unit/test_multi_grade_bug.py::TestMultiGradeExtractionBug::test_multi_grade_detection_logic -v

# Test real-world multi-grade scenarios
python -m pytest tests/unit/test_rfq_extraction.py::TestRFQTextExtraction::test_multi_grade_extraction_real_world_case -v
```
