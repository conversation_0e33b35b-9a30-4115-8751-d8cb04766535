# Database Design Specification

Complete database schema design, relationships, and operational procedures for Vanilla Steel AI.

## 🗄️ Database Architecture

### Technology Stack
- **Database**: PostgreSQL 15+
- **Extensions**: PgVector (for future vector operations)
- **Connection Pool**: SQLAlchemy with connection pooling
- **Migration Tool**: Alembic
- **Encoding**: UTF-8
- **Timezone**: UTC

### Connection Configuration

```python
# Database URL Format
DATABASE_URL = "postgresql://{DB_USER}:{DB_PASS}@{DB_HOST}:{DB_PORT}/{DB_DATABASE}"

# Connection Pool Settings
SQLALCHEMY_ENGINE_OPTIONS = {
    "pool_size": 5,           # Number of connections to maintain
    "max_overflow": 15,       # Additional connections beyond pool_size
    "pool_timeout": 30,       # Seconds to wait for connection
    "pool_recycle": 3600,     # Seconds before recreating connection
    "pool_pre_ping": True,    # Validate connections before use
    "echo": False,            # Log SQL statements (dev only)
}
```

## 📊 Complete Schema Design

### Entity Relationship Diagram

```
┌─────────────────────┐
│    rfq_clients      │
│                     │
│ PK client_id (UUID) │
│    name             │
│    client_code      │
│    status           │
│    has_kb           │
│    has_instruction  │
│    has_output_pref  │
│    created_at       │
│    updated_at       │
└─────────────────────┘
           │
           │ 1:N
           ▼
┌─────────────────────┐
│rfq_client_configs   │
│                     │
│ PK configuration_id │
│ FK client_id        │
│    output_schema    │
│    extraction_rules │
│    validation_rules │
│    normalization_r  │
│    formatter_cfg_id │
│    schema_version   │
│    is_active        │
│    created_at       │
│    updated_at       │
└─────────────────────┘
           │
           │ 1:N
           ▼
┌─────────────────────┐
│rfq_processing_sess  │
│                     │
│ PK session_id       │
│ FK client_id        │
│    email_id         │
│    email_subject    │
│    email_body       │
│    sender_email     │
│    status           │
│    performance_mode │
│    debug_mode       │
│    processing_start │
│    processing_end   │
│    processing_dur   │
│    retry_count      │
│    error_message    │
│    created_at       │
│    updated_at       │
└─────────────────────┘
           │
           │ 1:N
           ▼
┌─────────────────────┐    ┌─────────────────────┐    ┌─────────────────────┐
│rfq_extraction_res   │    │rfq_validation_res   │    │rfq_normalization_r  │
│                     │    │                     │    │                     │
│ PK extraction_id    │    │ PK validation_id    │    │ PK normalization_id │
│ FK session_id       │    │ FK session_id       │    │ FK session_id       │
│    material_specs   │    │ FK extraction_id    │    │ FK validation_id    │
│    extraction_meta  │    │    validation_res   │    │    normalized_specs │
│    confidence_score │    │    is_valid         │    │    normalization_m  │
│    processing_time  │    │    validation_errs  │    │    units_standard   │
│    model_used       │    │    validation_warn  │    │    values_normal    │
│    retry_attempt    │    │    confidence_score │    │    processing_time  │
│    created_at       │    │    processing_time  │    │    created_at       │
└─────────────────────┘    │    created_at       │    └─────────────────────┘
                           └─────────────────────┘
                                      │
                                      │ 1:1
                                      ▼
                           ┌─────────────────────┐
                           │rfq_final_outputs    │
                           │                     │
                           │ PK output_id        │
                           │ FK session_id       │
                           │    final_output     │
                           │    output_format    │
                           │    total_proc_time  │
                           │    total_specs_ext  │
                           │    success_rate     │
                           │    created_at       │
                           └─────────────────────┘
```

## 📋 Detailed Table Specifications

### 1. rfq_clients

**Purpose**: Store client information and feature flags

```sql
CREATE TABLE public.rfq_clients (
    client_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    client_code VARCHAR(100) UNIQUE NOT NULL,
    status VARCHAR(50) DEFAULT 'active'
        CHECK (status IN ('active', 'inactive', 'suspended', 'deleted')),
    has_kb BOOLEAN DEFAULT FALSE,
    has_instruction BOOLEAN DEFAULT FALSE,
    has_output_preference BOOLEAN DEFAULT FALSE,
    api_key_hash VARCHAR(255),
    rate_limit_per_minute INTEGER DEFAULT 100,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Constraints
    CONSTRAINT rfq_clients_name_not_empty CHECK (LENGTH(TRIM(name)) > 0),
    CONSTRAINT rfq_clients_client_code_format CHECK (client_code ~ '^[A-Za-z0-9_-]+$')
);

-- Indexes
CREATE INDEX idx_rfq_clients_status ON public.rfq_clients(status) WHERE status != 'deleted';
CREATE UNIQUE INDEX idx_rfq_clients_client_code ON public.rfq_clients(client_code);
CREATE INDEX idx_rfq_clients_created_at ON public.rfq_clients(created_at);

-- Comments
COMMENT ON TABLE public.rfq_clients IS 'Client information and feature flags';
COMMENT ON COLUMN public.rfq_clients.has_kb IS 'Client has knowledge base integration';
COMMENT ON COLUMN public.rfq_clients.has_instruction IS 'Client has custom instructions';
COMMENT ON COLUMN public.rfq_clients.has_output_preference IS 'Client has output formatting preferences';
```

### 2. rfq_client_configurations

**Purpose**: Store client-specific processing configurations

```sql
CREATE TABLE public.rfq_client_configurations (
    configuration_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    client_id UUID NOT NULL REFERENCES public.rfq_clients(client_id) ON DELETE CASCADE,
    output_schema JSONB NOT NULL DEFAULT '{}',
    extraction_rules JSONB NOT NULL DEFAULT '{}',
    validation_rules JSONB NOT NULL DEFAULT '{}',
    normalization_rules JSONB NOT NULL DEFAULT '{}',
    formatter_output_config_id UUID,
    schema_version VARCHAR(20) DEFAULT '1.0',
    is_active BOOLEAN DEFAULT TRUE,
    configuration_name VARCHAR(255),
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Constraints
    CONSTRAINT rfq_client_configs_schema_version_format
        CHECK (schema_version ~ '^\d+\.\d+(\.\d+)?$'),
    CONSTRAINT rfq_client_configs_one_active_per_client
        EXCLUDE (client_id WITH =) WHERE (is_active = TRUE)
);

-- Indexes
CREATE INDEX idx_rfq_client_configs_client_id ON public.rfq_client_configurations(client_id);
CREATE INDEX idx_rfq_client_configs_active ON public.rfq_client_configurations(is_active) WHERE is_active = TRUE;
CREATE INDEX idx_rfq_client_configs_schema_version ON public.rfq_client_configurations(schema_version);

-- JSONB Indexes for performance
CREATE INDEX idx_rfq_client_configs_output_schema
    ON public.rfq_client_configurations USING GIN (output_schema);
CREATE INDEX idx_rfq_client_configs_extraction_rules
    ON public.rfq_client_configurations USING GIN (extraction_rules);
CREATE INDEX idx_rfq_client_configs_validation_rules
    ON public.rfq_client_configurations USING GIN (validation_rules);

-- Comments
COMMENT ON TABLE public.rfq_client_configurations IS 'Client-specific processing configurations';
COMMENT ON COLUMN public.rfq_client_configurations.output_schema IS 'JSON schema for output format';
COMMENT ON COLUMN public.rfq_client_configurations.extraction_rules IS 'Rules for material extraction';
```

### 3. rfq_processing_sessions

**Purpose**: Track individual RFQ processing sessions

```sql
CREATE TABLE public.rfq_processing_sessions (
    session_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    client_id UUID NOT NULL REFERENCES public.rfq_clients(client_id),
    email_id VARCHAR(255) NOT NULL,
    email_subject TEXT,
    email_body TEXT NOT NULL,
    sender_email VARCHAR(255),
    status VARCHAR(50) DEFAULT 'pending'
        CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'timeout', 'cancelled')),
    performance_mode BOOLEAN DEFAULT FALSE,
    debug_mode BOOLEAN DEFAULT FALSE,
    processing_start_time TIMESTAMP WITH TIME ZONE,
    processing_end_time TIMESTAMP WITH TIME ZONE,
    processing_duration_ms INTEGER,
    retry_count INTEGER DEFAULT 0 CHECK (retry_count >= 0),
    error_message TEXT,
    error_code VARCHAR(50),
    request_ip INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Constraints
    CONSTRAINT rfq_sessions_email_id_not_empty CHECK (LENGTH(TRIM(email_id)) > 0),
    CONSTRAINT rfq_sessions_email_body_not_empty CHECK (LENGTH(TRIM(email_body)) > 0),
    CONSTRAINT rfq_sessions_processing_duration_positive
        CHECK (processing_duration_ms IS NULL OR processing_duration_ms >= 0),
    CONSTRAINT rfq_sessions_processing_times_logical
        CHECK (processing_end_time IS NULL OR processing_start_time IS NULL OR
               processing_end_time >= processing_start_time)
);

-- Indexes
CREATE INDEX idx_rfq_sessions_client_id ON public.rfq_processing_sessions(client_id);
CREATE INDEX idx_rfq_sessions_status ON public.rfq_processing_sessions(status);
CREATE INDEX idx_rfq_sessions_created_at ON public.rfq_processing_sessions(created_at);
CREATE INDEX idx_rfq_sessions_email_id ON public.rfq_processing_sessions(email_id);
CREATE INDEX idx_rfq_sessions_sender_email ON public.rfq_processing_sessions(sender_email);
CREATE INDEX idx_rfq_sessions_processing_duration
    ON public.rfq_processing_sessions(processing_duration_ms) WHERE processing_duration_ms IS NOT NULL;

-- Composite indexes for common queries
CREATE INDEX idx_rfq_sessions_client_status_created
    ON public.rfq_processing_sessions(client_id, status, created_at);
```

### 4. rfq_extraction_results

**Purpose**: Store material extraction results

```sql
CREATE TABLE public.rfq_extraction_results (
    extraction_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID NOT NULL REFERENCES public.rfq_processing_sessions(session_id) ON DELETE CASCADE,
    material_specs JSONB NOT NULL DEFAULT '[]',
    extraction_metadata JSONB NOT NULL DEFAULT '{}',
    confidence_score DECIMAL(3,2) CHECK (confidence_score >= 0 AND confidence_score <= 1),
    processing_time_ms INTEGER CHECK (processing_time_ms >= 0),
    model_used VARCHAR(100),
    model_temperature DECIMAL(3,2),
    retry_attempt INTEGER DEFAULT 0 CHECK (retry_attempt >= 0),
    tokens_used INTEGER,
    api_cost_usd DECIMAL(10,6),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Constraints
    CONSTRAINT rfq_extraction_material_specs_array
        CHECK (jsonb_typeof(material_specs) = 'array'),
    CONSTRAINT rfq_extraction_metadata_object
        CHECK (jsonb_typeof(extraction_metadata) = 'object')
);

-- Indexes
CREATE INDEX idx_rfq_extraction_session_id ON public.rfq_extraction_results(session_id);
CREATE INDEX idx_rfq_extraction_confidence ON public.rfq_extraction_results(confidence_score);
CREATE INDEX idx_rfq_extraction_model_used ON public.rfq_extraction_results(model_used);
CREATE INDEX idx_rfq_extraction_retry_attempt ON public.rfq_extraction_results(retry_attempt);
CREATE INDEX idx_rfq_extraction_material_specs
    ON public.rfq_extraction_results USING GIN (material_specs);
CREATE INDEX idx_rfq_extraction_created_at ON public.rfq_extraction_results(created_at);

-- Performance index for specs count
CREATE INDEX idx_rfq_extraction_specs_count
    ON public.rfq_extraction_results((jsonb_array_length(material_specs)));
```

### 5. rfq_validation_results

**Purpose**: Store validation results and quality metrics

```sql
CREATE TABLE public.rfq_validation_results (
    validation_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID NOT NULL REFERENCES public.rfq_processing_sessions(session_id) ON DELETE CASCADE,
    extraction_id UUID REFERENCES public.rfq_extraction_results(extraction_id),
    validation_results JSONB NOT NULL DEFAULT '{}',
    is_valid BOOLEAN NOT NULL,
    validation_errors JSONB DEFAULT '[]',
    validation_warnings JSONB DEFAULT '[]',
    confidence_score DECIMAL(3,2) CHECK (confidence_score >= 0 AND confidence_score <= 1),
    processing_time_ms INTEGER CHECK (processing_time_ms >= 0),
    validation_method VARCHAR(50) DEFAULT 'ai' CHECK (validation_method IN ('ai', 'rules', 'hybrid')),
    rules_passed INTEGER DEFAULT 0,
    rules_failed INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Constraints
    CONSTRAINT rfq_validation_results_object
        CHECK (jsonb_typeof(validation_results) = 'object'),
    CONSTRAINT rfq_validation_errors_array
        CHECK (jsonb_typeof(validation_errors) = 'array'),
    CONSTRAINT rfq_validation_warnings_array
        CHECK (jsonb_typeof(validation_warnings) = 'array'),
    CONSTRAINT rfq_validation_rules_non_negative
        CHECK (rules_passed >= 0 AND rules_failed >= 0)
);

-- Indexes
CREATE INDEX idx_rfq_validation_session_id ON public.rfq_validation_results(session_id);
CREATE INDEX idx_rfq_validation_extraction_id ON public.rfq_validation_results(extraction_id);
CREATE INDEX idx_rfq_validation_is_valid ON public.rfq_validation_results(is_valid);
CREATE INDEX idx_rfq_validation_confidence ON public.rfq_validation_results(confidence_score);
CREATE INDEX idx_rfq_validation_method ON public.rfq_validation_results(validation_method);
CREATE INDEX idx_rfq_validation_results_gin
    ON public.rfq_validation_results USING GIN (validation_results);

-- Error analysis indexes
CREATE INDEX idx_rfq_validation_errors_count
    ON public.rfq_validation_results((jsonb_array_length(validation_errors)));
CREATE INDEX idx_rfq_validation_warnings_count
    ON public.rfq_validation_results((jsonb_array_length(validation_warnings)));
```

### 6. rfq_normalization_results

**Purpose**: Store normalization results and transformations

```sql
CREATE TABLE public.rfq_normalization_results (
    normalization_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID NOT NULL REFERENCES public.rfq_processing_sessions(session_id) ON DELETE CASCADE,
    validation_id UUID REFERENCES public.rfq_validation_results(validation_id),
    normalized_specs JSONB NOT NULL DEFAULT '[]',
    normalization_metadata JSONB NOT NULL DEFAULT '{}',
    units_standardized BOOLEAN DEFAULT FALSE,
    values_normalized BOOLEAN DEFAULT FALSE,
    terminology_standardized BOOLEAN DEFAULT FALSE,
    processing_time_ms INTEGER CHECK (processing_time_ms >= 0),
    transformations_applied JSONB DEFAULT '[]',
    normalization_method VARCHAR(50) DEFAULT 'ai' CHECK (normalization_method IN ('ai', 'rules', 'hybrid')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Constraints
    CONSTRAINT rfq_normalization_specs_array
        CHECK (jsonb_typeof(normalized_specs) = 'array'),
    CONSTRAINT rfq_normalization_metadata_object
        CHECK (jsonb_typeof(normalization_metadata) = 'object'),
    CONSTRAINT rfq_normalization_transformations_array
        CHECK (jsonb_typeof(transformations_applied) = 'array')
);

-- Indexes
CREATE INDEX idx_rfq_normalization_session_id ON public.rfq_normalization_results(session_id);
CREATE INDEX idx_rfq_normalization_validation_id ON public.rfq_normalization_results(validation_id);
CREATE INDEX idx_rfq_normalization_units ON public.rfq_normalization_results(units_standardized);
CREATE INDEX idx_rfq_normalization_values ON public.rfq_normalization_results(values_normalized);
CREATE INDEX idx_rfq_normalization_method ON public.rfq_normalization_results(normalization_method);
CREATE INDEX idx_rfq_normalization_specs
    ON public.rfq_normalization_results USING GIN (normalized_specs);
```

### 7. rfq_final_outputs

**Purpose**: Store final processed outputs and metrics

```sql
CREATE TABLE public.rfq_final_outputs (
    output_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID NOT NULL REFERENCES public.rfq_processing_sessions(session_id) ON DELETE CASCADE,
    final_output JSONB NOT NULL,
    output_format VARCHAR(50) DEFAULT 'json' CHECK (output_format IN ('json', 'xml', 'csv', 'custom')),
    total_processing_time_ms INTEGER CHECK (total_processing_time_ms >= 0),
    total_specs_extracted INTEGER DEFAULT 0 CHECK (total_specs_extracted >= 0),
    success_rate DECIMAL(5,2) CHECK (success_rate >= 0 AND success_rate <= 100),
    quality_score DECIMAL(3,2) CHECK (quality_score >= 0 AND quality_score <= 1),
    output_size_bytes INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Constraints
    CONSTRAINT rfq_final_output_not_empty
        CHECK (jsonb_typeof(final_output) = 'object' AND final_output != '{}')
);

-- Indexes
CREATE INDEX idx_rfq_final_outputs_session_id ON public.rfq_final_outputs(session_id);
CREATE INDEX idx_rfq_final_outputs_success_rate ON public.rfq_final_outputs(success_rate);
CREATE INDEX idx_rfq_final_outputs_quality_score ON public.rfq_final_outputs(quality_score);
CREATE INDEX idx_rfq_final_outputs_specs_count ON public.rfq_final_outputs(total_specs_extracted);
CREATE INDEX idx_rfq_final_outputs_processing_time ON public.rfq_final_outputs(total_processing_time_ms);
CREATE INDEX idx_rfq_final_outputs_final_output
    ON public.rfq_final_outputs USING GIN (final_output);
```

## 🔧 Database Functions and Procedures

### Stored Procedures

```sql
-- Function to get client processing statistics
CREATE OR REPLACE FUNCTION get_client_processing_stats(
    p_client_id UUID,
    p_start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW() - INTERVAL '30 days',
    p_end_date TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
RETURNS TABLE (
    total_sessions INTEGER,
    successful_sessions INTEGER,
    failed_sessions INTEGER,
    avg_processing_time_ms NUMERIC,
    total_specs_extracted INTEGER,
    avg_confidence_score NUMERIC,
    success_rate NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        COUNT(*)::INTEGER as total_sessions,
        COUNT(CASE WHEN s.status = 'completed' THEN 1 END)::INTEGER as successful_sessions,
        COUNT(CASE WHEN s.status = 'failed' THEN 1 END)::INTEGER as failed_sessions,
        AVG(s.processing_duration_ms)::NUMERIC as avg_processing_time_ms,
        COALESCE(SUM(fo.total_specs_extracted), 0)::INTEGER as total_specs_extracted,
        AVG(er.confidence_score)::NUMERIC as avg_confidence_score,
        (COUNT(CASE WHEN s.status = 'completed' THEN 1 END) * 100.0 / COUNT(*))::NUMERIC as success_rate
    FROM public.rfq_processing_sessions s
    LEFT JOIN public.rfq_extraction_results er ON s.session_id = er.session_id
    LEFT JOIN public.rfq_final_outputs fo ON s.session_id = fo.session_id
    WHERE s.client_id = p_client_id
    AND s.created_at BETWEEN p_start_date AND p_end_date;
END;
$$ LANGUAGE plpgsql;

-- Function to cleanup old processing data
CREATE OR REPLACE FUNCTION cleanup_old_processing_data(
    p_retention_days INTEGER DEFAULT 90
)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM public.rfq_processing_sessions
    WHERE created_at < NOW() - (p_retention_days || ' days')::INTERVAL
    AND status IN ('completed', 'failed');

    GET DIAGNOSTICS deleted_count = ROW_COUNT;

    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;
```

## 🔍 Query Patterns and Optimization

### Common Query Patterns

```sql
-- 1. Get active client configuration
SELECT c.*, cc.output_schema, cc.extraction_rules
FROM public.rfq_clients c
JOIN public.rfq_client_configurations cc ON c.client_id = cc.client_id
WHERE c.client_code = $1
AND c.status = 'active'
AND cc.is_active = TRUE;

-- 2. Get processing session with all results
SELECT
    s.*,
    er.material_specs,
    er.confidence_score as extraction_confidence,
    vr.is_valid,
    vr.validation_errors,
    nr.normalized_specs,
    fo.final_output
FROM public.rfq_processing_sessions s
LEFT JOIN public.rfq_extraction_results er ON s.session_id = er.session_id
LEFT JOIN public.rfq_validation_results vr ON s.session_id = vr.session_id
LEFT JOIN public.rfq_normalization_results nr ON s.session_id = nr.session_id
LEFT JOIN public.rfq_final_outputs fo ON s.session_id = fo.session_id
WHERE s.session_id = $1;

-- 3. Get client performance metrics
SELECT
    DATE_TRUNC('day', s.created_at) as processing_date,
    COUNT(*) as total_sessions,
    COUNT(CASE WHEN s.status = 'completed' THEN 1 END) as successful_sessions,
    AVG(s.processing_duration_ms) as avg_processing_time,
    AVG(er.confidence_score) as avg_confidence
FROM public.rfq_processing_sessions s
LEFT JOIN public.rfq_extraction_results er ON s.session_id = er.session_id
WHERE s.client_id = $1
AND s.created_at >= $2
GROUP BY DATE_TRUNC('day', s.created_at)
ORDER BY processing_date;
```

### Performance Optimization

```sql
-- Partitioning for large tables (optional)
CREATE TABLE public.rfq_processing_sessions_y2024
PARTITION OF public.rfq_processing_sessions
FOR VALUES FROM ('2024-01-01') TO ('2025-01-01');

-- Materialized view for client statistics
CREATE MATERIALIZED VIEW public.client_processing_summary AS
SELECT
    c.client_id,
    c.name,
    c.client_code,
    COUNT(s.session_id) as total_sessions,
    COUNT(CASE WHEN s.status = 'completed' THEN 1 END) as successful_sessions,
    AVG(s.processing_duration_ms) as avg_processing_time,
    MAX(s.created_at) as last_processing_date
FROM public.rfq_clients c
LEFT JOIN public.rfq_processing_sessions s ON c.client_id = s.client_id
WHERE s.created_at >= NOW() - INTERVAL '30 days'
GROUP BY c.client_id, c.name, c.client_code;

-- Refresh materialized view (run periodically)
REFRESH MATERIALIZED VIEW public.client_processing_summary;
```

This database design provides a robust foundation for the Vanilla Steel AI system with proper normalization, indexing, and performance optimization.
