# Technical Specification

Complete technical specification of Vanilla Steel AI system architecture, database design, functions, and configurations.

## 🏗️ System Architecture

### Technology Stack
- **Backend**: Python 3.11+ with FastAPI 0.104+
- **Database**: PostgreSQL 15+ with PgVector extension
- **AI/ML**: OpenRouter API, Anthropic Claude, OpenAI GPT
- **Caching**: Redis (optional)
- **Deployment**: Docker containers, Kubernetes ready
- **Monitoring**: Built-in logging with structured JSON output

### Core Components

```
┌─────────────────────────────────────────────────────────────────┐
│                    FastAPI Application                         │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐              │
│  │ Auth        │ │ Rate Limit  │ │ CORS        │              │
│  │ Middleware  │ │ Middleware  │ │ Middleware  │              │
│  └─────────────┘ └─────────────┘ └─────────────┘              │
│                                                                 │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐              │
│  │ RFQ Routes  │ │ Status      │ │ Schema      │              │
│  │ /v1/rfq/*   │ │ Routes      │ │ Routes      │              │
│  └─────────────┘ └─────────────┘ └─────────────┘              │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                 RFQ Processing Workflow                        │
│                                                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │ RFQProcessingWorkflow.arun()                            │   │
│  │                                                         │   │
│  │ 1. _run_extraction_with_retry()                        │   │
│  │    ├─ MAX_EXTRACTION_RETRIES = 3                       │   │
│  │    ├─ EXTRACTION_RETRY_DELAY = 2.0s                    │   │
│  │    └─ Progressive delays: 2s, 4s, 6s                   │   │
│  │                                                         │   │
│  │ 2. _run_validation_with_fallback()                     │   │
│  │    ├─ Primary validation agent                          │   │
│  │    └─ Fallback validation logic                         │   │
│  │                                                         │   │
│  │ 3. _run_normalization_with_fallback()                  │   │
│  │    ├─ Unit standardization                              │   │
│  │    └─ Value normalization                               │   │
│  │                                                         │   │
│  │ 4. _format_final_output()                              │   │
│  │    └─ Client-specific output formatting                 │   │
│  └─────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                      AI Agents Layer                           │
│                                                                 │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐   │
│  │ RFQExtractor    │ │ RFQValidator    │ │ RFQNormalizer   │   │
│  │ Agent           │ │ Agent           │ │ Agent           │   │
│  │                 │ │                 │ │                 │   │
│  │ - Temperature:  │ │ - Temperature:  │ │ - Temperature:  │   │
│  │   0.0 (precise) │ │   0.1 (stable)  │ │   0.0 (precise) │   │
│  │                 │ │                 │ │                 │   │
│  │ - Model: Claude │ │ - Model: Claude │ │ - Model: Claude │   │
│  │   or GPT-4      │ │   or GPT-4      │ │   or GPT-4      │   │
│  │                 │ │                 │ │                 │   │
│  │ - Retry: Yes    │ │ - Fallback: Yes │ │ - Fallback: Yes │   │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘   │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                   Database Access Layer                        │
│                                                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │ RFQDatabaseTools                                        │   │
│  │                                                         │   │
│  │ - get_client_configuration(client_id: str) -> dict     │   │
│  │ - save_processing_result(session_id: str, result)      │   │
│  │ - execute_with_retry(query: str, params: dict)         │   │
│  │ - generate_dynamic_instructions(client_id: str)        │   │
│  │ - get_client_flags(client_id: str) -> dict             │   │
│  │ - update_client_schema(client_id: str, schema: dict)   │   │
│  └─────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                    PostgreSQL Database                         │
│                                                                 │
│  Schema: public (configurable via POSTGRES_INIT_SCHEMA)        │
│  Connection Pool: 5-20 connections                             │
│  Encoding: UTF-8                                               │
│  Timezone: UTC                                                 │
└─────────────────────────────────────────────────────────────────┘
```

## 🗄️ Database Schema Design

### Connection Configuration

```python
# Database URL Format
DATABASE_URL = "postgresql://{DB_USER}:{DB_PASS}@{DB_HOST}:{DB_PORT}/{DB_DATABASE}"

# Connection Pool Settings
POOL_SIZE = 5
MAX_OVERFLOW = 15
POOL_TIMEOUT = 30
POOL_RECYCLE = 3600
```

### Table Definitions

#### 1. rfq_clients
```sql
CREATE TABLE public.rfq_clients (
    client_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    client_code VARCHAR(100) UNIQUE NOT NULL,
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
    has_kb BOOLEAN DEFAULT FALSE,
    has_instruction BOOLEAN DEFAULT FALSE,
    has_output_preference BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_rfq_clients_status ON public.rfq_clients(status);
CREATE INDEX idx_rfq_clients_client_code ON public.rfq_clients(client_code);
```

#### 2. rfq_client_configurations
```sql
CREATE TABLE public.rfq_client_configurations (
    configuration_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    client_id UUID NOT NULL REFERENCES public.rfq_clients(client_id) ON DELETE CASCADE,
    output_schema JSONB NOT NULL DEFAULT '{}',
    extraction_rules JSONB NOT NULL DEFAULT '{}',
    validation_rules JSONB NOT NULL DEFAULT '{}',
    normalization_rules JSONB NOT NULL DEFAULT '{}',
    formatter_output_config_id UUID,
    schema_version VARCHAR(20) DEFAULT '1.0',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_rfq_client_configs_client_id ON public.rfq_client_configurations(client_id);
CREATE INDEX idx_rfq_client_configs_active ON public.rfq_client_configurations(is_active);
CREATE INDEX idx_rfq_client_configs_schema_version ON public.rfq_client_configurations(schema_version);

-- JSONB Indexes for performance
CREATE INDEX idx_rfq_client_configs_output_schema ON public.rfq_client_configurations USING GIN (output_schema);
CREATE INDEX idx_rfq_client_configs_extraction_rules ON public.rfq_client_configurations USING GIN (extraction_rules);
```

#### 3. rfq_processing_sessions
```sql
CREATE TABLE public.rfq_processing_sessions (
    session_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    client_id UUID NOT NULL REFERENCES public.rfq_clients(client_id),
    email_id VARCHAR(255) NOT NULL,
    email_subject TEXT,
    email_body TEXT NOT NULL,
    sender_email VARCHAR(255),
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'timeout')),
    performance_mode BOOLEAN DEFAULT FALSE,
    debug_mode BOOLEAN DEFAULT FALSE,
    processing_start_time TIMESTAMP WITH TIME ZONE,
    processing_end_time TIMESTAMP WITH TIME ZONE,
    processing_duration_ms INTEGER,
    retry_count INTEGER DEFAULT 0,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_rfq_sessions_client_id ON public.rfq_processing_sessions(client_id);
CREATE INDEX idx_rfq_sessions_status ON public.rfq_processing_sessions(status);
CREATE INDEX idx_rfq_sessions_created_at ON public.rfq_processing_sessions(created_at);
CREATE INDEX idx_rfq_sessions_email_id ON public.rfq_processing_sessions(email_id);
```

#### 4. rfq_extraction_results
```sql
CREATE TABLE public.rfq_extraction_results (
    extraction_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID NOT NULL REFERENCES public.rfq_processing_sessions(session_id) ON DELETE CASCADE,
    material_specs JSONB NOT NULL DEFAULT '[]',
    extraction_metadata JSONB NOT NULL DEFAULT '{}',
    confidence_score DECIMAL(3,2) CHECK (confidence_score >= 0 AND confidence_score <= 1),
    processing_time_ms INTEGER,
    model_used VARCHAR(100),
    retry_attempt INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_rfq_extraction_session_id ON public.rfq_extraction_results(session_id);
CREATE INDEX idx_rfq_extraction_confidence ON public.rfq_extraction_results(confidence_score);
CREATE INDEX idx_rfq_extraction_material_specs ON public.rfq_extraction_results USING GIN (material_specs);
```

#### 5. rfq_validation_results
```sql
CREATE TABLE public.rfq_validation_results (
    validation_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID NOT NULL REFERENCES public.rfq_processing_sessions(session_id) ON DELETE CASCADE,
    extraction_id UUID REFERENCES public.rfq_extraction_results(extraction_id),
    validation_results JSONB NOT NULL DEFAULT '{}',
    is_valid BOOLEAN NOT NULL,
    validation_errors JSONB DEFAULT '[]',
    validation_warnings JSONB DEFAULT '[]',
    confidence_score DECIMAL(3,2),
    processing_time_ms INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_rfq_validation_session_id ON public.rfq_validation_results(session_id);
CREATE INDEX idx_rfq_validation_is_valid ON public.rfq_validation_results(is_valid);
CREATE INDEX idx_rfq_validation_results ON public.rfq_validation_results USING GIN (validation_results);
```

#### 6. rfq_normalization_results
```sql
CREATE TABLE public.rfq_normalization_results (
    normalization_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID NOT NULL REFERENCES public.rfq_processing_sessions(session_id) ON DELETE CASCADE,
    validation_id UUID REFERENCES public.rfq_validation_results(validation_id),
    normalized_specs JSONB NOT NULL DEFAULT '[]',
    normalization_metadata JSONB NOT NULL DEFAULT '{}',
    units_standardized BOOLEAN DEFAULT FALSE,
    values_normalized BOOLEAN DEFAULT FALSE,
    processing_time_ms INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_rfq_normalization_session_id ON public.rfq_normalization_results(session_id);
CREATE INDEX idx_rfq_normalization_specs ON public.rfq_normalization_results USING GIN (normalized_specs);
```

#### 7. rfq_final_outputs
```sql
CREATE TABLE public.rfq_final_outputs (
    output_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID NOT NULL REFERENCES public.rfq_processing_sessions(session_id) ON DELETE CASCADE,
    final_output JSONB NOT NULL,
    output_format VARCHAR(50) DEFAULT 'json',
    total_processing_time_ms INTEGER,
    total_specs_extracted INTEGER DEFAULT 0,
    success_rate DECIMAL(5,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_rfq_final_outputs_session_id ON public.rfq_final_outputs(session_id);
CREATE INDEX idx_rfq_final_outputs_success_rate ON public.rfq_final_outputs(success_rate);
CREATE INDEX idx_rfq_final_outputs_final_output ON public.rfq_final_outputs USING GIN (final_output);
```

### Database Triggers

```sql
-- Auto-update updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply to relevant tables
CREATE TRIGGER update_rfq_clients_updated_at BEFORE UPDATE ON public.rfq_clients
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_rfq_client_configurations_updated_at BEFORE UPDATE ON public.rfq_client_configurations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_rfq_processing_sessions_updated_at BEFORE UPDATE ON public.rfq_processing_sessions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

## 🔧 Core Functions & Classes

### 1. RFQProcessingWorkflow Class

**File**: `workflows/wf_rfq_processor.py`

```python
class RFQProcessingWorkflow:
    """Main RFQ processing workflow with retry mechanisms."""

    def __init__(self, client_id: str, debug_mode: bool = False, performance_mode: bool = False):
        self.client_id = client_id
        self.debug_mode = debug_mode
        self.performance_mode = performance_mode
        self.db_tools = RFQDatabaseTools(DATABASE_URL)
        self.logger = get_app_logger(__name__)

    async def arun(self, rfq_text: str, email_id: str = None, sender_email: str = None) -> AsyncIterator[dict]:
        """
        Main async processing method.

        Args:
            rfq_text: Raw RFQ email content
            email_id: Unique email identifier
            sender_email: Sender email address

        Yields:
            dict: Processing results at each stage
        """

    async def _run_extraction_with_retry(self, rfq_text: str, session_id: str) -> dict:
        """
        Extract material specifications with retry mechanism.

        Retry Logic:
        - MAX_EXTRACTION_RETRIES = 3
        - EXTRACTION_RETRY_DELAY = 2.0 seconds
        - Progressive delays: 2s, 4s, 6s
        - Retry on: empty results, timeouts, API errors

        Returns:
            dict: Extracted material specifications
        """

    def _generate_spec_ids(self, extracted_data: dict, session_id: str) -> dict:
        """Generate unique IDs for each material specification."""

    def _process_extracted_specs(self, extracted_data: dict) -> Tuple[List[dict], dict]:
        """Process and validate extracted specifications."""

    async def _run_validation_with_fallback(self, extracted_data: dict) -> dict:
        """Run validation with fallback mechanisms."""

    async def _run_normalization_with_fallback(self, validated_data: dict) -> dict:
        """Run normalization with fallback mechanisms."""

    def _format_final_output(self, normalized_data: dict) -> dict:
        """Format final output according to client configuration."""
```

### 2. AI Agent Classes

#### RFQExtractorAgent

**File**: `agents/rfq_extractor.py`

```python
class RFQExtractorAgent(BaseAgent):
    """AI agent for extracting material specifications from RFQ text."""

    def __init__(self, config: dict, model_id: str, user_id: str, debug_mode: bool = False):
        super().__init__(config, model_id, user_id, debug_mode)
        self.db_tools = RFQDatabaseTools(DATABASE_URL)
        self.client_flags = self.db_tools.get_client_flags(user_id)

    def _get_agent_goal(self) -> str:
        """Define extraction goal."""
        return "Extract structured material specifications from RFQ emails with high accuracy"

    def _get_default_temperature(self) -> float:
        """Return temperature for consistent extraction."""
        return 0.0  # Precise, deterministic extraction

    async def arun(self, rfq_text: str) -> AgentResponse:
        """
        Extract material specifications from RFQ text.

        Args:
            rfq_text: Raw RFQ email content

        Returns:
            AgentResponse: Structured extraction results
        """
```

#### RFQValidatorAgent

**File**: `agents/rfq_validator.py`

```python
class RFQValidatorAgent(BaseAgent):
    """AI agent for validating extracted material specifications."""

    def _get_agent_goal(self) -> str:
        return "Validate and correct extracted material specifications for accuracy and completeness"

    def _get_default_temperature(self) -> float:
        return 0.1  # Slightly higher for reasoning flexibility

    async def arun(self, extracted_data: str) -> AgentResponse:
        """
        Validate extracted material specifications.

        Validation includes:
        - Required field presence
        - Data type validation
        - Business rule compliance
        - Cross-field consistency
        - Confidence scoring
        """
```

#### RFQNormalizerAgent

**File**: `agents/rfq_normalizer.py`

```python
class RFQNormalizerAgent(BaseAgent):
    """AI agent for normalizing and standardizing material specifications."""

    def _get_agent_goal(self) -> str:
        return "Normalize and standardize material specifications to consistent formats and units"

    def _get_default_temperature(self) -> float:
        return 0.0  # Precise normalization

    async def arun(self, validated_data: str) -> AgentResponse:
        """
        Normalize material specifications.

        Normalization includes:
        - Unit standardization (tons->kg, inches->mm)
        - Value formatting (decimals, ranges)
        - Terminology standardization
        - Data structure consistency
        """
```

### 3. Database Tools Class

**File**: `tools/rfq/database_tools.py`

```python
class RFQDatabaseTools:
    """Database operations for RFQ processing."""

    def __init__(self, db_url: str):
        self.db_url = db_url
        self.engine = create_engine(db_url, pool_size=5, max_overflow=15)
        self.SessionLocal = sessionmaker(bind=self.engine)

    def get_client_configuration(self, client_id: str) -> dict:
        """
        Retrieve client-specific configuration.

        Returns:
            dict: {
                "output_schema": {...},
                "extraction_rules": {...},
                "validation_rules": {...},
                "normalization_rules": {...}
            }
        """

    def save_processing_result(self, session_id: str, result: dict) -> bool:
        """Save processing results to database."""

    def execute_with_retry(self, query: str, params: dict, max_retries: int = 3) -> Any:
        """Execute database query with retry mechanism."""

    def generate_dynamic_instructions(self, client_id: str, instruction_type: str) -> str:
        """Generate dynamic instructions based on client configuration."""

    def get_client_flags(self, client_id: str) -> dict:
        """
        Get client feature flags.

        Returns:
            dict: {
                "has_kb": bool,
                "has_instruction": bool,
                "has_output_preference": bool
            }
        """

    def update_client_schema(self, client_id: str, schema_data: dict) -> bool:
        """Update client schema configuration."""
```

## ⚙️ Configuration System

### Environment Variables

```python
# api/settings.py
class ApiSettings(BaseSettings):
    # Environment
    runtime_env: EnvironmentType = EnvironmentType.DEV
    testing: bool = False
    docs_enabled: bool = True

    # API Keys
    openrouter_api_key: str
    agno_api_key: str
    unkey_api_key: str
    unkey_api_id: str
    agno_workspace_id: str
    agno_enable_telemetry: str = "false"
    exa_api_key: str

    # Database
    db_host: str
    db_port: int = 5432
    db_user: str
    db_pass: str
    db_database: str
    postgres_init_schema: str = "public"

    # CORS
    cors_origins: List[str] = ["http://localhost:3000"]

    # Application
    app_title: str = "vanilla-steel-ai"
    app_version: str = "1.0"

    class Config:
        env_file = ".env"
        case_sensitive = True
```

### Client Configuration Schema

```json
{
  "client_id": "uuid",
  "output_schema": {
    "fields": {
      "grade": {
        "type": "string",
        "required": true,
        "validation": {
          "pattern": "[A-Z][0-9]{2}[A-Z]?[0-9]?",
          "max_length": 20
        }
      },
      "coating": {
        "type": "string",
        "required": false,
        "validation": {
          "pattern": "Z[0-9]{2,3}",
          "allowed_values": ["ZE25/25", "ZE50/50", "ZE75/75"]
        }
      },
      "thickness_min": {
        "type": "number",
        "required": false,
        "unit": "mm",
        "validation": {
          "min": 0.1,
          "max": 50.0
        }
      },
      "weight_min": {
        "type": "number",
        "required": false,
        "unit": "kg",
        "validation": {
          "min": 100,
          "max": 100000
        }
      }
    }
  },
  "extraction_rules": {
    "patterns": {
      "grade": "[A-Z][0-9]{2}[A-Z]?[0-9]?",
      "coating": "Z[0-9]{2,3}",
      "thickness": "\\d+\\.?\\d*\\s*mm",
      "weight": "\\d+\\.?\\d*\\s*(tons?|kg)"
    },
    "context_keywords": [
      "steel", "coils", "sheets", "plates",
      "grade", "coating", "thickness", "weight"
    ],
    "ignore_patterns": [
      "delivery", "payment", "terms"
    ]
  },
  "validation_rules": {
    "required_fields": ["grade"],
    "field_validations": {
      "grade": {
        "pattern": "[A-Z][0-9]{2}[A-Z]?[0-9]?",
        "error_message": "Invalid steel grade format"
      },
      "quantity": {
        "min": 0,
        "max": 1000000,
        "error_message": "Quantity must be between 0 and 1,000,000"
      }
    },
    "cross_field_validations": [
      {
        "condition": "thickness_min > thickness_max",
        "error_message": "Minimum thickness cannot exceed maximum thickness"
      }
    ]
  },
  "normalization_rules": {
    "unit_conversions": {
      "weight": {
        "tons": 1000,
        "ton": 1000,
        "t": 1000,
        "kg": 1,
        "kilogram": 1
      },
      "thickness": {
        "mm": 1,
        "millimeter": 1,
        "cm": 10,
        "inch": 25.4,
        "in": 25.4
      }
    },
    "value_standardization": {
      "grade_formats": ["uppercase", "no_spaces"],
      "decimal_places": {
        "thickness": 2,
        "weight": 0
      }
    },
    "terminology_mapping": {
      "galvanized": "ZE coating",
      "hot dip": "ZE coating",
      "coil": "coils",
      "sheet": "sheets"
    }
  }
}
```

## 🔌 API Endpoints Specification

### Authentication Middleware

```python
# api/middleware/auth.py
async def authenticate_request(request: Request) -> Optional[str]:
    """
    Authenticate API request using API key.

    Headers:
        api-key: Client API key

    Returns:
        str: Client ID if authenticated, None otherwise
    """
    api_key = request.headers.get("api-key")
    if not api_key:
        raise HTTPException(status_code=401, detail="API key required")

    # Validate API key against database
    client_id = validate_api_key(api_key)
    if not client_id:
        raise HTTPException(status_code=401, detail="Invalid API key")

    return client_id
```

### Core API Routes

#### 1. RFQ Processing Endpoint

```python
# api/routes/rfq.py
@router.post("/v1/rfq/{client_id}/process")
async def process_rfq(
    client_id: str,
    request: RFQProcessingRequest,
    api_key: str = Depends(get_api_key)
) -> RFQProcessingResponse:
    """
    Process RFQ email and extract material specifications.

    Request Body:
    {
        "email_id": "unique-email-identifier",
        "email_body": "RFQ email content...",
        "email_subject": "RFQ for Steel Coils",
        "sender_email": "<EMAIL>",
        "performance_mode": false,
        "debug_mode": false
    }

    Response:
    {
        "status": "success",
        "data": {
            "session_id": "sess_123456789",
            "material_specs": [...],
            "extraction_metadata": {...}
        }
    }
    """
```

#### 2. Processing Status Endpoint

```python
@router.get("/v1/rfq/{client_id}/status/{session_id}")
async def get_processing_status(
    client_id: str,
    session_id: str,
    api_key: str = Depends(get_api_key)
) -> ProcessingStatusResponse:
    """
    Get processing status for a specific session.

    Response:
    {
        "session_id": "sess_123456789",
        "status": "completed",
        "progress": 100,
        "created_at": "2024-12-21T10:30:00Z",
        "completed_at": "2024-12-21T10:30:02Z",
        "processing_time": 2.3,
        "retry_count": 0,
        "error_message": null
    }
    """
```

#### 3. Schema Management Endpoints

```python
@router.get("/v1/rfq/{client_id}/schema")
async def get_client_schema(
    client_id: str,
    api_key: str = Depends(get_api_key)
) -> ClientSchemaResponse:
    """Get client-specific schema configuration."""

@router.put("/v1/rfq/{client_id}/schema")
async def update_client_schema(
    client_id: str,
    schema_update: SchemaUpdateRequest,
    api_key: str = Depends(get_api_key)
) -> SchemaUpdateResponse:
    """Update client schema configuration."""

@router.get("/v1/rfq/schema/template")
async def get_schema_template() -> SchemaTemplateResponse:
    """Get schema configuration template."""
```

## 🔄 Processing Flow Details

### Extraction Retry Mechanism

```python
# Constants
MAX_EXTRACTION_RETRIES = 3
EXTRACTION_RETRY_DELAY = 2.0  # seconds
EXTRACTION_TIMEOUT = 30.0     # seconds

async def _run_extraction_with_retry(self, rfq_text: str, session_id: str) -> dict:
    """
    Extraction with progressive retry delays.

    Retry Schedule:
    - Attempt 1: Immediate
    - Attempt 2: 2 second delay
    - Attempt 3: 4 second delay
    - Attempt 4: 6 second delay

    Retry Conditions:
    - Empty material_specs array
    - Timeout errors
    - API connection errors
    - Model response errors

    No Retry Conditions:
    - Authentication errors
    - Rate limit errors
    - Invalid input format
    """

    for attempt in range(MAX_EXTRACTION_RETRIES + 1):
        try:
            # Run extraction with timeout
            result = await asyncio.wait_for(
                self.extractor.arun(rfq_text),
                timeout=EXTRACTION_TIMEOUT
            )

            # Check if result has valid specifications
            if self._has_valid_specs(result):
                self.logger.info(f"Extraction successful on attempt {attempt + 1}")
                return result

            # Log empty result
            self.logger.warning(f"Empty extraction result on attempt {attempt + 1}")

        except (asyncio.TimeoutError, APIError, ConnectionError) as e:
            self.logger.warning(f"Extraction attempt {attempt + 1} failed: {e}")

            # Don't retry on final attempt
            if attempt == MAX_EXTRACTION_RETRIES:
                raise ExtractionError(f"Extraction failed after {MAX_EXTRACTION_RETRIES + 1} attempts")

            # Progressive delay
            delay = EXTRACTION_RETRY_DELAY * (attempt + 1)
            self.logger.info(f"Retrying extraction in {delay} seconds...")
            await asyncio.sleep(delay)

        except (AuthenticationError, RateLimitError, ValidationError) as e:
            # Don't retry these errors
            self.logger.error(f"Non-retryable extraction error: {e}")
            raise

    raise ExtractionError("Extraction failed: No valid specifications found")

def _has_valid_specs(self, result: dict) -> bool:
    """
    Check if extraction result contains valid specifications.

    Valid criteria:
    - material_specs array is not empty
    - At least one spec has required fields
    - Confidence score above threshold (0.3)
    """
    if not result or "material_specs" not in result:
        return False

    specs = result["material_specs"]
    if not specs or len(specs) == 0:
        return False

    # Check if at least one spec has content
    for spec in specs:
        if spec.get("specification") and len(spec["specification"]) > 0:
            return True

    return False
```

### Validation Flow

```python
async def _run_validation_with_fallback(self, extracted_data: dict) -> dict:
    """
    Validation with fallback mechanisms.

    Primary Validation:
    - AI-powered validation agent
    - Business rule validation
    - Cross-field consistency checks

    Fallback Validation:
    - Rule-based validation only
    - Basic data type checks
    - Required field validation
    """

    try:
        # Primary AI validation
        validation_result = await self.validator.arun(json.dumps(extracted_data))

        if validation_result and validation_result.content:
            return json.loads(validation_result.content)

    except Exception as e:
        self.logger.warning(f"Primary validation failed: {e}")

    # Fallback to rule-based validation
    return self._fallback_validation(extracted_data)

def _fallback_validation(self, extracted_data: dict) -> dict:
    """
    Rule-based fallback validation.

    Checks:
    - Required fields present
    - Data types correct
    - Value ranges valid
    - Basic consistency rules
    """
    validated_specs = []
    validation_errors = []

    for spec in extracted_data.get("material_specs", []):
        spec_validation = self._validate_single_spec(spec)
        validated_specs.append(spec_validation["spec"])
        validation_errors.extend(spec_validation["errors"])

    return {
        "material_specs": validated_specs,
        "validation_metadata": {
            "method": "fallback",
            "errors": validation_errors,
            "is_valid": len(validation_errors) == 0
        }
    }
```

### Normalization Flow

```python
async def _run_normalization_with_fallback(self, validated_data: dict) -> dict:
    """
    Normalization with fallback mechanisms.

    Primary Normalization:
    - AI-powered normalization agent
    - Context-aware unit conversion
    - Intelligent value standardization

    Fallback Normalization:
    - Rule-based unit conversion
    - Standard value formatting
    - Basic terminology mapping
    """

    try:
        # Primary AI normalization
        normalization_result = await self.normalizer.arun(json.dumps(validated_data))

        if normalization_result and normalization_result.content:
            return json.loads(normalization_result.content)

    except Exception as e:
        self.logger.warning(f"Primary normalization failed: {e}")

    # Fallback to rule-based normalization
    return self._fallback_normalization(validated_data)

def _fallback_normalization(self, validated_data: dict) -> dict:
    """
    Rule-based fallback normalization.

    Operations:
    - Unit standardization (tons->kg, inches->mm)
    - Decimal place formatting
    - Value range normalization
    - Terminology standardization
    """
    normalized_specs = []

    for spec in validated_data.get("material_specs", []):
        normalized_spec = self._normalize_single_spec(spec)
        normalized_specs.append(normalized_spec)

    return {
        "material_specs": normalized_specs,
        "normalization_metadata": {
            "method": "fallback",
            "units_standardized": True,
            "values_normalized": True
        }
    }
```

## 📊 Data Models

### Request/Response Models

```python
# models/rfq/api.py
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime

class RFQProcessingRequest(BaseModel):
    """Request model for RFQ processing."""
    email_id: str = Field(..., description="Unique email identifier")
    email_body: str = Field(..., description="RFQ email content")
    email_subject: Optional[str] = Field(None, description="Email subject line")
    sender_email: Optional[str] = Field(None, description="Sender email address")
    performance_mode: bool = Field(False, description="Enable performance mode")
    debug_mode: bool = Field(False, description="Enable debug mode")

class MaterialSpecification(BaseModel):
    """Material specification model."""
    spec_id: Optional[str] = Field(None, description="Unique specification ID")
    grade: Optional[str] = Field(None, description="Steel grade")
    coating: Optional[str] = Field(None, description="Coating specification")
    thickness_min: Optional[float] = Field(None, description="Minimum thickness in mm")
    thickness_max: Optional[float] = Field(None, description="Maximum thickness in mm")
    weight_min: Optional[float] = Field(None, description="Minimum weight in kg")
    weight_max: Optional[float] = Field(None, description="Maximum weight in kg")
    surface: Optional[str] = Field(None, description="Surface finish")
    form: Optional[str] = Field(None, description="Product form (coils, sheets, etc.)")
    quantity: Optional[float] = Field(None, description="Quantity required")
    additional_specs: Optional[Dict[str, Any]] = Field(None, description="Additional specifications")

class ValidationResult(BaseModel):
    """Validation result model."""
    is_valid: bool = Field(..., description="Overall validation status")
    confidence: Optional[float] = Field(None, description="Validation confidence score")
    errors: List[str] = Field(default_factory=list, description="Validation errors")
    warnings: List[str] = Field(default_factory=list, description="Validation warnings")

class NormalizationResult(BaseModel):
    """Normalization result model."""
    units_standardized: bool = Field(..., description="Units standardization status")
    values_normalized: bool = Field(..., description="Values normalization status")
    changes_made: List[str] = Field(default_factory=list, description="List of changes made")

class ExtractedMaterialSpec(BaseModel):
    """Complete extracted material specification."""
    spec_id: str = Field(..., description="Unique specification ID")
    specification: MaterialSpecification = Field(..., description="Material specification")
    validation: ValidationResult = Field(..., description="Validation results")
    normalization: NormalizationResult = Field(..., description="Normalization results")

class ExtractionMetadata(BaseModel):
    """Extraction process metadata."""
    confidence: float = Field(..., description="Overall extraction confidence")
    processing_time: float = Field(..., description="Processing time in seconds")
    retry_count: int = Field(..., description="Number of retry attempts")
    model_used: str = Field(..., description="AI model used for extraction")
    source: str = Field(..., description="Source of extraction (email_body, attachment, etc.)")

class RFQProcessingResponse(BaseModel):
    """Response model for RFQ processing."""
    status: str = Field(..., description="Processing status")
    data: Dict[str, Any] = Field(..., description="Processing results")

    class Config:
        schema_extra = {
            "example": {
                "status": "success",
                "data": {
                    "session_id": "sess_123456789",
                    "material_specs": [
                        {
                            "spec_id": "spec_001",
                            "specification": {
                                "grade": "DX51D",
                                "coating": "ZE25/25",
                                "thickness_min": 0.4,
                                "weight_min": 20000
                            },
                            "validation": {
                                "is_valid": True,
                                "confidence": 0.95
                            },
                            "normalization": {
                                "units_standardized": True,
                                "values_normalized": True
                            }
                        }
                    ],
                    "extraction_metadata": {
                        "confidence": 0.95,
                        "processing_time": 2.3,
                        "retry_count": 0,
                        "model_used": "claude-3-sonnet",
                        "source": "email_body"
                    }
                }
            }
        }
```

## 🔧 Configuration Capabilities

### Dynamic Configuration Loading

```python
# Configuration loading hierarchy:
# 1. Environment variables (.env file)
# 2. Database client configuration
# 3. Default system configuration
# 4. Runtime parameter overrides

def load_client_configuration(client_id: str) -> dict:
    """
    Load complete client configuration from multiple sources.

    Priority Order:
    1. Database client_configurations table
    2. Default system configuration
    3. Environment variable overrides
    """

    # Load from database
    db_config = db_tools.get_client_configuration(client_id)

    # Merge with defaults
    default_config = get_default_configuration()
    merged_config = deep_merge(default_config, db_config)

    # Apply environment overrides
    env_overrides = get_environment_overrides()
    final_config = deep_merge(merged_config, env_overrides)

    return final_config

def get_default_configuration() -> dict:
    """Default system configuration."""
    return {
        "extraction_rules": {
            "max_retries": 3,
            "retry_delay": 2.0,
            "timeout": 30.0,
            "confidence_threshold": 0.3
        },
        "validation_rules": {
            "required_fields": ["grade"],
            "confidence_threshold": 0.5
        },
        "normalization_rules": {
            "unit_conversions": {
                "weight": {"tons": 1000, "kg": 1},
                "thickness": {"mm": 1, "inch": 25.4}
            }
        },
        "output_preferences": {
            "format_type": "json",
            "include_metadata": True,
            "confidence_scores": True
        }
    }
```

### Runtime Configuration Updates

```python
# API endpoint for updating configuration
@router.put("/v1/rfq/{client_id}/config")
async def update_client_configuration(
    client_id: str,
    config_update: ConfigurationUpdateRequest,
    api_key: str = Depends(get_api_key)
) -> ConfigurationUpdateResponse:
    """
    Update client configuration at runtime.

    Supported Updates:
    - Extraction rules and patterns
    - Validation rules and thresholds
    - Normalization rules and mappings
    - Output preferences and formatting
    - Feature flags and toggles
    """

    # Validate configuration update
    validation_result = validate_configuration_update(config_update)
    if not validation_result.is_valid:
        raise HTTPException(status_code=400, detail=validation_result.errors)

    # Apply configuration update
    success = db_tools.update_client_configuration(client_id, config_update.dict())

    if success:
        # Invalidate configuration cache
        invalidate_configuration_cache(client_id)

        return ConfigurationUpdateResponse(
            status="success",
            message="Configuration updated successfully",
            updated_fields=list(config_update.dict().keys())
        )
    else:
        raise HTTPException(status_code=500, detail="Failed to update configuration")
```

This technical specification provides complete implementation details for understanding and working with the Vanilla Steel AI system without requiring additional explanation.
