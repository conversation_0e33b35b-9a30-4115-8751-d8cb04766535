# Code Formatting Guide for Vanilla Steel AI

## Overview

This project uses the following tools for code formatting and linting:

- **yapf**: For Python code formatting (Yet Another Python Formatter)
- **ruff**: For code linting and additional formatting
- **mypy**: For type checking
- **docformatter**: For docstring formatting

## YAPF Configuration

YAPF is configured using the `.style.yapf` file in the root directory. The key settings include:

- Based on PEP 8 style
- 120 character line limit
- Tab indentation (to match ruff configuration)
- Smart handling of multi-line expressions

## How to Use YAPF

### Manual Formatting

You can manually format files using the yapf command:

```bash
# Format a single file
yapf -i path/to/file.py

# Format all Python files in a directory recursively
yapf -i -r path/to/directory/

# Format only files that need formatting (more efficient)
yapf -d -r path/to/directory/ | grep "^+++ " | cut -d" " -f2 | xargs yapf -i
```

### Pre-commit Hook

YAPF is integrated into our pre-commit hooks. When you commit changes, it will automatically format your code. To use the pre-commit hooks:

1. Make sure pre-commit is installed:
   ```bash
   pip install pre-commit
   ```

2. Install the git hooks:
   ```bash
   pre-commit install
   ```

3. Now, every time you commit, the formatting tools will run automatically.

### Testing YAPF Configuration

We've included a test script to verify that yapf is working correctly:

```bash
# First, see the unformatted code
cat scripts/verify_yapf.py

# Run yapf manually on the file
yapf -i scripts/verify_yapf.py

# See the formatted result
cat scripts/verify_yapf.py
```

## Ruff Integration

In addition to yapf, we use ruff for linting and formatting. Ruff and yapf work together:

- yapf handles detailed code formatting
- ruff handles linting and some additional formatting rules

The configuration in `pyproject.toml` ensures these tools work well together.

## Formatting Rules

Key formatting rules for this project:

1. 120 character line limit
2. Tab indentation
3. Double quotes for strings
4. PEP 8 compliant with some modifications

## Troubleshooting

If you encounter issues with formatting:

1. Check that both yapf and pre-commit are installed
2. Ensure the `.style.yapf` file is in the project root
3. Try running yapf manually on the file to see specific errors
4. If pre-commit doesn't seem to be running, check that hooks are installed with `pre-commit install`
