# Production Deployment Guide

## Overview

This guide covers the production deployment strategies for Vanilla Steel AI, including blue-green deployments, canary releases, and rollback procedures.

## Deployment Strategies

### 1. Blue-Green Deployment (Default)

This is the default and recommended deployment strategy for production releases.

**How it works:**
- New version is deployed as "green" with no traffic
- Health checks validate the green deployment
- Traffic is instantly switched from blue to green
- Old version (blue) remains available for instant rollback

**To deploy:**
```bash
# Automatic on push to main branch
git push origin main

# Or manual with GitHub UI
# Go to Actions → Deploy to Cloud Run (Production) → Run workflow
# Select deployment_strategy: blue-green
```

### 2. Direct Deployment

Use only for emergency fixes when blue-green overhead is not desired.

**To deploy:**
```bash
# Via GitHub UI
# Go to Actions → Deploy to Cloud Run (Production) → Run workflow
# Select deployment_strategy: direct
```

### 3. Canary Deployment

Use for high-risk changes that need gradual rollout.

**To deploy:**
```bash
# Via GitHub UI
# Go to Actions → Deploy to Cloud Run (Production) → Run workflow
# Select deployment_strategy: canary
# Select initial canary_percentage (5%, 10%, 25%, or 50%)
```

**To promote canary:**
```bash
# Via GitHub UI
# Go to Actions → Promote Canary Production → Run workflow
# Select new traffic_percentage (25%, 50%, 75%, or 100%)
# Select services (api, ui, or both)
```

## Rollback Procedures

### Quick Rollback to Previous Version

```bash
# Via GitHub UI
# Go to Actions → Rollback Production → Run workflow
# Select rollback_target: previous
# Select services: both
# Enter reason for rollback
```

### Rollback to Specific Revision

```bash
# First, find the revision you want:
gcloud run revisions list --service=vanilla-steel-ai-api --region=europe-west1

# Then via GitHub UI:
# Go to Actions → Rollback Production → Run workflow
# Select rollback_target: specific-revision
# Enter the revision names for API and/or UI
# Enter reason for rollback
```

### Emergency Rollback (Command Line)

If GitHub Actions is unavailable:

```bash
# Rollback API
gcloud run services update-traffic vanilla-steel-ai-api \
  --region=europe-west1 \
  --to-revisions=REVISION_NAME=100

# Rollback UI
gcloud run services update-traffic vanilla-steel-ai-ui \
  --region=europe-west1 \
  --to-revisions=REVISION_NAME=100
```

## Monitoring Deployments

### Check Current Revisions

```bash
# List all revisions
gcloud run revisions list --service=vanilla-steel-ai-api --region=europe-west1
gcloud run revisions list --service=vanilla-steel-ai-ui --region=europe-west1

# Check current traffic split
gcloud run services describe vanilla-steel-ai-api --region=europe-west1 --format="value(status.traffic[].percent,status.traffic[].revisionName)"
```

### Monitor Health

```bash
# API Health
curl https://vanilla-steel-ai-api-xxxxx.europe-west1.run.app/v1/health

# Check logs
gcloud logging read "resource.type=\"cloud_run_revision\" AND resource.labels.service_name=\"vanilla-steel-ai-api\"" \
  --project=vs-data-439613 --limit=50
```

## Best Practices

1. **Always use blue-green for standard deployments**
   - Provides instant rollback capability
   - Zero downtime deployment
   - Safe and tested approach

2. **Use canary for risky changes**
   - Major dependency updates
   - Database schema changes
   - New agent implementations

3. **Monitor after deployment**
   - Watch error rates for 15-30 minutes
   - Check API response times
   - Verify database queries are performant

4. **Document deployment decisions**
   - Use meaningful commit messages
   - Add deployment notes in GitHub workflow inputs
   - Create issues for failed deployments

## Deployment Checklist

Before deploying to production:

- [ ] All tests passing in staging
- [ ] No hardcoded secrets in code
- [ ] Database migrations tested in staging
- [ ] API documentation updated if needed
- [ ] Performance impact assessed
- [ ] Rollback plan identified
- [ ] Team notified of deployment

## Common Issues and Solutions

### Deployment Fails Health Check

```bash
# Check logs
gcloud logging read "severity>=ERROR" --project=vs-data-439613 --limit=100

# Common causes:
# - Database connection issues
# - Missing environment variables
# - Dependency conflicts
```

### Canary Shows High Error Rate

```bash
# Immediately reduce canary traffic
gcloud run services update-traffic vanilla-steel-ai-api \
  --region=europe-west1 \
  --to-tags=green=0

# Or use rollback workflow
```

### Database Migration Issues

```bash
# If migrations fail, disable auto-migration
# Deploy with MIGRATE_DB=false
# Run migrations manually after investigation
```

## Environment Variables

Production uses these key settings:
- `RUNTIME_ENV=prd`
- `DEBUG=false`
- `MIGRATE_DB=true` (be careful with schema changes)
- `PERFORMANCE_MODE=true`
- `DB_POOL_SIZE=10`
- `MIN_INSTANCES=1` (always warm)

## Security Notes

1. **Never commit secrets to repository**
2. **Rotate API keys regularly**
3. **Use Google Secret Manager for all secrets**
4. **Monitor for unusual API activity**
5. **Keep dependencies updated**

## Contact

For deployment issues:
- Check #deployments Slack channel
- Contact DevOps team
- Create issue with `deployment-issue` label

---

Last updated: {{ current_date }}
Deployment workflows version: 2.0 (with blue-green and rollback)
