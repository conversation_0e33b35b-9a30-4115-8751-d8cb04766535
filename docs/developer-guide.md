# Developer Guide

Comprehensive guide for developers working on Vanilla Steel AI.

## 🚀 Getting Started

### Prerequisites

- **Python 3.11+** - Required for modern async features
- **PostgreSQL 15+** - Primary database
- **Docker & Docker Compose** - For containerized development
- **Git** - Version control
- **uv** - Python package manager (recommended)

### Development Environment Setup

#### 1. Clone Repository

```bash
git clone https://github.com/your-org/vanilla-steel-ai.git
cd vanilla-steel-ai
```

#### 2. Install uv (Python Package Manager)

```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
```

#### 3. Setup Development Environment

```bash
# Run the setup script
./scripts/dev_setup.sh

# Activate virtual environment
source .venv/bin/activate

# Verify installation
python --version  # Should be 3.11+
```

#### 4. Environment Configuration

Create `.env` file in project root:

```bash
# Development Environment
RUNTIME_ENV=dev
TESTING=False
DEBUG=True

# API Keys
OPENROUTER_API_KEY=your_openrouter_key
AGNO_API_KEY=your_agno_key
UNKEY_API_KEY=your_unkey_key
UNKEY_API_ID=your_unkey_id
AGNO_WORKSPACE_ID=your_workspace_id
EXA_API_KEY=your_exa_key

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASS=your_password
DB_DATABASE=vanilla_steel_ai_dev
POSTGRES_INIT_SCHEMA=public

# Application Settings
DOCS_ENABLED=true
CORS_ORIGINS=http://localhost:3000,http://localhost:8080,http://localhost:8501
```

#### 5. Database Setup

```bash
# Start PostgreSQL (using Docker)
docker run -d \
  --name postgres-dev \
  -e POSTGRES_PASSWORD=your_password \
  -e POSTGRES_DB=vanilla_steel_ai_dev \
  -p 5432:5432 \
  postgres:15

# Run database migrations
python -m alembic upgrade head
```

## 🏗️ Project Structure

```
vanilla-steel-ai/
├── api/                    # FastAPI application
│   ├── main.py            # Application entry point
│   ├── routes/            # API route definitions
│   ├── middleware/        # Custom middleware
│   └── settings.py        # Configuration management
├── agents/                # AI agent implementations
│   ├── rfq_extractor.py   # Material extraction agent
│   ├── rfq_validator.py   # Data validation agent
│   └── rfq_normalizer.py  # Data normalization agent
├── workflows/             # Business logic workflows
│   └── wf_rfq_processor.py # Main RFQ processing workflow
├── tools/                 # Database and utility tools
│   └── rfq/
│       └── database_tools.py # Database operations
├── models/                # Data models and schemas
│   └── rfq/
│       └── api.py         # API request/response models
├── db/                    # Database configuration
│   ├── tables/            # SQLAlchemy table definitions
│   ├── migrations/        # Alembic migration files
│   └── session.py         # Database session management
├── utils/                 # Utility modules
│   ├── log.py            # Logging configuration
│   ├── monitoring.py     # Performance monitoring
│   └── exceptions/       # Custom exception classes
├── tests/                 # Test suite
│   ├── unit/             # Unit tests
│   ├── integration/      # Integration tests
│   ├── e2e/              # End-to-end tests
│   └── conftest.py       # Test configuration
├── docs/                  # Documentation
├── scripts/               # Development and deployment scripts
└── ui/                    # Streamlit UI (optional)
```

## 🔧 Development Workflow

### 1. Running the Application

#### Local Development Server

```bash
# Start FastAPI server with hot reload
uvicorn api.main:app --reload --host 0.0.0.0 --port 8000

# Start Streamlit UI (optional)
streamlit run ui/Home.py --server.port 8501
```

#### Docker Development

```bash
# Start all services
ag ws up

# View logs
ag ws logs

# Stop services
ag ws down
```

### 2. Code Quality Tools

#### Formatting

```bash
# Format code with black
./scripts/format.sh

# Or manually
black .
isort .
```

#### Linting

```bash
# Run linting checks
./scripts/validate.sh

# Or manually
flake8 .
mypy .
```

#### Type Checking

```bash
# Run type checking
mypy api/ agents/ workflows/ tools/
```

### 3. Testing

#### Run All Tests

```bash
# Run working tests (recommended)
python tests/run_working_tests.py

# Run with coverage
python tests/run_working_tests.py --coverage
```

#### Test Categories

```bash
# Unit tests only
python tests/run_working_tests.py --unit

# Integration tests only
python tests/run_working_tests.py --integration

# E2E tests only
python tests/run_working_tests.py --e2e

# Extraction retry tests (core feature)
python tests/run_working_tests.py --extraction
```

#### Writing Tests

**Unit Test Example:**

```python
# tests/unit/test_extractor.py
import pytest
from agents.rfq_extractor import RFQExtractorAgent

class TestRFQExtractor:
    @pytest.fixture
    def extractor(self):
        config = {"extraction_rules": {...}}
        return RFQExtractorAgent(config, "test-model", "test-user")

    def test_extraction_basic(self, extractor):
        rfq_text = "DX51D steel coils, 0.4mm thickness"
        result = extractor.extract_specifications(rfq_text)

        assert result["grade"] == "DX51D"
        assert result["thickness"] == 0.4
```

**Integration Test Example:**

```python
# tests/integration/test_api.py
def test_process_rfq_endpoint(test_client):
    response = test_client.post(
        "/v1/rfq/client_123/process",
        headers={"api-key": "test-key"},
        json={
            "email_id": "test_001",
            "email_body": "RFQ for DX51D steel coils...",
            "email_subject": "Steel RFQ"
        }
    )

    assert response.status_code == 200
    data = response.json()
    assert "material_specs" in data["data"]
```

## 🤖 Working with AI Agents

### Agent Architecture

All agents inherit from a base agent class:

```python
# agents/base/base_agent.py
class BaseAgent:
    def __init__(self, config: dict, model_id: str, user_id: str):
        self.config = config
        self.model_id = model_id
        self.user_id = user_id

    async def arun(self, input_data: str) -> AgentResponse:
        """Main processing method - implement in subclasses"""
        raise NotImplementedError

    def _get_agent_goal(self) -> str:
        """Define the agent's primary objective"""
        raise NotImplementedError

    def _get_agent_instructions(self) -> str:
        """Get detailed processing instructions"""
        raise NotImplementedError
```

### Creating a New Agent

```python
# agents/my_new_agent.py
from agents.base.base_agent import BaseAgent
from models.rfq.api import AgentResponse

class MyNewAgent(BaseAgent):
    def _get_agent_goal(self) -> str:
        return "Process specific type of data"

    def _get_agent_instructions(self) -> str:
        return """
        1. Parse input data
        2. Apply business rules
        3. Return structured output
        """

    async def arun(self, input_data: str) -> AgentResponse:
        # Implement your processing logic
        processed_data = self._process_data(input_data)

        return AgentResponse(
            content=processed_data,
            metadata={"confidence": 0.95}
        )

    def _process_data(self, data: str) -> dict:
        # Your custom processing logic
        return {"processed": True}

# Factory function
def get_my_new_agent(config: dict, model_id: str, user_id: str) -> MyNewAgent:
    return MyNewAgent(config, model_id, user_id)
```

### Agent Configuration

Agents are configured through database-stored client configurations:

```python
# Example client configuration
{
    "client_id": "client_123",
    "extraction_rules": {
        "patterns": {
            "grade": r"[A-Z][0-9]{2}[A-Z]?[0-9]?",
            "coating": r"Z[0-9]{2,3}"
        },
        "required_fields": ["grade", "quantity"]
    },
    "validation_rules": {
        "grade": {"pattern": r"[A-Z][0-9]{2}[A-Z]?[0-9]?"},
        "quantity": {"min": 0, "max": 1000000}
    },
    "normalization_rules": {
        "units": {
            "weight": "kg",
            "thickness": "mm"
        }
    }
}
```

## 🔄 Workflow Development

### Creating a New Workflow

```python
# workflows/my_workflow.py
from typing import AsyncIterator
from workflows.base.base_workflow import BaseWorkflow

class MyWorkflow(BaseWorkflow):
    def __init__(self, client_id: str, **kwargs):
        super().__init__(client_id, **kwargs)
        self.setup_agents()

    def setup_agents(self):
        """Initialize required agents"""
        config = self.db_tools.get_client_configuration(self.client_id)
        self.my_agent = get_my_agent(config, self.model_id, self.client_id)

    async def arun(self, input_data: str) -> AsyncIterator[dict]:
        """Main workflow execution"""
        try:
            # Step 1: Process with agent
            result = await self.my_agent.arun(input_data)

            # Step 2: Validate result
            if self._is_valid_result(result):
                yield {"status": "success", "data": result}
            else:
                yield {"status": "error", "message": "Invalid result"}

        except Exception as e:
            self.logger.error(f"Workflow error: {e}")
            yield {"status": "error", "message": str(e)}

    def _is_valid_result(self, result: dict) -> bool:
        """Validate workflow result"""
        return result is not None and "data" in result
```

## 🗄️ Database Operations

### Using Database Tools

```python
# Example database operations
from tools.rfq.database_tools import RFQDatabaseTools

# Initialize database tools
db_tools = RFQDatabaseTools("postgresql://user:pass@host:port/db")

# Get client configuration
config = db_tools.get_client_configuration("client_123")

# Save processing result
result = {
    "session_id": "sess_123",
    "material_specs": [...],
    "metadata": {...}
}
success = db_tools.save_processing_result("sess_123", result)

# Execute custom query with retry
query = "SELECT * FROM rfq_clients WHERE status = %(status)s"
params = {"status": "active"}
clients = db_tools.execute_with_retry(query, params)
```

### Database Migrations

```bash
# Create new migration
alembic revision --autogenerate -m "Add new table"

# Apply migrations
alembic upgrade head

# Rollback migration
alembic downgrade -1
```

## 🔧 Configuration Management

### Environment-Based Settings

```python
# api/settings.py
from pydantic_settings import BaseSettings
from enum import Enum

class EnvironmentType(str, Enum):
    DEV = "dev"
    STG = "stg"
    PRD = "prd"
    TEST = "test"

class ApiSettings(BaseSettings):
    runtime_env: EnvironmentType = EnvironmentType.DEV
    openrouter_api_key: str
    db_host: str
    db_port: int = 5432
    docs_enabled: bool = True

    class Config:
        env_file = ".env"
        case_sensitive = True

# Usage
settings = ApiSettings()
```

### Client-Specific Configuration

Client configurations are stored in the database and can be updated via API:

```python
# Update client configuration
PUT /v1/rfq/{client_id}/schema
{
    "extraction_rules": {...},
    "validation_rules": {...},
    "normalization_rules": {...}
}
```

## 📊 Monitoring & Debugging

### Logging

```python
# utils/log.py usage
from utils.log import get_app_logger

logger = get_app_logger(__name__)

# Log levels
logger.debug("Debug information")
logger.info("General information")
logger.warning("Warning message")
logger.error("Error occurred", exc_info=True)
logger.critical("Critical error")
```

### Performance Monitoring

```python
# utils/monitoring.py usage
from utils.monitoring import monitor_performance

@monitor_performance("rfq_processing")
async def process_rfq(rfq_data: str):
    # Your processing logic
    return result
```

### Debugging Tools

```bash
# Debug script
./scripts/debug.sh

# Check database connection
python scripts/test_db_connection.py

# Verify imports
python scripts/verify_imports.py
```

## 🚀 Deployment

### Building Docker Images

```bash
# Development image
./scripts/build_dev_image.sh

# Production image
./scripts/build_prd_image.sh
```

### Environment Validation

```bash
# Validate environment configuration
./scripts/validate_env.sh

# Check CloudSQL configuration (if using GCP)
./scripts/check_cloudsql_config.sh
```

## 🤝 Contributing Guidelines

### Code Style

- Follow PEP 8 style guidelines
- Use type hints for all function parameters and return values
- Write docstrings for all public functions and classes
- Keep functions focused and single-purpose

### Git Workflow

```bash
# Create feature branch
git checkout -b feature/new-feature

# Make changes and commit
git add .
git commit -m "feat: add new feature"

# Push and create pull request
git push origin feature/new-feature
```

### Pull Request Process

1. Create feature branch from `main`
2. Implement changes with tests
3. Run test suite and ensure all tests pass
4. Update documentation if needed
5. Submit pull request with clear description
6. Address review feedback
7. Merge after approval

### Testing Requirements

- All new features must include unit tests
- Integration tests for API endpoints
- E2E tests for complete workflows
- Maintain test coverage above 80%

This guide provides the foundation for effective development on the Vanilla Steel AI platform. For specific questions or advanced topics, refer to the other documentation files or create an issue in the repository.
