# Admin API Key Setup Guide

This guide explains how to set up the admin API key for client management in the Vanilla Steel AI system.

## Overview

The admin API key is used to secure client onboarding and management operations. It provides access to:
- Client onboarding API (`POST /clients/onboard`)
- Client management operations
- Administrative functions in the Streamlit UI

## Environment Configuration

### 1. Local Development (.env)

Add the admin API key to your `.env` file:

```bash
# Admin API key for client management (generated via Unkey)
ADMIN_API_KEY=rfq_admin_your_admin_key_here
```

### 2. API Settings (api/settings.py)

The admin API key is automatically loaded from environment variables:

```python
# Admin API key for client management
ADMIN_API_KEY: str = Field(..., description="Admin API key for client onboarding and management")
```

### 3. Docker Development (workspace/dev_resources.py)

The admin API key is passed to both API and UI containers:

```python
# API container environment
api_env = {
    # ... other variables
    "ADMIN_API_KEY": env_vars.get("ADMIN_API_KEY"),
}

# UI container environment  
ui_env = {
    # ... other variables
    "ADMIN_API_KEY": env_vars.get("ADMIN_API_KEY"),
    "API_BASE_URL": "http://vanilla-steel-ai-api:8000",
}
```

## Google Secret Manager Setup

### Staging Environment

Update the `vanilla-steel-ai-staging-secrets` secret in Google Secret Manager:

```yaml
# Add this to your staging secrets
ADMIN_API_KEY: rfq_admin_staging_key_here
```

### Production Environment

Update the `vanilla-steel-ai-production-secrets` secret in Google Secret Manager:

```yaml
# Add this to your production secrets  
ADMIN_API_KEY: rfq_admin_production_key_here
```

## Generating Admin API Keys

### Using Unkey Dashboard

1. Log into your Unkey dashboard
2. Navigate to your API (using `UNKEY_API_ID`)
3. Create a new key with:
   - **Prefix**: `rfq_admin`
   - **Name**: `Admin - Environment Name`
   - **Meta**: `{"role": "admin", "environment": "staging/production"}`
   - **Expires**: Set appropriate expiration or leave blank for no expiration

### Using Unkey API

```bash
curl -X POST "https://api.unkey.dev/v1/keys.createKey" \
  -H "Authorization: Bearer $UNKEY_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "apiId": "your-unkey-api-id",
    "prefix": "rfq_admin",
    "name": "Admin - Staging",
    "meta": {
      "role": "admin",
      "environment": "staging"
    }
  }'
```

## Deployment Updates

The deployment workflows have been updated to:

### Staging (.github/workflows/deploy-stg.yml)
- API deployment reads `ADMIN_API_KEY` from secrets.yml
- UI deployment gets `API_BASE_URL` pointing to staging API
- UI deployment reads `ADMIN_API_KEY` from secrets.yml

### Production (.github/workflows/deploy-prd.yml)
- API deployment reads `ADMIN_API_KEY` from secrets.yml
- UI deployment gets `API_BASE_URL` pointing to production API  
- UI deployment reads `ADMIN_API_KEY` from secrets.yml
- Both blue-green and direct deployment strategies updated

## Security Considerations

### Key Rotation
- Regularly rotate admin API keys
- Update all environments when rotating keys
- Monitor key usage through Unkey analytics

### Access Control
- Limit admin key distribution to authorized personnel only
- Use different keys for different environments
- Set appropriate expiration dates

### Monitoring
- Monitor admin API usage through logs
- Set up alerts for unusual admin activity
- Track client onboarding operations

## Troubleshooting

### Common Issues

1. **"Admin API key not configured" in UI**
   - Verify `ADMIN_API_KEY` is set in environment
   - Check Google Secret Manager has the key
   - Ensure deployment picked up the latest secrets

2. **"Invalid admin key" errors**
   - Verify key is active in Unkey dashboard
   - Check key hasn't expired
   - Ensure correct API ID is being used

3. **UI can't connect to API**
   - Verify `API_BASE_URL` is correctly set
   - Check API service is running and accessible
   - Verify network connectivity between services

### Debug Commands

```bash
# Check if admin key is loaded in API container
kubectl exec -it api-pod -- env | grep ADMIN_API_KEY

# Check if UI has correct API URL
kubectl exec -it ui-pod -- env | grep API_BASE_URL

# Test admin key validity
curl -X GET "https://your-api-url/clients" \
  -H "admin-key: your-admin-key"
```

## Testing

### Local Testing
```bash
# Set environment variables
export ADMIN_API_KEY="rfq_admin_test_key"
export API_BASE_URL="http://localhost:8000"

# Start services
agno ws up

# Test UI access
open http://localhost:8501
```

### Staging Testing
```bash
# Test staging API
curl -X GET "https://vanilla-steel-ai-api-stg-vs-data-439613.a.run.app/clients" \
  -H "admin-key: your-staging-admin-key"

# Test staging UI
open https://vanilla-steel-ai-ui-stg-vs-data-439613.a.run.app
```

## Next Steps

1. Generate admin API keys for each environment
2. Update Google Secret Manager secrets
3. Deploy to staging and test
4. Deploy to production
5. Document key rotation procedures
6. Set up monitoring and alerts
