# Function Reference

Complete reference of all functions, classes, and methods in Vanilla Steel AI system.

## 🏗️ Core Workflow Functions

### RFQProcessingWorkflow Class

**File**: `workflows/wf_rfq_processor.py`

#### Constructor
```python
def __init__(self, client_id: str, debug_mode: bool = False, performance_mode: bool = False)
```
**Purpose**: Initialize RFQ processing workflow
**Parameters**:
- `client_id` (str): Unique client identifier
- `debug_mode` (bool): Enable debug logging and detailed output
- `performance_mode` (bool): Enable performance optimizations

**Returns**: RFQProcessingWorkflow instance

#### Main Processing Method
```python
async def arun(self, rfq_text: str, email_id: str = None, sender_email: str = None) -> AsyncIterator[dict]
```
**Purpose**: Main async processing method for RFQ workflow
**Parameters**:
- `rfq_text` (str): Raw RFQ email content
- `email_id` (str, optional): Unique email identifier
- `sender_email` (str, optional): Sender email address

**Returns**: AsyncIterator[dict] - Processing results at each stage
**Yields**:
```python
{
    "stage": "extraction|validation|normalization|final",
    "status": "success|error",
    "data": {...},
    "metadata": {...}
}
```

#### Extraction with Retry
```python
async def _run_extraction_with_retry(self, rfq_text: str, session_id: str) -> dict
```
**Purpose**: Extract material specifications with retry mechanism
**Parameters**:
- `rfq_text` (str): Raw RFQ email content
- `session_id` (str): Processing session identifier

**Returns**: dict - Extracted material specifications
**Retry Logic**:
- MAX_EXTRACTION_RETRIES = 3
- EXTRACTION_RETRY_DELAY = 2.0 seconds
- Progressive delays: 2s, 4s, 6s
- Retries on: empty results, timeouts, API errors

**Example Return**:
```python
{
    "material_specs": [
        {
            "spec_id": "spec_001",
            "specification": {
                "grade": "DX51D",
                "coating": "ZE25/25",
                "thickness_min": 0.4,
                "weight_min": 20000
            }
        }
    ],
    "extraction_metadata": {
        "confidence": 0.95,
        "processing_time": 2.3,
        "retry_count": 1,
        "model_used": "claude-3-sonnet"
    }
}
```

#### Validation with Fallback
```python
async def _run_validation_with_fallback(self, extracted_data: dict) -> dict
```
**Purpose**: Validate extracted data with fallback mechanisms
**Parameters**:
- `extracted_data` (dict): Results from extraction stage

**Returns**: dict - Validated material specifications
**Fallback Strategy**:
1. Primary: AI-powered validation agent
2. Fallback: Rule-based validation

#### Normalization with Fallback
```python
async def _run_normalization_with_fallback(self, validated_data: dict) -> dict
```
**Purpose**: Normalize and standardize data with fallback mechanisms
**Parameters**:
- `validated_data` (dict): Results from validation stage

**Returns**: dict - Normalized material specifications
**Operations**:
- Unit standardization (tons→kg, inches→mm)
- Value formatting and range normalization
- Terminology standardization

#### Utility Methods
```python
def _generate_spec_ids(self, extracted_data: dict, session_id: str) -> dict
```
**Purpose**: Generate unique IDs for each material specification
**Parameters**:
- `extracted_data` (dict): Extracted specifications
- `session_id` (str): Processing session ID

**Returns**: dict - Data with generated spec IDs

```python
def _has_valid_specs(self, result: dict) -> bool
```
**Purpose**: Check if extraction result contains valid specifications
**Parameters**:
- `result` (dict): Extraction result to validate

**Returns**: bool - True if valid specifications found
**Validation Criteria**:
- material_specs array is not empty
- At least one spec has required fields
- Confidence score above threshold (0.3)

## 🤖 AI Agent Functions

### BaseAgent Class

**File**: `agents/base/base_agent.py`

#### Constructor
```python
def __init__(self, config: dict, model_id: str, user_id: str, debug_mode: bool = False)
```
**Purpose**: Initialize base AI agent
**Parameters**:
- `config` (dict): Agent configuration
- `model_id` (str): AI model identifier
- `user_id` (str): User/client identifier
- `debug_mode` (bool): Enable debug mode

#### Abstract Methods
```python
async def arun(self, input_data: str) -> AgentResponse
```
**Purpose**: Main agent processing method (must be implemented by subclasses)

```python
def _get_agent_goal(self) -> str
```
**Purpose**: Define agent's primary objective (must be implemented by subclasses)

```python
def _get_agent_instructions(self) -> str
```
**Purpose**: Get detailed processing instructions (must be implemented by subclasses)

#### Utility Methods
```python
def _get_default_temperature(self) -> float
```
**Purpose**: Get default temperature for AI model
**Returns**: float - Temperature value (0.0-1.0)

### RFQExtractorAgent Class

**File**: `agents/rfq_extractor.py`

#### Main Processing Method
```python
async def arun(self, rfq_text: str) -> AgentResponse
```
**Purpose**: Extract material specifications from RFQ text
**Parameters**:
- `rfq_text` (str): Raw RFQ email content

**Returns**: AgentResponse with extracted specifications
**Temperature**: 0.0 (precise, deterministic extraction)

#### Configuration Methods
```python
def _get_agent_goal(self) -> str
```
**Returns**: "Extract structured material specifications from RFQ emails with high accuracy"

```python
def _get_agent_instructions(self) -> str
```
**Returns**: Detailed extraction instructions based on client configuration

#### Factory Function
```python
def get_rfq_extractor_agent(config: dict, model_id: str, user_id: str, debug_mode: bool = False) -> RFQExtractorAgent
```
**Purpose**: Create RFQ extractor agent instance
**Parameters**: Same as constructor
**Returns**: RFQExtractorAgent instance

### RFQValidatorAgent Class

**File**: `agents/rfq_validator.py`

#### Main Processing Method
```python
async def arun(self, extracted_data: str) -> AgentResponse
```
**Purpose**: Validate extracted material specifications
**Parameters**:
- `extracted_data` (str): JSON string of extracted data

**Returns**: AgentResponse with validation results
**Temperature**: 0.1 (slightly higher for reasoning flexibility)

**Validation Includes**:
- Required field presence
- Data type validation
- Business rule compliance
- Cross-field consistency
- Confidence scoring

#### Factory Function
```python
def get_rfq_validator_agent(config: dict, model_id: str, user_id: str, debug_mode: bool = False) -> RFQValidatorAgent
```

### RFQNormalizerAgent Class

**File**: `agents/rfq_normalizer.py`

#### Main Processing Method
```python
async def arun(self, validated_data: str) -> AgentResponse
```
**Purpose**: Normalize and standardize material specifications
**Parameters**:
- `validated_data` (str): JSON string of validated data

**Returns**: AgentResponse with normalized specifications
**Temperature**: 0.0 (precise normalization)

**Normalization Includes**:
- Unit standardization
- Value formatting
- Terminology standardization
- Data structure consistency

#### Factory Function
```python
def get_rfq_normalizer_agent(config: dict, model_id: str, user_id: str, debug_mode: bool = False) -> RFQNormalizerAgent
```

## 🗄️ Database Tool Functions

### RFQDatabaseTools Class

**File**: `tools/rfq/database_tools.py`

#### Constructor
```python
def __init__(self, db_url: str)
```
**Purpose**: Initialize database tools with connection
**Parameters**:
- `db_url` (str): PostgreSQL connection URL

#### Client Configuration Methods
```python
def get_client_configuration(self, client_id: str) -> dict
```
**Purpose**: Retrieve client-specific configuration
**Parameters**:
- `client_id` (str): Client identifier

**Returns**: dict - Complete client configuration
```python
{
    "output_schema": {...},
    "extraction_rules": {...},
    "validation_rules": {...},
    "normalization_rules": {...},
    "formatter_output_config_id": "uuid"
}
```

```python
def update_client_configuration(self, client_id: str, config_data: dict) -> bool
```
**Purpose**: Update client configuration
**Parameters**:
- `client_id` (str): Client identifier
- `config_data` (dict): Configuration updates

**Returns**: bool - Success status

#### Session Management Methods
```python
def create_processing_session(self, client_id: str, email_data: dict) -> str
```
**Purpose**: Create new processing session
**Parameters**:
- `client_id` (str): Client identifier
- `email_data` (dict): Email information

**Returns**: str - Session ID

```python
def update_session_status(self, session_id: str, status: str, error_message: str = None) -> bool
```
**Purpose**: Update processing session status
**Parameters**:
- `session_id` (str): Session identifier
- `status` (str): New status (pending, processing, completed, failed, timeout)
- `error_message` (str, optional): Error details

**Returns**: bool - Success status

#### Result Storage Methods
```python
def save_extraction_result(self, session_id: str, extraction_data: dict) -> bool
```
**Purpose**: Save extraction results to database
**Parameters**:
- `session_id` (str): Session identifier
- `extraction_data` (dict): Extraction results

**Returns**: bool - Success status

```python
def save_validation_result(self, session_id: str, validation_data: dict) -> bool
```
**Purpose**: Save validation results to database

```python
def save_normalization_result(self, session_id: str, normalization_data: dict) -> bool
```
**Purpose**: Save normalization results to database

```python
def save_final_output(self, session_id: str, final_output: dict) -> bool
```
**Purpose**: Save final processing output to database

#### Query Methods
```python
def execute_with_retry(self, query: str, params: dict, max_retries: int = 3) -> Any
```
**Purpose**: Execute database query with retry mechanism
**Parameters**:
- `query` (str): SQL query
- `params` (dict): Query parameters
- `max_retries` (int): Maximum retry attempts

**Returns**: Any - Query results

```python
def get_processing_session(self, session_id: str) -> dict
```
**Purpose**: Retrieve complete processing session data
**Parameters**:
- `session_id` (str): Session identifier

**Returns**: dict - Complete session data with all results

#### Client Management Methods
```python
def get_client_flags(self, client_id: str) -> dict
```
**Purpose**: Get client feature flags
**Parameters**:
- `client_id` (str): Client identifier

**Returns**: dict - Client flags
```python
{
    "has_kb": bool,
    "has_instruction": bool,
    "has_output_preference": bool
}
```

```python
def validate_api_key(self, api_key: str) -> Optional[str]
```
**Purpose**: Validate API key and return client ID
**Parameters**:
- `api_key` (str): API key to validate

**Returns**: Optional[str] - Client ID if valid, None otherwise

#### Dynamic Instruction Methods
```python
def generate_dynamic_instructions(self, client_id: str, instruction_type: str) -> str
```
**Purpose**: Generate dynamic instructions based on client configuration
**Parameters**:
- `client_id` (str): Client identifier
- `instruction_type` (str): Type of instructions (extraction, validation, normalization)

**Returns**: str - Generated instructions

## 🔧 Utility Functions

### Logging Functions

**File**: `utils/log.py`

```python
def get_app_logger(name: str) -> logging.Logger
```
**Purpose**: Get configured application logger
**Parameters**:
- `name` (str): Logger name (usually __name__)

**Returns**: logging.Logger - Configured logger instance

```python
def setup_logging(level: str = "INFO", format_type: str = "json") -> None
```
**Purpose**: Setup application logging configuration
**Parameters**:
- `level` (str): Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
- `format_type` (str): Log format (json, text)

### Monitoring Functions

**File**: `utils/monitoring.py`

```python
def monitor_performance(operation_name: str)
```
**Purpose**: Decorator for monitoring function performance
**Parameters**:
- `operation_name` (str): Name of operation being monitored

**Usage**:
```python
@monitor_performance("rfq_processing")
async def process_rfq(data):
    # Function implementation
    pass
```

```python
def log_request_metrics(request: Request, response: Response, processing_time: float) -> None
```
**Purpose**: Log request metrics for monitoring
**Parameters**:
- `request` (Request): FastAPI request object
- `response` (Response): FastAPI response object
- `processing_time` (float): Processing time in seconds

### Exception Functions

**File**: `utils/exceptions/rfq_exceptions.py`

```python
class ExtractionError(Exception)
```
**Purpose**: Exception for extraction failures

```python
class ValidationError(Exception)
```
**Purpose**: Exception for validation failures

```python
class NormalizationError(Exception)
```
**Purpose**: Exception for normalization failures

```python
class ConfigurationError(Exception)
```
**Purpose**: Exception for configuration issues

## 🌐 API Route Functions

### RFQ Routes

**File**: `api/routes/rfq.py`

```python
async def process_rfq(client_id: str, request: RFQProcessingRequest, api_key: str = Depends(get_api_key)) -> RFQProcessingResponse
```
**Purpose**: Main RFQ processing endpoint
**HTTP Method**: POST
**Path**: `/v1/rfq/{client_id}/process`

```python
async def get_processing_status(client_id: str, session_id: str, api_key: str = Depends(get_api_key)) -> ProcessingStatusResponse
```
**Purpose**: Get processing status
**HTTP Method**: GET
**Path**: `/v1/rfq/{client_id}/status/{session_id}`

```python
async def get_client_schema(client_id: str, api_key: str = Depends(get_api_key)) -> ClientSchemaResponse
```
**Purpose**: Get client schema configuration
**HTTP Method**: GET
**Path**: `/v1/rfq/{client_id}/schema`

### Status Routes

**File**: `api/routes/status.py`

```python
async def health_check() -> dict
```
**Purpose**: System health check
**HTTP Method**: GET
**Path**: `/v1/health`

**Returns**:
```python
{
    "status": "success",
    "router": "status",
    "path": "/health",
    "utc": "2024-12-21T10:30:00Z",
    "environment": "production",
    "version": "1.0.0"
}
```

```python
async def get_config() -> dict
```
**Purpose**: Get system configuration
**HTTP Method**: GET
**Path**: `/v1/config`

## 🔐 Authentication Functions

**File**: `api/middleware/auth.py`

```python
async def get_api_key(request: Request) -> str
```
**Purpose**: Extract and validate API key from request
**Parameters**:
- `request` (Request): FastAPI request object

**Returns**: str - Validated API key
**Raises**: HTTPException(401) if invalid

```python
async def authenticate_client(api_key: str) -> str
```
**Purpose**: Authenticate client using API key
**Parameters**:
- `api_key` (str): API key to authenticate

**Returns**: str - Client ID
**Raises**: HTTPException(401) if authentication fails

This function reference provides complete documentation of all callable functions and methods in the Vanilla Steel AI system.
