# Configuration Reference

Complete reference for all configuration options, environment variables, and client-specific settings in Vanilla Steel AI.

## 🌍 Environment Variables

### Required Environment Variables

```bash
# API Keys (Required)
OPENROUTER_API_KEY=your_openrouter_api_key          # OpenRouter API access
AGNO_API_KEY=your_agno_api_key                      # Agno platform API key
UNKEY_API_KEY=your_unkey_api_key                    # Unkey authentication service
UNKEY_API_ID=your_unkey_api_id                      # Unkey API identifier
AGNO_WORKSPACE_ID=your_agno_workspace_id            # Agno workspace identifier

# Database Configuration (Required)
DB_HOST=localhost                                    # PostgreSQL host
DB_PORT=5432                                        # PostgreSQL port
DB_USER=postgres                                    # Database username
DB_PASS=your_secure_password                       # Database password
DB_DATABASE=vanilla_steel_ai                       # Database name
```

### Optional Environment Variables

```bash
# Application Environment
RUNTIME_ENV=dev                                     # Environment: dev|stg|prd|test
TESTING=False                                       # Enable testing mode
DEBUG=False                                         # Enable debug mode
DOCS_ENABLED=True                                   # Enable API documentation

# Database Options
POSTGRES_INIT_SCHEMA=public                        # Default schema name
DB_POOL_SIZE=5                                      # Connection pool size
DB_MAX_OVERFLOW=15                                  # Max overflow connections
DB_POOL_TIMEOUT=30                                  # Connection timeout (seconds)
DB_POOL_RECYCLE=3600                               # Connection recycle time (seconds)

# Application Settings
APP_TITLE=vanilla-steel-ai                         # Application title
APP_VERSION=1.0                                     # Application version
CORS_ORIGINS=http://localhost:3000,http://localhost:8080  # CORS allowed origins

# AI/ML Configuration
DEFAULT_MODEL_ID=claude-3-sonnet-20240229          # Default AI model
MODEL_TEMPERATURE=0.0                              # Default model temperature
MODEL_MAX_TOKENS=4000                              # Maximum tokens per request
MODEL_TIMEOUT=30                                   # Model request timeout (seconds)

# Processing Configuration
MAX_EXTRACTION_RETRIES=3                           # Maximum extraction retry attempts
EXTRACTION_RETRY_DELAY=2.0                        # Base retry delay (seconds)
EXTRACTION_TIMEOUT=30.0                           # Extraction timeout (seconds)
PROCESSING_TIMEOUT=300                             # Overall processing timeout (seconds)

# Monitoring and Logging
LOG_LEVEL=INFO                                     # Logging level: DEBUG|INFO|WARNING|ERROR|CRITICAL
LOG_FORMAT=json                                    # Log format: json|text
ENABLE_TELEMETRY=false                            # Enable telemetry collection
METRICS_ENABLED=true                              # Enable metrics collection

# Security
API_KEY_HEADER=api-key                            # API key header name
RATE_LIMIT_PER_MINUTE=100                         # Default rate limit per minute
ENABLE_CORS=true                                  # Enable CORS middleware
CORS_ALLOW_CREDENTIALS=false                      # Allow credentials in CORS

# Optional Services
EXA_API_KEY=your_exa_api_key                      # Exa search API key (optional)
REDIS_URL=redis://localhost:6379                 # Redis cache URL (optional)
```

## ⚙️ Application Settings Class

**File**: `api/settings.py`

```python
from pydantic_settings import BaseSettings
from typing import List, Optional
from enum import Enum

class EnvironmentType(str, Enum):
    DEV = "dev"
    STG = "stg"
    PRD = "prd"
    TEST = "test"

class ApiSettings(BaseSettings):
    """Application configuration settings."""

    # Environment
    runtime_env: EnvironmentType = EnvironmentType.DEV
    testing: bool = False
    debug: bool = False
    docs_enabled: bool = True

    # API Keys
    openrouter_api_key: str
    agno_api_key: str
    unkey_api_key: str
    unkey_api_id: str
    agno_workspace_id: str
    agno_enable_telemetry: str = "false"
    exa_api_key: Optional[str] = None

    # Database
    db_host: str
    db_port: int = 5432
    db_user: str
    db_pass: str
    db_database: str
    postgres_init_schema: str = "public"
    db_pool_size: int = 5
    db_max_overflow: int = 15
    db_pool_timeout: int = 30
    db_pool_recycle: int = 3600

    # Application
    app_title: str = "vanilla-steel-ai"
    app_version: str = "1.0"
    cors_origins: List[str] = ["http://localhost:3000"]
    cors_allow_credentials: bool = False
    enable_cors: bool = True

    # AI/ML
    default_model_id: str = "claude-3-sonnet-20240229"
    model_temperature: float = 0.0
    model_max_tokens: int = 4000
    model_timeout: int = 30

    # Processing
    max_extraction_retries: int = 3
    extraction_retry_delay: float = 2.0
    extraction_timeout: float = 30.0
    processing_timeout: int = 300

    # Security
    api_key_header: str = "api-key"
    rate_limit_per_minute: int = 100

    # Monitoring
    log_level: str = "INFO"
    log_format: str = "json"
    enable_telemetry: bool = False
    metrics_enabled: bool = True

    # Optional
    redis_url: Optional[str] = None

    class Config:
        env_file = ".env"
        case_sensitive = True

    @property
    def database_url(self) -> str:
        """Construct database URL from components."""
        return f"postgresql://{self.db_user}:{self.db_pass}@{self.db_host}:{self.db_port}/{self.db_database}"

    @property
    def is_development(self) -> bool:
        """Check if running in development mode."""
        return self.runtime_env == EnvironmentType.DEV

    @property
    def is_production(self) -> bool:
        """Check if running in production mode."""
        return self.runtime_env == EnvironmentType.PRD
```

## 🏢 Client Configuration Schema

### Database Storage

Client configurations are stored in the `rfq_client_configurations` table with the following JSONB structure:

### Output Schema Configuration

```json
{
  "output_schema": {
    "version": "1.0",
    "fields": {
      "grade": {
        "type": "string",
        "required": true,
        "description": "Steel grade specification",
        "validation": {
          "pattern": "[A-Z][0-9]{2}[A-Z]?[0-9]?",
          "max_length": 20,
          "min_length": 3
        },
        "examples": ["DX51D", "S355", "DC01"]
      },
      "coating": {
        "type": "string",
        "required": false,
        "description": "Coating specification",
        "validation": {
          "pattern": "Z[A-Z]?[0-9]{2,3}(/[0-9]{2,3})?",
          "allowed_values": ["ZE25/25", "ZE50/50", "ZE75/75", "ZM140", "ZM275"]
        },
        "examples": ["ZE25/25", "ZM140"]
      },
      "thickness_min": {
        "type": "number",
        "required": false,
        "description": "Minimum thickness in millimeters",
        "unit": "mm",
        "validation": {
          "min": 0.1,
          "max": 50.0,
          "decimal_places": 2
        }
      },
      "thickness_max": {
        "type": "number",
        "required": false,
        "description": "Maximum thickness in millimeters",
        "unit": "mm",
        "validation": {
          "min": 0.1,
          "max": 50.0,
          "decimal_places": 2
        }
      },
      "weight_min": {
        "type": "number",
        "required": false,
        "description": "Minimum weight in kilograms",
        "unit": "kg",
        "validation": {
          "min": 100,
          "max": 100000,
          "decimal_places": 0
        }
      },
      "weight_max": {
        "type": "number",
        "required": false,
        "description": "Maximum weight in kilograms",
        "unit": "kg",
        "validation": {
          "min": 100,
          "max": 100000,
          "decimal_places": 0
        }
      },
      "surface": {
        "type": "string",
        "required": false,
        "description": "Surface finish specification",
        "validation": {
          "allowed_values": ["AC", "FC", "SC", "MC", "DC"]
        }
      },
      "form": {
        "type": "string",
        "required": false,
        "description": "Product form",
        "validation": {
          "allowed_values": ["Coils", "Sheets", "Plates", "Strips"]
        }
      },
      "quantity": {
        "type": "number",
        "required": false,
        "description": "Quantity required",
        "unit": "pieces",
        "validation": {
          "min": 1,
          "max": 10000,
          "decimal_places": 0
        }
      }
    },
    "required_fields": ["grade"],
    "optional_fields": ["coating", "thickness_min", "thickness_max", "weight_min", "weight_max", "surface", "form", "quantity"]
  }
}
```

### Extraction Rules Configuration

```json
{
  "extraction_rules": {
    "patterns": {
      "grade": {
        "regex": "[A-Z][0-9]{2}[A-Z]?[0-9]?",
        "context_keywords": ["grade", "steel", "material", "spec"],
        "examples": ["DX51D", "S355", "DC01"]
      },
      "coating": {
        "regex": "Z[A-Z]?[0-9]{2,3}(/[0-9]{2,3})?",
        "context_keywords": ["coating", "galvanized", "zinc"],
        "examples": ["ZE25/25", "ZM140"]
      },
      "thickness": {
        "regex": "\\d+\\.?\\d*\\s*(mm|millimeter|inch|in)",
        "context_keywords": ["thickness", "thick", "gauge"],
        "unit_conversions": {
          "inch": 25.4,
          "in": 25.4,
          "mm": 1.0
        }
      },
      "weight": {
        "regex": "\\d+\\.?\\d*\\s*(tons?|tonnes?|kg|kilogram)",
        "context_keywords": ["weight", "ton", "tonne", "kg"],
        "unit_conversions": {
          "ton": 1000,
          "tonne": 1000,
          "tons": 1000,
          "tonnes": 1000,
          "kg": 1,
          "kilogram": 1
        }
      }
    },
    "context_analysis": {
      "section_keywords": ["specification", "requirements", "material", "steel"],
      "ignore_sections": ["delivery", "payment", "terms", "conditions"],
      "table_detection": true,
      "list_detection": true
    },
    "confidence_thresholds": {
      "minimum_confidence": 0.3,
      "high_confidence": 0.8,
      "extraction_retry_threshold": 0.5
    },
    "retry_configuration": {
      "max_retries": 3,
      "retry_delay": 2.0,
      "progressive_delay": true,
      "retry_on_empty": true,
      "retry_on_low_confidence": true
    }
  }
}
```

### Validation Rules Configuration

```json
{
  "validation_rules": {
    "required_fields": ["grade"],
    "field_validations": {
      "grade": {
        "type": "string",
        "pattern": "[A-Z][0-9]{2}[A-Z]?[0-9]?",
        "min_length": 3,
        "max_length": 20,
        "error_message": "Invalid steel grade format. Expected format: [Letter][2-3 digits][Optional letter][Optional digit]"
      },
      "coating": {
        "type": "string",
        "pattern": "Z[A-Z]?[0-9]{2,3}(/[0-9]{2,3})?",
        "allowed_values": ["ZE25/25", "ZE50/50", "ZE75/75", "ZM140", "ZM275"],
        "error_message": "Invalid coating specification"
      },
      "thickness_min": {
        "type": "number",
        "min": 0.1,
        "max": 50.0,
        "error_message": "Thickness must be between 0.1mm and 50.0mm"
      },
      "thickness_max": {
        "type": "number",
        "min": 0.1,
        "max": 50.0,
        "error_message": "Thickness must be between 0.1mm and 50.0mm"
      },
      "weight_min": {
        "type": "number",
        "min": 100,
        "max": 100000,
        "error_message": "Weight must be between 100kg and 100,000kg"
      },
      "weight_max": {
        "type": "number",
        "min": 100,
        "max": 100000,
        "error_message": "Weight must be between 100kg and 100,000kg"
      }
    },
    "cross_field_validations": [
      {
        "condition": "thickness_min > thickness_max",
        "error_message": "Minimum thickness cannot exceed maximum thickness",
        "severity": "error"
      },
      {
        "condition": "weight_min > weight_max",
        "error_message": "Minimum weight cannot exceed maximum weight",
        "severity": "error"
      },
      {
        "condition": "thickness_min == thickness_max AND thickness_min IS NOT NULL",
        "error_message": "Consider using single thickness value instead of range",
        "severity": "warning"
      }
    ],
    "business_rules": [
      {
        "rule": "grade_coating_compatibility",
        "description": "Validate grade and coating compatibility",
        "implementation": "check_grade_coating_matrix"
      },
      {
        "rule": "thickness_weight_relationship",
        "description": "Validate thickness and weight relationship makes sense",
        "implementation": "check_thickness_weight_logic"
      }
    ],
    "confidence_scoring": {
      "base_confidence": 0.5,
      "field_confidence_weights": {
        "grade": 0.3,
        "coating": 0.2,
        "thickness": 0.2,
        "weight": 0.2,
        "other": 0.1
      },
      "validation_pass_bonus": 0.1,
      "cross_validation_bonus": 0.05
    }
  }
}
```

### Normalization Rules Configuration

```json
{
  "normalization_rules": {
    "unit_conversions": {
      "weight": {
        "target_unit": "kg",
        "conversions": {
          "ton": 1000,
          "tonne": 1000,
          "tons": 1000,
          "tonnes": 1000,
          "t": 1000,
          "kg": 1,
          "kilogram": 1,
          "kilograms": 1,
          "lb": 0.453592,
          "lbs": 0.453592,
          "pound": 0.453592,
          "pounds": 0.453592
        }
      },
      "thickness": {
        "target_unit": "mm",
        "conversions": {
          "mm": 1,
          "millimeter": 1,
          "millimeters": 1,
          "cm": 10,
          "centimeter": 10,
          "centimeters": 10,
          "inch": 25.4,
          "inches": 25.4,
          "in": 25.4,
          "mil": 0.0254,
          "mils": 0.0254
        }
      },
      "length": {
        "target_unit": "mm",
        "conversions": {
          "mm": 1,
          "cm": 10,
          "m": 1000,
          "meter": 1000,
          "meters": 1000,
          "inch": 25.4,
          "inches": 25.4,
          "in": 25.4,
          "ft": 304.8,
          "feet": 304.8,
          "foot": 304.8
        }
      }
    },
    "value_standardization": {
      "decimal_places": {
        "thickness": 2,
        "weight": 0,
        "length": 1,
        "width": 1,
        "quantity": 0
      },
      "range_formatting": {
        "separator": " - ",
        "single_value_threshold": 0.001
      },
      "number_formatting": {
        "thousands_separator": ",",
        "decimal_separator": ".",
        "remove_trailing_zeros": true
      }
    },
    "terminology_mapping": {
      "grade_synonyms": {
        "galvanized": "ZE coating",
        "hot dip": "ZE coating",
        "electro galvanized": "ZE coating",
        "zinc coated": "ZE coating"
      },
      "form_standardization": {
        "coil": "Coils",
        "coils": "Coils",
        "sheet": "Sheets",
        "sheets": "Sheets",
        "plate": "Plates",
        "plates": "Plates",
        "strip": "Strips",
        "strips": "Strips"
      },
      "surface_standardization": {
        "as cold rolled": "AC",
        "cold rolled": "AC",
        "full hard": "FC",
        "skin passed": "SC",
        "matt": "MC",
        "dull": "DC"
      }
    },
    "data_cleaning": {
      "remove_extra_whitespace": true,
      "standardize_case": {
        "grade": "uppercase",
        "coating": "uppercase",
        "surface": "uppercase",
        "form": "title_case"
      },
      "remove_special_characters": {
        "grade": false,
        "coating": false,
        "numeric_fields": true
      }
    }
  }
}
```

## 🔧 Runtime Configuration Management

### Configuration Loading Priority

1. **Environment Variables** (.env file)
2. **Database Client Configuration** (rfq_client_configurations table)
3. **Default System Configuration** (hardcoded defaults)
4. **Runtime Parameter Overrides** (API request parameters)

### Configuration Update API

```python
# Update client configuration via API
PUT /v1/rfq/{client_id}/config
Content-Type: application/json

{
  "extraction_rules": {
    "patterns": {
      "grade": {
        "regex": "[A-Z][0-9]{2}[A-Z]?[0-9]?",
        "context_keywords": ["grade", "steel"]
      }
    }
  },
  "validation_rules": {
    "required_fields": ["grade", "quantity"]
  }
}
```

### Configuration Validation

All configuration updates are validated against a JSON schema to ensure:
- Required fields are present
- Data types are correct
- Regex patterns are valid
- Numeric ranges are logical
- Cross-field dependencies are satisfied

This configuration system provides complete flexibility for customizing the RFQ processing behavior per client while maintaining system integrity and performance.
