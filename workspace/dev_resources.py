# workspace/dev_resources.py - FIXED VERSION

import os

from agno.docker.app.fastapi import FastApi
from agno.docker.app.postgres import PgVectorDb
from agno.docker.app.streamlit import Streamlit
from agno.docker.resource.image import DockerImage
from agno.docker.resources import DockerResources
from dotenv import load_dotenv

from workspace.settings import ws_settings

# Load environment variables from .env file
# This allows you to manage sensitive information like database credentials
# without hardcoding them in your codebase.
load_dotenv()

# Get all environment variables as a dictionary
env_vars = dict(os.environ)

# -*- Dev image
dev_image = DockerImage(
    name=f"{ws_settings.image_repo}/{ws_settings.image_name}",
    tag=ws_settings.dev_env,
    enabled=ws_settings.build_images,
    path=str(ws_settings.ws_root),
    push_image=False,
)

# -*- Dev database -*-
# Use environment variables for database configuration
dev_db = PgVectorDb(
    name=f"{ws_settings.ws_name}-db",
    pg_user=env_vars.get("DB_USER"),
    pg_password=env_vars.get("DB_PASS"),
    pg_database=env_vars.get("DB_DATABASE"),
    host_port=env_vars.get("DB_PORT"),  # Default to 5432 if not set
    container_port=5432,
    host=env_vars.get("DB_HOST"),  # Default to localhost if not set
    enabled=True,
    volumes=["vanilla-steel-ai-pgdata:/var/lib/postgresql/data"],
    env_vars={
        "POSTGRES_USER": env_vars.get("DB_USER"),
        "POSTGRES_PASSWORD": env_vars.get("DB_PASS"),
        "POSTGRES_DB": env_vars.get("DB_NAME"),
        "POSTGRES_INITDB_ARGS": "--encoding=UTF8 --locale=C",
    },
)

# -*- Container environment - UPDATED
api_env = {
    "RUNTIME_ENV": env_vars.get("RUNTIME_ENV"),  # Default to 'dev' if not set
    # Database configuration
    "DB_HOST": dev_db.name,
    "DB_PORT": str(dev_db.container_port),
    "DB_USER": dev_db.pg_user,
    "DB_PASS": dev_db.pg_password,
    "DB_DATABASE": dev_db.pg_database,
    "POSTGRES_INIT_SCHEMA": env_vars.get("POSTGRES_INIT_SCHEMA"),
    # Wait for database to be available before starting the application
    "WAIT_FOR_DB": "true",
    # Migrate database on startup using alembic
    "MIGRATE_DB": "true",
    # REMOVED: "FORCE_DB_INIT": "true",  # Let PostgreSQL container handle this
    "UNKEY_API_KEY": env_vars.get("UNKEY_API_KEY"),
    "UNKEY_API_ID": env_vars.get("UNKEY_API_ID"),
    "OPENROUTER_API_KEY": env_vars.get("OPENROUTER_API_KEY"),
    "EXA_API_KEY": env_vars.get("EXA_API_KEY"),
    "AGNO_API_KEY": env_vars.get("AGNO_API_KEY"),
    "AGNO_WORKSPACE_ID": env_vars.get("AGNO_WORKSPACE_ID"),
    "AGNO_ENABLE_TELEMETRY": env_vars.get("AGNO_ENABLE_TELEMETRY"),  # Default to 'true' if not set
    "ADMIN_API_KEY": env_vars.get("ADMIN_API_KEY"),  # Admin API key for client management
    "API_TIMEOUT": env_vars.get("API_TIMEOUT"),
    "DEBUG_MODE": env_vars.get("DEBUG_MODE"),  # Default to 'true' if not set
    "PERFORMANCE_MODE": env_vars.get("PERFORMANCE_MODE"),  # Default to 'false' if not set
}

# -*- UI Container environment (NO MIGRATIONS)
ui_env = {
    "RUNTIME_ENV": env_vars.get("RUNTIME_ENV"),  # Default to 'dev' if not set
    "DB_HOST": dev_db.name,
    "DB_PORT": str(dev_db.container_port),
    "DB_USER": dev_db.pg_user,
    "DB_PASS": dev_db.pg_password,
    "DB_DATABASE": dev_db.pg_database,
    "WAIT_FOR_DB": "true",
    "MIGRATE_DB": "false",  # UI container does NOT run migrations
    "UNKEY_API_KEY": env_vars.get("UNKEY_API_KEY"),
    "UNKEY_API_ID": env_vars.get("UNKEY_API_ID"),
    "ADMIN_API_KEY": env_vars.get("ADMIN_API_KEY"),  # Admin API key for UI client management
    "API_BASE_URL": "http://vanilla-steel-ai-api:8000",  # Internal Docker network URL
}

# -*- Streamlit running on port 8501:8501
dev_streamlit = Streamlit(
    name=f"{ws_settings.ws_name}-ui",
    image=dev_image,
    command="streamlit run ui/Home.py",
    port_number=8501,
    debug_mode=True,
    mount_workspace=True,
    streamlit_server_headless=True,
    env_vars=ui_env,
    use_cache=True,
    # secrets_file=ws_settings.ws_root.joinpath("workspace/secrets/dev_app_secrets.yml"),
    depends_on=[dev_db] if dev_db.enabled else [],
)

# -*- FastApi running on port 8000:8000
dev_fastapi = FastApi(
    name=f"{ws_settings.ws_name}-api",
    image=dev_image,
    command="uvicorn api.main:app --reload --host 0.0.0.0",
    port_number=8000,
    debug_mode=True,
    mount_workspace=True,
    env_vars=api_env,
    use_cache=True,
    # secrets_file=ws_settings.ws_root.joinpath("workspace/secrets/dev_app_secrets.yml"),
    depends_on=[dev_db] if dev_db.enabled else [],
)

# -*- Dev DockerResources
dev_docker_resources = DockerResources(
    env=ws_settings.dev_env,
    network=ws_settings.ws_name,
    apps=([dev_db, dev_streamlit, dev_fastapi] if dev_db.enabled else [dev_streamlit, dev_fastapi]),
)
