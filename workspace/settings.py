from pathlib import Path

from agno.workspace.settings import WorkspaceSettings

#
# We define workspace settings using a WorkspaceSettings object
# these values can also be set using environment variables
# Import them into your project using `from workspace.settings import ws_settings`
#
ws_settings = WorkspaceSettings(
    # Workspace name
    ws_name="vanilla-steel-ai",
    # Path to the workspace root
    ws_root=Path(__file__).parent.parent.resolve(),
    # -*- Workspace Environments
    dev_env="dev",
    prd_env="prd",
    stg_env="stg",
    # default env for `agno ws` commands
    default_env="dev",
    default_infra="docker",
    # -*- Image Settings
    # Repository for images
    image_repo="europe-west1-docker.pkg.dev/vs-data-439613/vanilla-steel-ai",
    # 'Name:tag' for the image
    image_name="vanilla-steel-ai",
    # Build images locally
    build_images=True,
    # Push images to the registry
    push_images=True,
    # Skip cache when building images
    skip_image_cache=False,
    # Force pull images in FROM
    force_pull_images=False,
)
