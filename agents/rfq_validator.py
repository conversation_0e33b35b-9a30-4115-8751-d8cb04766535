"""RFQ Validation Agent for validating and correcting steel material specifications.

This module provides the main validation agent that processes both original
email text and extracted RFQ specifications to validate extraction accuracy,
correct misinterpretations, and validate against reference catalogs.
"""

from textwrap import dedent
from typing import Any

from agents.base import BaseRFQAgent
from utils.exceptions import RFQExtractionError
from utils.log import get_app_logger
from utils.rfq.config import get_default_client_id

logger = get_app_logger()


class RFQValidatorAgent(BaseRFQAgent):
    """
    RFQ Validation and Correction Agent.

    This agent performs two-stage validation:
    1. Extraction Validation: Compares extracted specs with original email text,
       corrects misinterpretations and fills missing information
    2. Database Validation: Validates corrected specs against reference catalogs
       and business rules
    """

    def __init__(
        self,
        config: dict[str, Any],
        model_id: str = "google/gemini-flash-1.5",
        user_id: str | None = None,
        session_id: str | None = None,
        debug_mode: bool = False,
        confidence_threshold: float = 0.7,
        performance_mode: bool = False,
    ):
        """
        Initialize the RFQ Validation Agent.

        Args:
            config: Agent configuration dictionary
            model_id: LLM model ID to use
            user_id: Optional user/client ID for client-specific validation rules
            session_id: Optional session ID for conversation tracking
            debug_mode: Enable debug logging and detailed error messages
            confidence_threshold: Threshold below which fields are re-evaluated
            performance_mode: Enable performance optimizations
        """
        self.confidence_threshold = confidence_threshold

        # Store config for validation
        self._validate_config(config)
        self._config = config

        # Initialize base agent
        super().__init__(
            agent_type="validation",
            model_id=model_id,
            user_id=user_id,
            session_id=session_id,
            debug_mode=debug_mode,
            performance_mode=performance_mode,
        )

    def _validate_config(self, config: dict[str, Any]):
        """
        Validate the provided configuration.

        Args:
            config: Configuration to validate

        Raises:
            RFQExtractionError: If configuration is invalid
        """
        try:
            # Check for required validation output schema
            if "validation_output_schema" not in config:
                raise ValueError("Missing validation_output_schema in configuration")

            if "material_specs" not in config["validation_output_schema"]:
                raise ValueError("Missing material_specs in validation_output_schema")

            logger.info("✅ Successfully validated validation configuration")

        except Exception as e:
            logger.error(f"Configuration validation failed: {e}")
            raise RFQExtractionError("Invalid validation agent configuration", details={"error": str(e)}) from e

    def _get_agent_name(self) -> str:
        """Get the display name for this agent."""
        return "RFQ Validator & Corrector"

    def _get_agent_description(self) -> str:
        """Get the description for this agent."""
        return dedent("""
            You are an expert steel RFQ validation and correction specialist. You perform two-stage
            validation: first comparing extracted specifications with original email text to correct
            misinterpretations, then validating against reference catalogs. You use reasoning to
            understand tolerances, ranges, and steel industry context to improve extraction quality.
            You return validation results and corrected specifications using the schema format loaded
            dynamically from the database.
        """).strip()

    def _get_agent_instructions(self) -> str:
        """
        Get the instructions for this agent.

        Returns:
            Agent-specific instructions
        """
        try:
            has_instruction = self.client_flags.get("has_instruction", False)

            if has_instruction:
                # Use client-specific instructions
                logger.info("Generating client-specific validation instructions")
                validation_instructions = self.db_tools.generate_dynamic_instructions(config_json=self._config, instruction_type="validation")
                logger.info("Using client-specific validation instructions")
            else:
                # Use generic instructions
                logger.info("Generating generic validation instructions")
                generic_config = self.db_tools.get_conditional_client_configuration(get_default_client_id())
                validation_instructions = self.db_tools.generate_dynamic_instructions(config_json=generic_config, instruction_type="validation")
                logger.info("Using generic validation instructions")

            if not validation_instructions:
                raise ValueError("Generated validation instructions are empty")

            logger.info("✅ Added dynamic validation output schema to instructions")
            return validation_instructions

        except Exception as e:
            logger.warning(f"Failed to load dynamic validation instructions, using simplified version: {e}")

            # Simplified fallback instructions for better compatibility
            validation_output_schema = self._config.get("validation_output_schema", {})
            import json

            validation_instructions = f"""
You are an expert steel RFQ validation and correction specialist.

## TASK
Validate and correct extracted specifications by:
1. Comparing extracted data with original email text
2. Correcting misinterpretations and filling missing information
3. Validating against database catalogs and business rules
4. Providing confidence scores for corrections

## CONFIDENCE THRESHOLD
Re-evaluate any extracted field with confidence score below {self.confidence_threshold}

## OUTPUT SCHEMA
You MUST format your response according to this exact JSON schema:
```json
{json.dumps(validation_output_schema, indent=2)}
```

CRITICAL: Return ONLY valid JSON following this schema, no explanatory text, no markdown blocks.
"""
            return validation_instructions

    def _get_agent_goal(self) -> str:
        """Get the goal for this agent."""
        return (
            "Validate and correct steel RFQ specifications through two-stage analysis: "
            "extraction validation against original email text, then database validation against "
            "reference catalogs. Return corrected specifications using the dynamically loaded schema format."
        )

    def _get_default_temperature(self) -> float:
        """Get default temperature for validation agent."""
        return 0.1  # Slightly higher temperature for reasoning

    def _get_default_max_tokens(self) -> int:
        """Get default max tokens for validation agent."""
        return 8192  # Higher token limit for validation reasoning


def get_rfq_validator(
    config: dict[str, Any],
    model_id: str = "google/gemini-flash-1.5",
    user_id: str | None = None,
    session_id: str | None = None,
    debug_mode: bool = False,
    confidence_threshold: float = 0.7,
    performance_mode: bool = False,
) -> RFQValidatorAgent:
    """
    Create and return the RFQ Validation and Correction Agent.

    This agent performs two-stage validation:
    1. Extraction Validation: Compares extracted specs with original email text,
       corrects misinterpretations and fills missing information
    2. Database Validation: Validates corrected specs against reference catalogs
       and business rules

    Args:
        config: Agent configuration dictionary
        model_id: The LLM model ID to use (e.g., "google/gemini-flash-1.5")
        user_id: Optional user/client ID for client-specific validation rules
        session_id: Optional session ID for conversation tracking
        debug_mode: Enable debug logging and detailed error messages
        confidence_threshold: Threshold below which fields are re-evaluated (default: 0.7)
        performance_mode: Enable performance optimizations (disables thinking tools)

    Returns:
        RFQValidatorAgent: Configured RFQ validation and correction agent

    Example:
        >>> config = {"validation_output_schema": {"material_specs": []}}
        >>> agent = get_rfq_validator(config)
        >>> validation_prompt = f'''
        ... ## ORIGINAL EMAIL TEXT:
        ... {original_email}
        ... ## EXTRACTED RFQ DATA TO VALIDATE:
        ... {json.dumps(extracted_data, indent=2)}
        ... Please validate and correct...
        ... '''
        >>> result = await agent.arun(message=validation_prompt)
    """
    return RFQValidatorAgent(
        config=config,
        model_id=model_id,
        user_id=user_id,
        session_id=session_id,
        debug_mode=debug_mode,
        confidence_threshold=confidence_threshold,
        performance_mode=performance_mode,
    )
