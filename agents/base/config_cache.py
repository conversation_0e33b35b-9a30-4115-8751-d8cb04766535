"""
Configuration caching to improve performance across agents.
"""

import threading
import time
from typing import Any

from db.session import db_url
from tools.rfq.database_tools import RFQDatabaseTools
from utils.exceptions import ClientConfigurationError
from utils.log import get_app_logger

logger = get_app_logger()


class ConfigurationCache:
    """Thread-safe configuration cache for RFQ agents."""

    def __init__(self):
        self._cache: dict[str, Any] = {}
        self._db_tools_cache: dict[str, RFQDatabaseTools] = {}
        self._lock = threading.RLock()
        self._cache_ttl = 300  # 5 minutes TTL
        self._cache_timestamps: dict[str, float] = {}

    def get_client_config(self, client_id: str, force_refresh: bool = False) -> dict[str, Any]:
        """
        Get client configuration with caching.

        Args:
            client_id: Client ID to get configuration for
            force_refresh: Force refresh from database

        Returns:
            Client configuration dictionary

        Raises:
            ClientConfigurationError: If configuration cannot be loaded
        """
        cache_key = f"config_{client_id}"

        with self._lock:
            # Check if we need to refresh cache
            current_time = time.time()
            cache_time = self._cache_timestamps.get(cache_key, 0)
            is_expired = (current_time - cache_time) > self._cache_ttl

            if force_refresh or is_expired or cache_key not in self._cache:
                try:
                    logger.debug(f"Loading configuration for client: {client_id}")
                    db_tools = self._get_db_tools()
                    config = db_tools.get_conditional_client_configuration(client_id)

                    if not config:
                        raise ClientConfigurationError(f"No configuration found for client {client_id}")

                    # Apply schema key compatibility mapping
                    config = self._apply_schema_key_compatibility(config)

                    self._cache[cache_key] = config
                    self._cache_timestamps[cache_key] = current_time
                    logger.debug(f"Cached configuration for client: {client_id}")

                except Exception as e:
                    logger.error(f"Failed to load configuration for client {client_id}: {e}")
                    # Return cached config if available, otherwise raise
                    if cache_key in self._cache:
                        logger.warning(f"Using stale cache for client {client_id}")
                        return self._cache[cache_key]
                    raise ClientConfigurationError(f"Failed to load configuration for client {client_id}") from e

            return self._cache[cache_key]

    def get_db_tools(self) -> RFQDatabaseTools:
        """
        Get shared database tools instance.

        Returns:
            RFQDatabaseTools instance
        """
        return self._get_db_tools()

    def _get_db_tools(self) -> RFQDatabaseTools:
        """Get or create database tools instance with caching."""
        thread_id = threading.get_ident()

        with self._lock:
            if thread_id not in self._db_tools_cache:
                logger.debug(f"Creating new database tools for thread {thread_id}")
                self._db_tools_cache[thread_id] = RFQDatabaseTools(db_url)

            return self._db_tools_cache[thread_id]

    def invalidate_client_config(self, client_id: str):
        """
        Invalidate cached configuration for a client.

        Args:
            client_id: Client ID to invalidate
        """
        cache_key = f"config_{client_id}"

        with self._lock:
            self._cache.pop(cache_key, None)
            self._cache_timestamps.pop(cache_key, None)
            logger.debug(f"Invalidated cache for client: {client_id}")

    def _apply_schema_key_compatibility(self, config: dict[str, Any]) -> dict[str, Any]:
        """
        Apply schema key compatibility mapping to ensure agents get expected key names.

        This method maps database schema keys to the agent-expected keys for consistency.

        Args:
            config: Configuration dictionary from database

        Returns:
            Configuration with compatible schema keys
        """
        # Create a copy to avoid modifying the original
        compatible_config = config.copy()

        # Schema key mapping: database_key -> agent_expected_key
        schema_key_mappings = {
            # Normalizer schema keys
            "normalization_output_schema": "normalizer_output_schema",
            "normalization_rules": "normalizer_rules",
            # Formatter schema keys
            "formatting_output_schema": "formatter_output_schema",
            "formatting_rules": "formatter_rules",
        }

        # Apply mappings
        for db_key, agent_key in schema_key_mappings.items():
            if db_key in compatible_config:
                compatible_config[agent_key] = compatible_config[db_key]
                # Keep the original key for backward compatibility
                # Don't remove it in case other parts of the system still use it

        logger.debug("Applied schema key compatibility mappings: %s", list(schema_key_mappings.keys()))
        return compatible_config

    def clear_cache(self):
        """Clear all cached configurations."""
        with self._lock:
            self._cache.clear()
            self._cache_timestamps.clear()
            logger.debug("Cleared all configuration cache")

    def get_cache_stats(self) -> dict[str, Any]:
        """
        Get cache statistics.

        Returns:
            Dictionary with cache statistics
        """
        with self._lock:
            current_time = time.time()
            active_entries = 0
            expired_entries = 0

            for _cache_key, timestamp in self._cache_timestamps.items():
                if (current_time - timestamp) <= self._cache_ttl:
                    active_entries += 1
                else:
                    expired_entries += 1

            return {
                "total_entries": len(self._cache),
                "active_entries": active_entries,
                "expired_entries": expired_entries,
                "cache_ttl": self._cache_ttl,
                "db_tools_instances": len(self._db_tools_cache),
            }


# Global cache instance
config_cache = ConfigurationCache()
