"""
Base Agent Class for RFQ Processing Agents.

This module provides a base class that eliminates code duplication across
all RFQ processing agents (Extractor, Validator, Normalizer, Formatter).
"""

import functools
import os
import time
import traceback
from abc import ABC, abstractmethod
from typing import Any

from agno.agent import Agent
from agno.models.openrouter import OpenRouter
from agno.storage.agent.postgres import PostgresAgentStorage
from agno.tools.thinking import ThinkingTools

from db.session import db_url
from tools.rfq.database_tools import RFQDatabaseTools
from utils.exceptions import (
    AgnoServiceError,
    ClientConfigurationError,
    RFQProcessingError,
)
from utils.log import get_app_logger
from utils.rfq.config import get_default_client_id

from .config_cache import config_cache

logger = get_app_logger()


def agent_error_handler(fallback_response: Any | None = None, error_type: type[Exception] = RFQProcessingError):
    """
    Decorator for standardized agent error handling.

    Args:
        fallback_response: Response to return if operation fails
        error_type: Exception type to raise on error
    """

    def decorator(func):
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except TimeoutError as e:
                logger.error(f"Timeout in {func.__name__}: {e}")
                if fallback_response is not None:
                    return fallback_response
                raise error_type(f"Operation timed out: {e}") from e
            except Exception as e:
                logger.error(f"Error in {func.__name__}: {e}")
                logger.error(f"Traceback: {traceback.format_exc()}")
                if fallback_response is not None:
                    return fallback_response
                raise error_type(f"Operation failed: {e}") from e

        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                logger.error(f"Error in {func.__name__}: {e}")
                logger.error(f"Traceback: {traceback.format_exc()}")
                if fallback_response is not None:
                    return fallback_response
                raise error_type(f"Operation failed: {e}") from e

        # Return appropriate wrapper based on function type
        import asyncio

        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator


class BaseRFQAgent(ABC):
    """
    Base class for all RFQ processing agents.

    This class provides common functionality for:
    - Configuration loading and caching
    - Model initialization
    - Database tools management
    - Error handling
    - Performance monitoring
    """

    def __init__(
        self,
        agent_type: str,
        model_id: str = "google/gemini-flash-1.5",
        user_id: str | None = None,
        session_id: str | None = None,
        debug_mode: bool = False,
        performance_mode: bool = False,
        **kwargs,
    ):
        """
        Initialize base RFQ agent.

        Args:
            agent_type: Type of agent (extraction, validation, normalization, formatting)
            model_id: LLM model ID to use
            user_id: Optional user/client ID
            session_id: Optional session ID
            debug_mode: Enable debug logging
            performance_mode: Enable performance optimizations
            **kwargs: Additional agent-specific parameters
        """
        self.agent_type = agent_type
        self.model_id = model_id
        self.user_id = user_id
        self.session_id = session_id
        self.debug_mode = debug_mode
        self.performance_mode = performance_mode
        self.agent_id = f"rfq_{agent_type}"

        # Track initialization time
        start_time = time.time()

        logger.info(f"Initializing {agent_type} agent: model={model_id}, user_id={user_id}, performance_mode={performance_mode}")

        # Load configuration (cached)
        self.config = self._load_configuration()

        # Extract client flags
        self.client_flags = self.config.get(
            "_client_flags", {"has_kb": False, "has_instruction": False, "client_id": user_id or get_default_client_id()}
        )

        # Initialize shared components
        self.db_tools = self._get_db_tools()
        self.model = self._configure_model()
        self.agent = self._create_agent()

        # Log successful initialization
        init_time = (time.time() - start_time) * 1000
        logger.info(f"{agent_type.title()} agent initialized successfully in {init_time:.2f}ms")

    def _load_configuration(self) -> dict[str, Any]:
        """
        Load client-specific configuration with caching.

        Returns:
            Configuration dictionary

        Raises:
            ClientConfigurationError: If configuration cannot be loaded
        """
        try:
            client_id = self.user_id or get_default_client_id()
            return config_cache.get_client_config(client_id)
        except Exception as e:
            logger.error(f"Failed to load configuration for {self.agent_type} agent: {e}")
            raise ClientConfigurationError(
                f"Failed to load configuration for {self.agent_type} agent", details={"error": str(e), "client_id": self.user_id}
            ) from e

    def _get_db_tools(self) -> RFQDatabaseTools:
        """
        Get shared database tools instance.

        Returns:
            RFQDatabaseTools instance
        """
        return config_cache.get_db_tools()

    def _configure_model(self) -> OpenRouter:
        """
        Configure OpenRouter model with agent-specific preferences.

        Returns:
            Configured OpenRouter model

        Raises:
            AgnoServiceError: If model configuration fails
        """
        try:
            # Get agent-specific model preferences
            model_preferences = self.db_tools.get_agent_model_preferences(
                client_id=self.user_id or get_default_client_id(), agent_type=self.agent_type
            )

            # Use agent-specific model if not in performance mode
            if not self.performance_mode:
                self.model_id = model_preferences["model_id"]

            # Get API key
            openrouter_api_key = os.getenv("OPENROUTER_API_KEY")
            if not openrouter_api_key:
                raise ValueError("OPENROUTER_API_KEY environment variable not set")

            # Configure model with agent-specific settings
            temperature = model_preferences.get("temperature", self._get_default_temperature())
            max_tokens = model_preferences.get("max_tokens", self._get_default_max_tokens())

            model = OpenRouter(
                id=self.model_id,
                temperature=temperature,
                max_tokens=max_tokens,
                api_key=openrouter_api_key,
                response_format={"type": "json_object"},
                extra_headers={
                    "HTTP-Referer": "https://vanilla-steel-ai.com",
                    "X-Title": f"Steel RFQ {self.agent_type.title()}",
                },
            )

            logger.info(
                f"{self.agent_type.title()} model configured: {self.model_id} "
                f"(temp={temperature}, max_tokens={max_tokens}, source={model_preferences.get('source', 'unknown')})"
            )

            return model

        except Exception as e:
            logger.error(f"Failed to configure model for {self.agent_type} agent: {e}")
            raise AgnoServiceError(
                f"Failed to configure language model for {self.agent_type} agent", details={"error": str(e), "model_id": self.model_id}
            ) from e

    def _create_agent(self) -> Agent:
        """
        Create the Agno agent instance.

        Returns:
            Configured Agent instance

        Raises:
            AgnoServiceError: If agent creation fails
        """
        try:
            # Generate instructions
            instructions = self._get_agent_instructions()

            # Build additional context
            additional_context = self._build_additional_context()

            # Configure tools
            tools = self._configure_tools()

            # Configure storage (disabled in performance mode)
            storage = (
                None if self.performance_mode else PostgresAgentStorage(table_name=f"rfq_{self.agent_type}_sessions", db_url=db_url, schema="public")
            )

            agent = Agent(
                name=self._get_agent_name(),
                agent_id=self.agent_id,
                user_id=self.user_id,
                session_id=self.session_id,
                model=self.model,
                tools=tools,
                storage=storage,
                description=self._get_agent_description(),
                instructions=instructions,
                goal=self._get_agent_goal(),
                additional_context=additional_context,
                markdown=False,  # Disable markdown for JSON output
                add_datetime_to_instructions=not self.performance_mode,
                debug_mode=self.debug_mode and not self.performance_mode,
                show_tool_calls=self.debug_mode and not self.performance_mode,
            )

            logger.info(f"{self.agent_type.title()} agent created successfully")
            return agent

        except Exception as e:
            logger.error(f"Failed to create {self.agent_type} agent: {e}")
            raise AgnoServiceError(
                f"Failed to create {self.agent_type} agent",
                details={
                    "error": str(e),
                    "agent_id": self.agent_id,
                    "model_id": self.model_id,
                    "user_id": self.user_id,
                },
            ) from e

    def _configure_tools(self) -> list:
        """
        Configure tools for the agent.

        Returns:
            List of tools for the agent
        """
        tools = [self.db_tools]

        # Add thinking tools unless in performance mode
        if not self.performance_mode:
            tools.append(
                ThinkingTools(
                    think=True,
                    add_instructions=False,
                )
            )

        return tools

    def _build_additional_context(self) -> str:
        """
        Build additional context for the agent.

        Returns:
            Additional context string
        """
        if not self.user_id:
            return ""

        has_kb = self.client_flags.get("has_kb", False)
        has_instruction = self.client_flags.get("has_instruction", False)

        return f"""<context>
Processing request for client: {self.user_id}
Knowledge base source: {"client-specific" if has_kb else "generic"}
Instruction source: {"client-specific" if has_instruction else "generic"}
Output schema source: generic (internal format)
Agent type: {self.agent_type}
Performance mode: {self.performance_mode}
</context>"""

    # Abstract methods that subclasses must implement

    @abstractmethod
    def _get_agent_name(self) -> str:
        """Get the display name for this agent."""
        pass

    @abstractmethod
    def _get_agent_description(self) -> str:
        """Get the description for this agent."""
        pass

    @abstractmethod
    def _get_agent_instructions(self) -> str:
        """Get the instructions for this agent."""
        pass

    @abstractmethod
    def _get_agent_goal(self) -> str:
        """Get the goal for this agent."""
        pass

    def _get_default_temperature(self) -> float:
        """Get default temperature for this agent type."""
        return 0.0 if self.agent_type in ["extraction", "normalizer", "formatter"] else 0.1

    def _get_default_max_tokens(self) -> int:
        """Get default max tokens for this agent type."""
        defaults = {
            "extraction": 6144,
            "validation": 8192,
            "normalizer": 4096,
            "formatter": 4096,
        }
        return defaults.get(self.agent_type, 6144)

    # Common utility methods

    @agent_error_handler()
    async def arun(self, message: str, **kwargs) -> Any:
        """
        Run the agent asynchronously with standardized error handling.

        Args:
            message: Input message for the agent
            **kwargs: Additional arguments

        Returns:
            Agent response
        """
        return await self.agent.arun(message=message, **kwargs)

    def get_performance_metrics(self) -> dict[str, Any]:
        """
        Get performance metrics for this agent.

        Returns:
            Dictionary of performance metrics
        """
        return {
            "agent_type": self.agent_type,
            "model_id": self.model_id,
            "performance_mode": self.performance_mode,
            "user_id": self.user_id,
            "client_flags": self.client_flags,
        }

    def refresh_configuration(self, force: bool = False):
        """
        Refresh agent configuration from database.

        Args:
            force: Force refresh even if cache is valid
        """
        if self.user_id:
            config_cache.invalidate_client_config(self.user_id)

        # Reload configuration
        self.config = self._load_configuration()
        logger.info(f"Configuration refreshed for {self.agent_type} agent")
