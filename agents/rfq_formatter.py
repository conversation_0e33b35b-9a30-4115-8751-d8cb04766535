"""RFQ Formatter Agent for final output formatting according to client preferences.

This module provides the formatter agent that processes normalized specifications
and formats them according to client-specific output preferences and schemas.
"""

from textwrap import dedent
from typing import Any

from agents.base import BaseRFQAgent
from utils.exceptions import RFQProcessingError
from utils.log import get_app_logger
from utils.rfq.config import get_default_client_id

logger = get_app_logger()


class RFQFormatterAgent(BaseRFQAgent):
    """
    RFQ Formatter Agent for client-specific output formatting.

    This agent performs final formatting of normalized specifications:
    1. Schema Compliance: Ensures output adheres to client's schema requirements
    2. Field Mapping: Maps internal fields to client-specific field names
    3. Value Formatting: Applies client-specific formatting rules
    4. Output Structure: Organizes data according to client preferences
    """

    def __init__(
        self,
        config: dict[str, Any],
        model_id: str = "google/gemini-flash-1.5",
        user_id: str | None = None,
        session_id: str | None = None,
        debug_mode: bool = False,
        performance_mode: bool = False,
        format_type: str | None = None,
    ):
        """
        Initialize the RFQ Formatter Agent.

        Args:
            config: Agent configuration dictionary
            model_id: LLM model ID to use
            user_id: Optional user/client ID for client-specific formatting rules
            session_id: Optional session ID for conversation tracking
            debug_mode: Enable debug logging and detailed error messages
            performance_mode: Enable performance optimizations
            format_type: Output format type (minimal, standard, api, detailed).
                        If None, will be extracted from client configuration.
        """
        # Store config for validation and extract format_type
        self._validate_config(config, user_id)
        self._config = config

        # Extract format_type immediately after config validation
        self.format_type = self._extract_format_type_from_validated_config(format_type)

        # Initialize base agent
        super().__init__(
            agent_type="formatter",
            model_id=model_id,
            user_id=user_id,
            session_id=session_id,
            debug_mode=debug_mode,
            performance_mode=performance_mode,
        )

    def _validate_config(self, config: dict[str, Any], user_id: str | None = None):
        """
        Validate the provided configuration.

        Args:
            config: Configuration to validate
            user_id: User ID for database operations

        Raises:
            RFQProcessingError: If configuration is invalid
        """
        try:
            # Check for required formatter output schema
            if "formatter_output_schema" not in config:
                logger.warning("Missing formatter_output_schema in configuration, using default")
                # Load default schema from database
                formatter_schema = self.db_tools.get_formatter_output_schema(
                    client_id=self.user_id or get_default_client_id(), format_type=self.format_type
                )
                if formatter_schema:
                    config["formatter_output_schema"] = {self.format_type: formatter_schema}
                else:
                    # Ultimate fallback
                    from utils.rfq.schemas.formatter_schema import DEFAULT_FORMATTER_OUTPUT_SCHEMA

                    config["formatter_output_schema"] = DEFAULT_FORMATTER_OUTPUT_SCHEMA

            # NEW: Check for client output preferences (CLIENT_CONFIGURATION_SCHEMA)
            if "client_output_preferences" not in config:
                logger.info("Loading client output preferences from database")

                # Create temporary database tools instance for validation
                from db.settings import DbSettings
                from tools.rfq.database_tools import RFQDatabaseTools

                db_settings = DbSettings()
                temp_db_tools = RFQDatabaseTools(db_settings.get_db_url())

                output_prefs = temp_db_tools.get_client_output_preferences(client_id=user_id or get_default_client_id())
                if output_prefs:
                    config["client_output_preferences"] = output_prefs
                    logger.info(
                        f"✅ Loaded client output preferences: format_type={output_prefs.get('output_preferences', {}).get('format_type', 'unknown')}"
                    )
                else:
                    logger.warning("Could not load client output preferences, using default")
                    from utils.rfq.schemas.formatter_schema import CLIENT_CONFIGURATION_SCHEMA

                    config["client_output_preferences"] = CLIENT_CONFIGURATION_SCHEMA

            logger.info("✅ Successfully validated formatter configuration")

        except Exception as e:
            logger.error(f"Configuration validation failed: {e}")
            raise RFQProcessingError("Invalid formatter agent configuration", details={"error": str(e)}) from e

    def _extract_format_type_from_validated_config(self, format_type_param: str | None) -> str:
        """Extract format_type from validated client configuration."""
        try:
            # Check if format_type was provided as parameter
            if format_type_param is not None:
                logger.info(f"📋 Format type provided as parameter: {format_type_param}")
                return format_type_param

            # Extract from client_output_preferences (loaded during validation)
            client_prefs = self._config.get("client_output_preferences", {})
            output_prefs = client_prefs.get("output_preferences", {})
            extracted_format_type = output_prefs.get("format_type", "standard")

            logger.info(f"📋 Format type extracted from client config: {extracted_format_type}")
            return extracted_format_type

        except Exception as e:
            logger.warning(f"Failed to extract format_type from config: {e}, using default 'standard'")
            return "standard"

    def _get_agent_name(self) -> str:
        """Get the display name for this agent."""
        return "RFQ Output Formatter"

    def _get_agent_description(self) -> str:
        """Get the description for this agent."""
        return dedent("""
            You are an expert steel RFQ output formatter. You format normalized specifications
            into client-specific output formats while maintaining data integrity and applying
            client preferences for field names, value formats, and data organization. You ensure
            final output adheres exactly to the required schema and formatting standards.
        """).strip()

    def _get_agent_instructions(self) -> str:
        """
        Get the instructions for this agent.

        Returns:
            Agent-specific instructions
        """
        try:
            has_instruction = self.client_flags.get("has_instruction", False)
            has_output_preference = self.client_flags.get("has_output_preference", False)

            if has_instruction:
                # Use client-specific instructions
                logger.info("Generating client-specific formatter instructions")
                formatting_instructions = self.db_tools.generate_dynamic_instructions(config_json=self._config, instruction_type="formatter")
                logger.info("Using client-specific formatter instructions")
            else:
                # Use generic instructions
                logger.info("Generating generic formatter instructions")
                generic_config = self.db_tools.get_conditional_client_configuration(get_default_client_id())
                formatting_instructions = self.db_tools.generate_dynamic_instructions(config_json=generic_config, instruction_type="formatter")
                logger.info("Using generic formatter instructions")

            if not formatting_instructions:
                raise ValueError("Generated formatting instructions are empty")

            logger.info("✅ Added dynamic formatting output schema to instructions")
            return formatting_instructions

        except Exception as e:
            logger.warning(f"Failed to load dynamic formatting instructions, using simplified version: {e}")

            # Simplified fallback instructions
            formatter_output_schema = self._config.get("formatter_output_schema", {})
            format_schema = formatter_output_schema.get(self.format_type, {})
            import json

            schema_str = json.dumps(format_schema, indent=2) if format_schema else "Schema not available"

        return formatting_instructions

    def _get_agent_goal(self) -> str:
        """Get the goal for this agent."""
        return (
            f"Format normalized steel RFQ specifications into {self.format_type} output format "
            "according to client preferences while ensuring schema compliance and data integrity."
        )

    def _get_default_temperature(self) -> float:
        """Get default temperature for formatter agent."""
        return 0.0  # Low temperature for consistent formatting

    def _get_default_max_tokens(self) -> int:
        """Get default max tokens for formatter agent."""
        return 4096  # Moderate token limit for formatting

    def set_format_type(self, format_type: str):
        """Change the format type for this agent."""
        self.format_type = format_type
        logger.info(f"Format type changed to: {format_type}")


def get_rfq_formatter(
    config: dict[str, Any],
    model_id: str = "google/gemini-flash-1.5",
    user_id: str | None = None,
    session_id: str | None = None,
    debug_mode: bool = False,
    performance_mode: bool = False,
    format_type: str | None = None,
) -> RFQFormatterAgent:
    """
    Create and return the RFQ Formatter Agent.

    This agent performs final formatting of normalized specifications according
    to client-specific output preferences and schema requirements.

    Args:
        config: Agent configuration dictionary
        model_id: The LLM model ID to use (e.g., "google/gemini-flash-1.5")
        user_id: Optional user/client ID for client-specific formatting rules
        session_id: Optional session ID for conversation tracking
        debug_mode: Enable debug logging and detailed error messages
        performance_mode: Enable performance optimizations (disables thinking tools)
        format_type: Output format type (minimal, standard, api, detailed).
                    If None, will be extracted from client configuration.

    Returns:
        RFQFormatterAgent: Configured RFQ formatter agent

    Raises:
        RFQProcessingError: If agent creation fails

    Example:
        >>> config = {"formatter_output_schema": {"standard": {}}}
        >>> agent = get_rfq_formatter(config, format_type="standard")
        >>> formatting_prompt = f'''
        ... ## NORMALIZED SPECIFICATIONS TO FORMAT:
        ... {json.dumps(normalized_specs, indent=2)}
        ... Please format according to client preferences...
        ... '''
        >>> result = await agent.arun(message=formatting_prompt)
    """
    return RFQFormatterAgent(
        config=config,
        model_id=model_id,
        user_id=user_id,
        session_id=session_id,
        debug_mode=debug_mode,
        performance_mode=performance_mode,
        format_type=format_type,
    )
