"""RFQ Extraction Agent for processing steel material specifications.

This module provides the main extraction agent that processes Request for
Quotation (RFQ) emails and extracts structured material specifications.
"""

from textwrap import dedent
from typing import Any

from agents.base import BaseRFQAgent
from utils.exceptions import RFQExtractionError
from utils.log import get_app_logger
from utils.rfq.config import get_default_client_id

logger = get_app_logger()


class RFQExtractorAgent(BaseRFQAgent):
    """
    RFQ Extraction Agent for processing steel material specifications.

    This agent processes Request for Quotation (RFQ) emails and extracts
    structured material specifications using client-specific configurations.
    """

    def __init__(
        self,
        config: dict[str, Any],
        model_id: str = "google/gemini-flash-1.5",
        user_id: str | None = None,
        session_id: str | None = None,
        debug_mode: bool = False,
        performance_mode: bool = False,
    ):
        """
        Initialize the RFQ Extraction Agent.

        Args:
            config: Agent configuration dictionary
            model_id: LLM model ID to use
            user_id: Optional user/client ID for client-specific processing
            session_id: Optional session ID for conversation tracking
            debug_mode: Enable debug logging and detailed error messages
            performance_mode: Enable performance optimizations
        """
        # Store config for validation
        self._validate_config(config)
        self._config = config

        # Initialize base agent
        super().__init__(
            agent_type="extraction",
            model_id=model_id,
            user_id=user_id,
            session_id=session_id,
            debug_mode=debug_mode,
            performance_mode=performance_mode,
        )

    def _validate_config(self, config: dict[str, Any]):
        """
        Validate the provided configuration.

        Args:
            config: Configuration to validate

        Raises:
            RFQExtractionError: If configuration is invalid
        """
        try:
            # Check for required extraction output schema
            if "extraction_output_schema" not in config:
                raise ValueError("Missing extraction_output_schema in configuration")

            if "material_specs" not in config["extraction_output_schema"]:
                raise ValueError("Missing material_specs in extraction_output_schema")

            logger.info("✅ Successfully validated extraction configuration")

        except Exception as e:
            logger.error(f"Configuration validation failed: {e}")
            raise RFQExtractionError("Invalid extraction agent configuration", details={"error": str(e)}) from e

    def _get_agent_name(self) -> str:
        """Get the display name for this agent."""
        return "RFQ Extractor"

    def _get_agent_description(self) -> str:
        """Get the description for this agent."""
        return dedent("""
            You are an expert steel RFQ text extraction specialist. You extract material specifications from
            Request for Quotation (RFQ) emails and return them as JSON. The output schema is loaded
            dynamically from the database to ensure consistency across the system.
        """).strip()

    def _get_agent_instructions(self) -> str:
        """
        Get the instructions for this agent.

        Returns:
            Agent-specific instructions
        """
        try:
            has_instruction = self.client_flags.get("has_instruction", False)

            if has_instruction:
                # Use client-specific instructions
                instructions = self.db_tools.generate_dynamic_instructions(config_json=self._config, instruction_type="extraction")
                logger.info("Using client-specific extraction instructions")
            else:
                # Use generic instructions
                generic_config = self.db_tools.get_conditional_client_configuration(get_default_client_id())
                instructions = self.db_tools.generate_dynamic_instructions(config_json=generic_config, instruction_type="extraction")
                logger.info("Using generic extraction instructions")

            if not instructions:
                raise ValueError("Generated instructions are empty")

            logger.info("✅ Added dynamic extraction output schema to instructions")
            return instructions

        except Exception as e:
            logger.error(f"Failed to generate extraction instructions: {e}")
            raise RFQExtractionError("Failed to generate agent instructions", details={"error": str(e)}) from e

    def _get_agent_goal(self) -> str:
        """Get the goal for this agent."""
        return "Extract structured material specifications from unstructured RFQ emails using the dynamically loaded schema format."

    def _get_default_temperature(self) -> float:
        """Get default temperature for extraction agent."""
        return 0.0  # Low temperature for consistent extraction

    def _get_default_max_tokens(self) -> int:
        """Get default max tokens for extraction agent."""
        return 6144 if self.performance_mode else 8192


def get_rfq_extractor(
    config: dict[str, Any],
    model_id: str = "google/gemini-flash-1.5",
    user_id: str | None = None,
    session_id: str | None = None,
    debug_mode: bool = False,
    performance_mode: bool = False,
) -> RFQExtractorAgent:
    """
    Create and return the RFQ Extraction Agent.

    This agent processes Request for Quotation (RFQ) emails for steel materials,
    extracting structured specifications and returning them as JSON. It uses
    client-specific configurations when available and supports performance
    optimizations for high-throughput scenarios.

    Args:
        config: Agent configuration dictionary
        model_id: The LLM model ID to use (e.g., "google/gemini-flash-1.5")
        user_id: Optional user/client ID for client-specific processing
        session_id: Optional session ID for conversation tracking
        debug_mode: Enable debug logging and detailed error messages
        performance_mode: Enable performance optimizations:
            - Disables thinking tools for faster processing
            - Reduces token limits
            - Disables session storage
            - Streamlines logging

    Returns:
        RFQExtractorAgent: Configured RFQ extraction agent

    Raises:
        RFQExtractionError: If agent creation fails

    Example:
        >>> config = {"extraction_output_schema": {"material_specs": []}}
        >>> agent = get_rfq_extractor(config, model_id="google/gemini-flash-1.5", user_id="client-123", debug_mode=True)
        >>> response = await agent.arun("Extract specs from: 100 tons DX51D...")
    """
    return RFQExtractorAgent(
        config=config,
        model_id=model_id,
        user_id=user_id,
        session_id=session_id,
        debug_mode=debug_mode,
        performance_mode=performance_mode,
    )
