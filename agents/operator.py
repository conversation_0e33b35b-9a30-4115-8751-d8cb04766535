"""Agent operator module for managing different agent types.

This module provides a centralized interface for creating and managing
different types of agents in the Vanilla Steel AI system.
"""

from enum import Enum
from typing import Any

from agents.rfq_extractor import get_rfq_extractor
from agents.rfq_normalizer import get_rfq_normalizer
from agents.rfq_validator import get_rfq_validator
from agents.sage import get_sage
from agents.scholar import get_scholar
from utils.exceptions import VanillaSteelError
from utils.log import get_app_logger

# Initialize application logger
logger = get_app_logger()


class AgentType(Enum):
    """Enumeration of available agent types.

    Each agent type serves a specific purpose:
    - SAGE: General purpose assistant agent
    - SCHOLAR: Research and knowledge retrieval agent
    - RFQ_PROCESSOR: Specialized agent for RFQ extraction
    - RFQ_VALIDATOR: Specialized agent for RFQ validation and correction
    - RFQ_NORMALIZER: Specialized agent for numerical normalization
    """

    SAGE = "sage"
    SCHOLAR = "scholar"
    RFQ_PROCESSOR = "rfq_processor"
    RFQ_VALIDATOR = "rfq_validator"
    RFQ_NORMALIZER = "rfq_normalizer"
    RFQ_FORMATTER = "rfq_formatter"


class AgentNotFoundError(VanillaSteelError):
    """Raised when an unknown agent type is requested."""

    pass


def get_available_agents() -> list[str]:
    """
    Get a list of all available agent identifiers.

    Returns:
        list[str]: List of agent type values that can be used
                   to create agents

    Example:
        >>> agents = get_available_agents()
        >>> print(agents)
        ['sage', 'scholar', 'rfq_processor', 'rfq_validator', 'rfq_normalizer']
    """
    return [agent.value for agent in AgentType]


def get_agent(
    model_id: str = "gpt-4o",
    agent_id: AgentType | None = None,
    user_id: str | None = None,
    session_id: str | None = None,
    debug_mode: bool = True,
    performance_mode: bool = False,
):
    """
    Factory function to create an agent instance by type.

    This function serves as a centralized factory for creating different
    types of agents with consistent configuration options.

    Args:
        model_id: The language model identifier to use
                  (e.g., "gpt-4o", "claude-3", "google/gemini-flash-1.5")
        agent_id: The type of agent to create
        user_id: Optional user/client identifier for personalization
        session_id: Optional session identifier for conversation tracking
        debug_mode: Enable debug logging and detailed error messages
        performance_mode: Enable performance optimizations (for RFQ processor)

    Returns:
        Agent: An initialized agent instance of the requested type

    Raises:
        AgentNotFoundError: If an invalid agent_id is provided
        AgnoServiceError: If agent creation fails

    Example:
        >>> from agents.operator import get_agent, AgentType
        >>>
        >>> # Create an RFQ processor agent
        >>> agent = get_agent(agent_id=AgentType.RFQ_PROCESSOR, user_id="client-123", model_id="gpt-4o", debug_mode=True)
        >>>
        >>> # Process an RFQ
        >>> response = await agent.arun("Extract specs from: 100 tons DX51D...")
    """
    logger.info(f"Creating agent: type={agent_id.value if agent_id else 'scholar'}, model={model_id}, user_id={user_id}")

    try:
        if agent_id == AgentType.SAGE:
            return get_sage(
                model_id=model_id,
                user_id=user_id,
                session_id=session_id,
                debug_mode=debug_mode,
            )
        elif agent_id == AgentType.RFQ_PROCESSOR:
            # RFQ agents need config - use empty config as fallback
            processor_config: dict[str, Any] = {}  # TODO: Load proper config for agent operator
            return get_rfq_extractor(
                config=processor_config,
                model_id=model_id,
                user_id=user_id,
                session_id=session_id,
                debug_mode=debug_mode,
                performance_mode=performance_mode,
            )
        elif agent_id == AgentType.RFQ_VALIDATOR:
            # RFQ agents need config - use empty config as fallback
            validator_config: dict[str, Any] = {}  # TODO: Load proper config for agent operator
            return get_rfq_validator(
                config=validator_config,
                model_id=model_id,
                user_id=user_id,
                session_id=session_id,
                debug_mode=debug_mode,
            )
        elif agent_id == AgentType.RFQ_NORMALIZER:
            # RFQ agents need config - use empty config as fallback
            normalizer_config: dict[str, Any] = {}  # TODO: Load proper config for agent operator
            return get_rfq_normalizer(
                config=normalizer_config,
                model_id=model_id,
                user_id=user_id,
                session_id=session_id,
                debug_mode=debug_mode,
            )
        elif agent_id == AgentType.RFQ_FORMATTER:
            # RFQ agents need config - use empty config as fallback
            formatter_config: dict[str, Any] = {}  # TODO: Load proper config for agent operator
            return get_rfq_normalizer(
                config=formatter_config,
                model_id=model_id,
                user_id=user_id,
                session_id=session_id,
                debug_mode=debug_mode,
            )
        else:
            # Default to Scholar agent
            return get_scholar(
                model_id=model_id,
                user_id=user_id,
                session_id=session_id,
                debug_mode=debug_mode,
            )
    except Exception as e:
        logger.error(f"Failed to create agent {agent_id}: {e}")
        raise


def validate_agent_id(agent_id: str | AgentType) -> AgentType:
    """
    Validate and convert agent ID to AgentType.

    Args:
        agent_id: Agent identifier as string or AgentType enum

    Returns:
        AgentType: Validated agent type

    Raises:
        AgentNotFoundError: If agent_id is invalid

    Example:
        >>> agent_type = validate_agent_id("rfq_processor")
        >>> print(agent_type)
        <AgentType.RFQ_PROCESSOR: 'rfq_processor'>
    """
    if isinstance(agent_id, AgentType):
        return agent_id

    if isinstance(agent_id, str):
        try:
            return AgentType(agent_id)
        except ValueError as e:
            available = ", ".join(get_available_agents())
            raise AgentNotFoundError(f"Unknown agent type '{agent_id}'. Available agents: {available}") from e

    raise AgentNotFoundError(f"Invalid agent_id type: {type(agent_id).__name__}. Expected string or AgentType enum")
