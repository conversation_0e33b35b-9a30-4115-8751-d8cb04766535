"""RFQ Normalizer Agent for validating and standardizing numerical values and units.

This module provides the normalizer agent that processes validation output
to validate, standardize, and correct all numerical values and units in
steel material specifications. Ensures consistency between extracted values
and original text while converting measurements to standard units.
"""

import json
from textwrap import dedent
from typing import Any

from agents.base import BaseRFQAgent
from utils.exceptions import RFQProcessingError
from utils.log import get_app_logger
from utils.rfq.config import get_default_client_id
from utils.rfq.schemas.numerical_schema import NORMALIZER_AGENT_OUTPUT_SCHEMA

logger = get_app_logger()


class RFQNormalizerAgent(BaseRFQAgent):
    """
    RFQ Normalization Agent for standardizing numerical values and units.

    This agent performs systematic numerical validation and standardization:
    1. Unit Standardization: Converts all measurements to standard units (kg, mm, N/mm²)
    2. Numerical Validation: Validates values against realistic ranges
    3. Consistency Checks: Ensures min ≤ max relationships and logical consistency
    4. Text Verification: Cross-references values with original email text
    """

    def __init__(
        self,
        config: dict[str, Any],
        model_id: str = "google/gemini-flash-1.5",
        user_id: str | None = None,
        session_id: str | None = None,
        debug_mode: bool = False,
        performance_mode: bool = False,
    ):
        """
        Initialize the RFQ Normalization Agent.

        Args:
            config: Agent configuration dictionary
            model_id: LLM model ID to use
            user_id: Optional user/client ID for client-specific normalization rules
            session_id: Optional session ID for conversation tracking
            debug_mode: Enable debug logging and detailed error messages
            performance_mode: Enable performance optimizations
        """
        # Store config for validation
        self._validate_config(config)
        self._config = config

        # Initialize base agent
        super().__init__(
            agent_type="normalizer",
            model_id=model_id,
            user_id=user_id,
            session_id=session_id,
            debug_mode=debug_mode,
            performance_mode=performance_mode,
        )

    def _validate_config(self, config: dict[str, Any]):
        """
        Validate the provided configuration.

        Args:
            config: Configuration to validate

        Raises:
            RFQProcessingError: If configuration is invalid
        """
        try:
            # Check for required normalizer output schema
            if "normalizer_output_schema" not in config:
                raise ValueError("Missing normalizer_output_schema in configuration")

            normalizer_schema = config["normalizer_output_schema"]

            # Handle None schema by providing a fallback
            if normalizer_schema is None:
                logger.warning("normalizer_output_schema is None, using fallback schema")
                config["normalizer_output_schema"] = NORMALIZER_AGENT_OUTPUT_SCHEMA
                normalizer_schema = NORMALIZER_AGENT_OUTPUT_SCHEMA

            # CRITICAL FIX: Ensure we always use the correct schema structure
            # The database might have old/incorrect schema, so force the correct one

            if not isinstance(normalizer_schema, dict) or "material_specs" not in normalizer_schema:
                logger.warning("normalizer_output_schema has incorrect structure, using correct schema")
                config["normalizer_output_schema"] = NORMALIZER_AGENT_OUTPUT_SCHEMA
                normalizer_schema = NORMALIZER_AGENT_OUTPUT_SCHEMA
            else:
                # Check if the schema has the correct structure for material_specs
                material_specs = normalizer_schema.get("material_specs")
                if material_specs and isinstance(material_specs, list) and len(material_specs) > 0:
                    sample_spec = material_specs[0]
                    has_normalized_fields = isinstance(sample_spec, dict) and "normalized_numerical_fields" in sample_spec
                    if not has_normalized_fields:
                        logger.warning("normalizer_output_schema missing normalized_numerical_fields, using correct schema")
                        config["normalizer_output_schema"] = NORMALIZER_AGENT_OUTPUT_SCHEMA
                        normalizer_schema = NORMALIZER_AGENT_OUTPUT_SCHEMA

            if not isinstance(normalizer_schema, dict) or "material_specs" not in normalizer_schema:
                raise ValueError("Invalid normalizer_output_schema structure - missing material_specs")

            logger.info("Successfully validated normalizer configuration")

        except Exception as e:
            logger.error(f"Configuration validation failed: {e}")
            raise RFQProcessingError("Invalid normalization agent configuration", details={"error": str(e)}) from e

    def _get_agent_name(self) -> str:
        """Get the display name for this agent."""
        return "RFQ Numerical Normalizer"

    def _get_agent_description(self) -> str:
        """Get the description for this agent."""
        return dedent("""
            You are an expert steel RFQ numerical normalization specialist. You validate and standardize
            all numerical values and units in steel material specifications. You ensure consistency
            between extracted values and original text while converting all measurements to standard
            units (kg for weight, mm for dimensions, N/mm² for strength). You perform systematic
            validation of ranges, relationships, and cross-reference with original email text.
        """).strip()

    def _get_agent_instructions(self) -> str:
        """
        Get the instructions for this agent.

        Returns:
            Agent-specific instructions
        """
        try:
            has_instruction = self.client_flags.get("has_instruction", False)

            if has_instruction:
                # Use client-specific instructions
                logger.info("Generating client-specific normalizer instructions")
                normalization_instructions = self.db_tools.generate_dynamic_instructions(config_json=self._config, instruction_type="normalizer")
                logger.info("Using client-specific normalizer instructions")
            else:
                # Use generic instructions
                logger.info("Generating generic normalizer instructions")
                generic_config = self.db_tools.get_conditional_client_configuration(get_default_client_id())
                normalization_instructions = self.db_tools.generate_dynamic_instructions(config_json=generic_config, instruction_type="normalizer")
                logger.info("Using generic normalizer instructions")

            if not normalization_instructions:
                raise ValueError("Generated normalization instructions are empty")

            logger.info("✅ Added dynamic normalization output schema to instructions")
            return normalization_instructions

        except Exception as e:
            logger.warning(f"Failed to load dynamic normalization instructions, using simplified version: {e}")

            # Simplified fallback instructions
            normalization_output_schema = self._config.get("normalizer_output_schema", {})

            # Extract key schema components for better instructions
            schema_keys = list(normalization_output_schema.keys()) if isinstance(normalization_output_schema, dict) else []

            # Build dynamic requirements based on schema structure
            schema_requirements = []
            if "normalization_summary" in schema_keys:
                schema_requirements.append("Include normalization_summary with overall_valid, corrections_made, warnings_issued")
            if "corrected_rfq_data" in schema_keys:
                schema_requirements.append("Include corrected_rfq_data with normalized material specifications")
            if "conflicts_resolved" in schema_keys:
                schema_requirements.append("Document all changes in conflicts_resolved array")
            if "normalization_metadata" in schema_keys:
                schema_requirements.append("Include normalization_metadata with units_used and conversion_summary")

            requirements_text = (
                "\n".join([f"- {req}" for req in schema_requirements]) if schema_requirements else "- Follow the exact schema structure provided"
            )

            schema_str = json.dumps(normalization_output_schema, indent=2) if normalization_output_schema else "Schema not available"

            normalization_instructions = f"""You are an expert steel RFQ numerical normalization specialist.

## TASK
Normalize and standardize all numerical values in the validation output by:
1. Converting all units to standard format (kg, mm, N/mm²)
2. Validating numerical ranges and relationships
3. Cross-referencing with original email text
4. Documenting all corrections and warnings

## REQUIREMENTS
- Convert weight to kg, dimensions to mm, strength to N/mm²
- Ensure min ≤ max for all paired values
- Warn about out-of-range values but include them
- Document all changes in conflicts_resolved

## OUTPUT SCHEMA REQUIREMENTS
Your response MUST include these components based on the loaded schema:
{requirements_text}

## OUTPUT SCHEMA
You MUST format your response according to this exact JSON schema:
```json
{schema_str}
```

CRITICAL: Return ONLY valid JSON following this schema, no explanatory text, no markdown blocks.
"""
            return normalization_instructions

    def _get_agent_goal(self) -> str:
        """Get the goal for this agent."""
        return (
            "Normalize and standardize all numerical values in steel RFQ specifications through "
            "systematic unit conversion, range validation, consistency checking, and text verification. "
            "Return corrected specifications using the dynamically loaded schema format."
        )

    def _get_default_temperature(self) -> float:
        """Get default temperature for normalization agent."""
        return 0.0  # Low temperature for numerical accuracy

    def _get_default_max_tokens(self) -> int:
        """Get default max tokens for normalization agent."""
        return 4096  # Moderate token limit for numerical processing


def get_rfq_normalizer(
    config: dict[str, Any],
    model_id: str = "google/gemini-flash-1.5",
    user_id: str | None = None,
    session_id: str | None = None,
    debug_mode: bool = False,
    performance_mode: bool = False,
) -> RFQNormalizerAgent:
    """
    Create and return the RFQ Normalization Agent.

    This agent performs systematic numerical validation and standardization:
    1. Unit Standardization: Converts all measurements to standard units (kg, mm, N/mm²)
    2. Numerical Validation: Validates values against realistic ranges
    3. Consistency Checks: Ensures min ≤ max relationships and logical consistency
    4. Text Verification: Cross-references values with original email text

    Args:
        config: Agent configuration dictionary
        model_id: The LLM model ID to use (e.g., "google/gemini-flash-1.5")
        user_id: Optional user/client ID for client-specific normalization rules
        session_id: Optional session ID for conversation tracking
        debug_mode: Enable debug logging and detailed error messages
        performance_mode: Enable performance optimizations (disables thinking tools)

    Returns:
        RFQNormalizerAgent: Configured RFQ normalization agent

    Raises:
        RFQProcessingError: If agent creation fails

    Example:
        >>> config = {"normalizer_output_schema": {"material_specs": []}}
        >>> agent = get_rfq_normalizer(config)
        >>> normalization_prompt = f'''
        ... ## ORIGINAL EMAIL TEXT:
        ... {original_email}
        ... ## VALIDATION OUTPUT TO NORMALIZE:
        ... {json.dumps(validation_output, indent=2)}
        ... Please normalize and standardize all numerical values...
        ... '''
        >>> result = await agent.arun(message=normalization_prompt)
    """
    return RFQNormalizerAgent(
        config=config,
        model_id=model_id,
        user_id=user_id,
        session_id=session_id,
        debug_mode=debug_mode,
        performance_mode=performance_mode,
    )
