[run]
source = api, db, utils, agents
omit =
    */tests/*
    */venv/*
    */.venv/*
    */migrations/*
    */__pycache__/*
    */conftest.py
    */test_*.py
    setup.py
    */site-packages/*
    .local/*
    .cache/*

[report]
# Regexes for lines to exclude from consideration
exclude_lines =
    # Have to re-enable the standard pragma
    pragma: no cover

    # Don't complain about missing debug-only code:
    def __repr__
    if self\.debug

    # Don't complain if tests don't hit defensive assertion code:
    raise AssertionError
    raise NotImplementedError

    # Don't complain if non-runnable code isn't run:
    if 0:
    if __name__ == .__main__.:

    # Don't complain about abstract methods
    @(abc\.)?abstractmethod

ignore_errors = True
show_missing = True
precision = 2
skip_covered = False

[html]
directory = htmlcov
title = Vanilla Steel AI Coverage Report

[xml]
output = coverage.xml
